import pyodbc

# إعدادات قاعدة البيانات الأصلية (Terra) - قراءة فقط
MAIN_DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

def connect_to_terra_db():
    """الاتصال بقاعدة البيانات الأصلية"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={MAIN_DB_CONFIG['server']};DATABASE={MAIN_DB_CONFIG['database']};UID={MAIN_DB_CONFIG['user']};PWD={MAIN_DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        print("✅ تم الاتصال بقاعدة البيانات الأصلية بنجاح")
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات الأصلية: {e}")
        return None

def check_current_status():
    """فحص الوضع الحالي"""
    print("🔍 فحص الوضع الحالي...")
    
    conn = connect_to_terra_db()
    if not conn:
        return
    
    cursor = conn.cursor()
    
    # فحص العميل 4255
    print("\n1️⃣ فحص العميل 4255:")
    cursor.execute("""
        SELECT CustomerCode, NameAr, BranchId, IsDeleted, AddDate
        FROM Acc_Customers
        WHERE CustomerCode = 4255
    """)
    
    customer = cursor.fetchone()
    if customer:
        print(f"✅ العميل موجود:")
        print(f"   - كود العميل: {customer[0]}")
        print(f"   - اسم العميل: {customer[1]}")
        print(f"   - معرف الفرع: {customer[2]}")
        print(f"   - محذوف؟: {customer[3]}")
        print(f"   - تاريخ الإضافة: {customer[4]}")
    else:
        print("❌ العميل غير موجود!")
    
    # فحص المعاينة 1409 مع العميل
    print("\n2️⃣ فحص المعاينة 1409 مع العميل:")
    cursor.execute("""
        SELECT p.PreviewId, p.PreviewCode, c.NameAr, c.BranchId, p.AddDate, u.UserName
        FROM Sys_Previews p
        LEFT JOIN Acc_Customers c ON p.CustomerId = c.CustomerCode
        LEFT JOIN Sys_Users u ON p.AddUser = u.UserId
        WHERE p.PreviewId = 1409 AND p.IsDeleted = 0
    """)
    
    result = cursor.fetchone()
    if result:
        print(f"✅ المعاينة مع العميل:")
        print(f"   - معرف المعاينة: {result[0]}")
        print(f"   - كود المعاينة: {result[1]}")
        print(f"   - اسم العميل: {result[2]}")
        print(f"   - معرف الفرع: {result[3]}")
        print(f"   - تاريخ الإضافة: {result[4]}")
        print(f"   - أضيف بواسطة: {result[5]}")
    else:
        print("❌ المعاينة غير موجودة أو مش مربوطة بعميل!")
    
    # فحص الاستعلام الكامل اللي النظام بيستخدمه
    print("\n3️⃣ فحص الاستعلام الكامل للفرع 2:")
    cursor.execute("""
        SELECT p.PreviewId, p.PreviewCode, c.NameAr, c.BranchId, p.AddDate, u.UserName
        FROM Sys_Previews p
        INNER JOIN Acc_Customers c ON p.CustomerId = c.CustomerCode
        LEFT JOIN Sys_Users u ON p.AddUser = u.UserId
        WHERE p.IsDeleted = 0 AND c.IsDeleted = 0 AND c.BranchId = 2 AND p.PreviewId > 1408
        ORDER BY p.PreviewId ASC
    """)
    
    results = cursor.fetchall()
    if results:
        print(f"✅ وُجد {len(results)} معاينة جديدة للفرع 2:")
        for row in results:
            print(f"   - معاينة {row[0]} - كود {row[1]} - {row[2]} - فرع {row[3]} - {row[4]}")
    else:
        print("❌ لا توجد معاينات جديدة للفرع 2")

    conn.close()

if __name__ == "__main__":
    check_current_status()
