#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إشعارات محسن مع تفاصيل شاملة من جميع الجداول المرتبطة
قراءة فقط - لا يكتب أي بيانات في قاعدة البيانات
"""

    #'server': 'hej08pxktvw.sn.mynetname.net',
    #'server': 'localhost',

import asyncio
import pyodbc
import os
import logging
from datetime import datetime
from telegram import Bot

# استيراد الإعدادات - بيانات ثابتة في الكود
print("✅ استخدام الإعدادات الثابتة في الكود")

# إعدادات ثابتة في الكود
BOT_TOKEN = "7664606990:AAFbBWGShtg00af-qkbCm9VRpvqq7M--bU0"

DEFAULT_NOTIFICATION_CHAT_IDS = [
    1107000748,      # مستخدم 1
    1206533289,      # مستخدم 2
    -1002255521584,  # جروب TERRABOT
    -1002308493862   # الجروب الرئيسي
]

# إعدادات قاعدة البيانات الثابتة
MAIN_DB_CONFIG = {
    'server': 'localhost',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

NOTIFICATIONS_DB_CONFIG = {
    'server': 'localhost',
    'database': 'Terra_Notifications_DB1',
    'user': 'sa',
    'password': 'Ret_ME@'
}

print("✅ تم تحميل الإعدادات الثابتة")
print(f"🤖 توكن البوت: {BOT_TOKEN[:20]}...")
print(f"🖥️ خادم قاعدة البيانات: {MAIN_DB_CONFIG['server']}")
print(f"👤 المستخدم: {MAIN_DB_CONFIG['user']}")

def get_active_recipients(branch_id=None):
    """الحصول على قائمة المستقبلين النشطين من قاعدة بيانات الإشعارات حسب الفرع"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            print("⚠️ لا يمكن الاتصال بقاعدة بيانات الإشعارات - استخدام القائمة الافتراضية")
            if logger:
                logger.warning("لا يمكن الاتصال بقاعدة بيانات الإشعارات - استخدام القائمة الافتراضية")
            return DEFAULT_NOTIFICATION_CHAT_IDS

        cursor = conn.cursor()

        # بناء الاستعلام حسب الفرع
        if branch_id == 2:  # فرع مدينة نصر
            query = """
                SELECT ChatId, RecipientName, RecipientType
                FROM Notification_Recipients
                WHERE IsActive = 1 AND ReceiveNasrBranch = 1
                ORDER BY RecipientType, RecipientName
            """
        elif branch_id == 3:  # فرع التجمع
            query = """
                SELECT ChatId, RecipientName, RecipientType
                FROM Notification_Recipients
                WHERE IsActive = 1 AND ReceiveTajammuBranch = 1
                ORDER BY RecipientType, RecipientName
            """
        else:  # جميع المستقبلين النشطين (للرسائل العامة)
            query = """
                SELECT ChatId, RecipientName, RecipientType
                FROM Notification_Recipients
                WHERE IsActive = 1
                ORDER BY RecipientType, RecipientName
            """

        cursor.execute(query)
        results = cursor.fetchall()
        conn.close()

        if results:
            chat_ids = [row[0] for row in results]
            branch_text = ""
            if branch_id == 2:
                branch_text = " (فرع مدينة نصر)"
            elif branch_id == 3:
                branch_text = " (فرع التجمع)"

            print(f"📋 تم تحميل {len(chat_ids)} مستقبل نشط من قاعدة البيانات{branch_text}:")
            for row in results:
                recipient_type = "👤 مستخدم" if row[2] == 'User' else "👥 جروب"
                print(f"   {recipient_type}: {row[1]} ({row[0]})")

            if logger:
                logger.info(f"📋 تم تحميل {len(chat_ids)} مستقبل نشط من قاعدة البيانات{branch_text}")

            return chat_ids
        else:
            print(f"⚠️ لا توجد مستقبلين نشطين في قاعدة البيانات للفرع {branch_id} - استخدام القائمة الافتراضية")
            if logger:
                logger.warning(f"لا توجد مستقبلين نشطين في قاعدة البيانات للفرع {branch_id} - استخدام القائمة الافتراضية")
            return DEFAULT_NOTIFICATION_CHAT_IDS

    except Exception as e:
        error_msg = f"❌ خطأ في تحميل المستقبلين من قاعدة البيانات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return DEFAULT_NOTIFICATION_CHAT_IDS

# تم نقل إعدادات قاعدة البيانات إلى ملف config.py

# تم حذف المتغيرات غير المستخدمة

# ملف Log لحفظ الأخطاء والأحداث
LOG_FILE = "terra_bot.log"

# إعداد نظام Logging
def setup_logging():
    """إعداد نظام logging لحفظ الأخطاء والأحداث"""
    try:
        # إنشاء logger
        logger = logging.getLogger('TerraBot')
        logger.setLevel(logging.DEBUG)

        # إزالة handlers القديمة إذا وجدت
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # إنشاء formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # إنشاء file handler
        file_handler = logging.FileHandler(LOG_FILE, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)

        # إنشاء console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(console_formatter)

        # إضافة handlers للlogger
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        return logger

    except Exception as e:
        print(f"❌ خطأ في إعداد نظام Logging: {str(e)}")
        return None

# إنشاء logger عام
logger = setup_logging()

def connect_to_main_db():
    """الاتصال بقاعدة البيانات الأصلية (Terra) - قراءة فقط"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={MAIN_DB_CONFIG['server']};DATABASE={MAIN_DB_CONFIG['database']};UID={MAIN_DB_CONFIG['user']};PWD={MAIN_DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        if logger:
            logger.info("✅ تم الاتصال بقاعدة البيانات الأصلية بنجاح")
        return conn
    except Exception as e:
        error_msg = f"❌ خطأ في الاتصال بقاعدة البيانات الأصلية: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

def connect_to_notifications_db():
    """الاتصال بقاعدة بيانات الإشعارات (Terra_Notifications_DB1) - قراءة وكتابة"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={NOTIFICATIONS_DB_CONFIG['server']};DATABASE={NOTIFICATIONS_DB_CONFIG['database']};UID={NOTIFICATIONS_DB_CONFIG['user']};PWD={NOTIFICATIONS_DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        if logger:
            logger.info("✅ تم الاتصال بقاعدة بيانات الإشعارات بنجاح")
        return conn
    except Exception as e:
        error_msg = f"❌ خطأ في الاتصال بقاعدة بيانات الإشعارات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

# للتوافق مع الكود القديم
def connect_to_db():
    """الاتصال بقاعدة البيانات الأصلية - للتوافق مع الكود القديم"""
    return connect_to_main_db()

def init_notifications_db():
    """التحقق من وجود جداول قاعدة بيانات الإشعارات وإنشاؤها إذا لزم الأمر"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return False

        cursor = conn.cursor()

        # فحص وجود جدول سجل الإشعارات
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Notifications_Log' AND xtype='U')
            BEGIN
                CREATE TABLE Notifications_Log (
                    LogId INT IDENTITY(1,1) PRIMARY KEY,
                    DataType NVARCHAR(50) NOT NULL,
                    RecordId INT NOT NULL,
                    RecordName NVARCHAR(255),
                    BranchId INT,
                    BranchName NVARCHAR(100),
                    RecordUniqueId INT,
                    AddDate DATETIME,
                    AddedBy NVARCHAR(100),
                    NotificationDate DATETIME DEFAULT GETDATE(),
                    NotificationStatus NVARCHAR(20) DEFAULT N'تم الإرسال',
                    IsActive BIT DEFAULT 1,
                    Notes NVARCHAR(500),
                    CONSTRAINT UK_Notifications_DataType_RecordId_Branch UNIQUE(DataType, RecordId, BranchId)
                );

                CREATE INDEX IX_Notifications_DataType ON Notifications_Log(DataType);
                CREATE INDEX IX_Notifications_RecordId ON Notifications_Log(RecordId);
                CREATE INDEX IX_Notifications_Date ON Notifications_Log(NotificationDate);
                CREATE INDEX IX_Notifications_Branch ON Notifications_Log(BranchId);
            END

            -- إضافة الأعمدة الجديدة إذا لم تكن موجودة
            IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Notifications_Log' AND COLUMN_NAME = 'BranchId')
            BEGIN
                ALTER TABLE Notifications_Log ADD BranchId INT;
            END

            IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Notifications_Log' AND COLUMN_NAME = 'RecordUniqueId')
            BEGIN
                ALTER TABLE Notifications_Log ADD RecordUniqueId INT;
            END
        """)

        # فحص وجود جدول المستقبلين
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Notification_Recipients' AND xtype='U')
            BEGIN
                CREATE TABLE Notification_Recipients (
                    RecipientId INT IDENTITY(1,1) PRIMARY KEY,
                    ChatId BIGINT NOT NULL UNIQUE,
                    RecipientName NVARCHAR(100) NOT NULL,
                    RecipientType NVARCHAR(20) NOT NULL CHECK (RecipientType IN ('User', 'Group')),
                    IsActive BIT DEFAULT 1,
                    AddDate DATETIME DEFAULT GETDATE(),
                    Notes NVARCHAR(500),
                    ReceiveNasrBranch BIT DEFAULT 1,
                    ReceiveTajammuBranch BIT DEFAULT 1,
                    ReceiveReports BIT DEFAULT 1
                );

                CREATE INDEX IX_Recipients_ChatId ON Notification_Recipients(ChatId);
                CREATE INDEX IX_Recipients_Active ON Notification_Recipients(IsActive);

                -- إدراج المستقبلين الافتراضيين من ملف الإعدادات
                -- سيتم إدراجهم تلقائٍ عند أول تشغيل إذا لم يكونوا موجودين
            END
        """)

        # فحص وجود جدول التقارير المجدولة
        print("🔄 فحص جدول التقارير المجدولة...")
        try:
            cursor.execute("""
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Scheduled_Reports' AND xtype='U')
                BEGIN
                    CREATE TABLE Scheduled_Reports (
                        ReportId INT IDENTITY(1,1) PRIMARY KEY,
                        ReportName NVARCHAR(100) NOT NULL,
                        ReportType NVARCHAR(50) NOT NULL,
                        ScheduleType NVARCHAR(20) NOT NULL,
                        IntervalValue INT NOT NULL DEFAULT 1,
                        DaysToInclude INT NOT NULL DEFAULT 1,
                        LastRunTime DATETIME NULL,
                        NextRunTime DATETIME NOT NULL,
                        IsActive BIT DEFAULT 1,
                        BranchIds NVARCHAR(100) NULL,
                        AddDate DATETIME DEFAULT GETDATE(),
                        Notes NVARCHAR(500)
                    );
                END
            """)
            conn.commit()
            print("✅ تم إنشاء جدول Scheduled_Reports")

            # إنشاء الفهارس
            cursor.execute("""
                IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ScheduledReports_NextRun')
                BEGIN
                    CREATE INDEX IX_ScheduledReports_NextRun ON Scheduled_Reports(NextRunTime, IsActive);
                END
            """)

            cursor.execute("""
                IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ScheduledReports_Type')
                BEGIN
                    CREATE INDEX IX_ScheduledReports_Type ON Scheduled_Reports(ReportType, IsActive);
                END
            """)
            conn.commit()
            print("✅ تم إنشاء فهارس جدول التقارير")

            # إدراج التقارير الافتراضية
            cursor.execute("SELECT COUNT(*) FROM Scheduled_Reports")
            count = cursor.fetchone()[0]
            if count == 0:
                cursor.execute("""
                    INSERT INTO Scheduled_Reports (ReportName, ReportType, ScheduleType, IntervalValue, DaysToInclude, NextRunTime, BranchIds, Notes)
                    VALUES
                        (N'تقرير إحصائي يومي', 'statistics', 'daily', 1, 1, DATEADD(HOUR, 1, GETDATE()), NULL, N'تقرير يومي شامل لجميع الفروع'),
                        (N'تقرير إحصائي أسبوعي', 'statistics', 'weekly', 1, 7, DATEADD(DAY, 1, GETDATE()), NULL, N'تقرير أسبوعي شامل لجميع الفروع'),
                        (N'تقرير فرع مدينة نصر', 'statistics', 'daily', 1, 1, DATEADD(HOUR, 2, GETDATE()), '2', N'تقرير يومي لفرع مدينة نصر فقط'),
                        (N'تقرير فرع التجمع', 'statistics', 'daily', 1, 1, DATEADD(HOUR, 2, GETDATE()), '3', N'تقرير يومي لفرع التجمع فقط')
                """)
                conn.commit()
                print("✅ تم إدراج التقارير الافتراضية")
            else:
                print(f"ℹ️ يوجد {count} تقرير مجدول بالفعل")

        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول التقارير: {str(e)}")
            if logger:
                logger.error(f"خطأ في إنشاء جدول التقارير: {str(e)}")

        # فحص وجود جدول إعدادات الفروع للتقارير
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Branch_Report_Settings' AND xtype='U')
            BEGIN
                CREATE TABLE Branch_Report_Settings (
                    SettingId INT IDENTITY(1,1) PRIMARY KEY,
                    BranchId INT NOT NULL,
                    BranchName NVARCHAR(100) NOT NULL,
                    IsActiveForReports BIT DEFAULT 1,
                    ReceiveDailyReports BIT DEFAULT 1,
                    ReceiveWeeklyReports BIT DEFAULT 1,
                    ReceiveMonthlyReports BIT DEFAULT 1,
                    ReportRecipients NVARCHAR(500) NULL, -- معرفات المستقبلين مفصولة بفاصلة (NULL = جميع المستقبلين)
                    AddDate DATETIME DEFAULT GETDATE(),
                    UpdateDate DATETIME NULL,
                    Notes NVARCHAR(500)
                );

                CREATE INDEX IX_BranchReportSettings_Branch ON Branch_Report_Settings(BranchId, IsActiveForReports);

                -- إدراج إعدادات افتراضية للفروع
                INSERT INTO Branch_Report_Settings (BranchId, BranchName, IsActiveForReports, Notes)
                VALUES
                    (2, N'مدينة نصر', 1, N'فرع مدينة نصر - نشط للتقارير'),
                    (3, N'التجمع', 1, N'فرع التجمع - نشط للتقارير');
            END
        """)

        conn.commit()

        # التحقق من وجود الجداول الجديدة
        print("🔍 التحقق من الجداول الجديدة...")
        try:
            # فحص جدول التقارير المجدولة
            cursor.execute("SELECT COUNT(*) FROM Scheduled_Reports")
            reports_count = cursor.fetchone()[0]
            print(f"📊 جدول التقارير المجدولة: {reports_count} تقرير")

            # فحص جدول إعدادات الفروع
            cursor.execute("SELECT COUNT(*) FROM Branch_Report_Settings")
            settings_count = cursor.fetchone()[0]
            print(f"🏢 جدول إعدادات الفروع: {settings_count} إعداد")

            # عرض التقارير المجدولة
            cursor.execute("SELECT ReportName, ScheduleType, IsActive FROM Scheduled_Reports")
            reports = cursor.fetchall()
            print("📋 التقارير المجدولة:")
            for report in reports:
                status = "✅ نشط" if report[2] else "❌ معطل"
                print(f"   - {report[0]} ({report[1]}) - {status}")

        except Exception as e:
            print(f"⚠️ تحذير: خطأ في فحص الجداول الجديدة: {str(e)}")

        # إدراج المستقبلين الافتراضيين من ملف الإعدادات إذا لم يكونوا موجودين
        try:
            for chat_id in DEFAULT_NOTIFICATION_CHAT_IDS:
                cursor.execute("""
                    IF NOT EXISTS (SELECT 1 FROM Notification_Recipients WHERE ChatId = ?)
                    BEGIN
                        INSERT INTO Notification_Recipients (ChatId, RecipientName, RecipientType, IsActive)
                        VALUES (?, N'مستقبل افتراضي', CASE WHEN ? < 0 THEN 'Group' ELSE 'User' END, 1)
                    END
                """, (chat_id, chat_id, chat_id))
            conn.commit()
        except Exception as e:
            print(f"⚠️ تحذير: لم يتم إدراج المستقبلين الافتراضيين: {str(e)}")

        conn.close()

        success_msg = "✅ تم التحقق من جداول قاعدة بيانات الإشعارات والتقارير المجدولة"
        print(success_msg)
        if logger:
            logger.info(success_msg)
        return True

    except Exception as e:
        error_msg = f"❌ خطأ في إنشاء جداول قاعدة بيانات الإشعارات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return False

def save_notification_to_db(data_type, code, name, branch_id, branch_name, unique_id, add_date, added_by):
    """حفظ بيانات الإشعار في قاعدة بيانات الإشعارات"""
    try:
        print(f"🔄 محاولة حفظ {data_type} - كود {code} - معرف فريد {unique_id}")

        conn = connect_to_notifications_db()
        if not conn:
            print("❌ فشل الاتصال بقاعدة بيانات الإشعارات")
            return False

        cursor = conn.cursor()

        # التحقق أولاً من وجود السجل لتجنب التكرار
        print(f"🔍 فحص وجود السجل: {data_type} - معرف فريد {unique_id}")
        cursor.execute("""
            SELECT COUNT(*) FROM Notifications_Log
            WHERE DataType = ? AND RecordUniqueId = ?
        """, (data_type, unique_id))

        result = cursor.fetchone()
        if result and result[0] > 0:
            # السجل موجود بالفعل
            print(f"⚠️ السجل موجود بالفعل: {data_type} - معرف فريد {unique_id}")
            conn.close()
            return True

        # إدراج السجل الجديد
        print(f"💾 إدراج سجل جديد: {data_type} - كود {code}")
        cursor.execute("""
            INSERT INTO Notifications_Log
            (DataType, RecordId, RecordName, BranchId, BranchName, RecordUniqueId, AddDate, AddedBy, NotificationDate, NotificationStatus)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, GETDATE(), N'تم الإرسال')
        """, (data_type, code, name, branch_id, branch_name, unique_id, add_date, added_by))

        conn.commit()
        conn.close()

        success_msg = f"✅ تم حفظ {data_type} - كود {code} - معرف فريد {unique_id} في قاعدة بيانات الإشعارات"
        print(success_msg)
        if logger:
            logger.info(success_msg)
        return True

    except Exception as e:
        # تجاهل أخطاء UNIQUE KEY constraint لأنها تعني أن السجل موجود بالفعل
        if "UNIQUE KEY constraint" in str(e) or "2627" in str(e):
            print(f"⚠️ السجل موجود بالفعل (UNIQUE constraint): {data_type} - معرف فريد {unique_id}")
            return True

        error_msg = f"❌ خطأ في حفظ البيانات في قاعدة بيانات الإشعارات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return False





# تم حذف دالة load_last_ids_from_db() غير المستخدمة

def get_statistics_report(days=1, branch_ids=None):
    """الحصول على التقرير الإحصائي الشامل"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # تحديد فلتر الفروع
        branch_filter = ""
        if branch_ids:
            if isinstance(branch_ids, str):
                branch_list = [x.strip() for x in branch_ids.split(',') if x.strip()]
                if branch_list:
                    branch_filter = f" AND BranchId IN ({','.join(branch_list)})"
            elif isinstance(branch_ids, list):
                if branch_ids:
                    branch_filter = f" AND BranchId IN ({','.join(map(str, branch_ids))})"

        # استعلام التقرير الإحصائي الشامل
        query = f"""
        DECLARE @Days INT = ?;

        -- العملاء
        WITH CustomersSummary AS (
            SELECT BranchId, COUNT(*) AS CustomersCount
            FROM Acc_Customers
            WHERE AddDate >= DATEADD(DAY, -@Days, CAST(GETDATE() AS DATE))
              AND AddDate < DATEADD(DAY, 1, CAST(GETDATE() AS DATE))
              AND IsDeleted = 0{branch_filter}
            GROUP BY BranchId
        ),
        -- المعاينات
        PreviewsSummary AS (
            SELECT
                BranchId,
                COUNT(*) AS PreviewsCount,
                SUM(ISNULL(TotalValue, 0)) AS PreviewsTotalValue,
                COUNT(CASE WHEN ISNULL(TotalValue, 0) > 0 THEN 1 END) AS PreviewsPaidCount,
                COUNT(CASE WHEN ISNULL(TotalValue, 0) = 0 THEN 1 END) AS PreviewsFreeCount
            FROM Sys_Previews
            WHERE AddDate >= DATEADD(DAY, -@Days, CAST(GETDATE() AS DATE))
              AND AddDate < DATEADD(DAY, 1, CAST(GETDATE() AS DATE))
              AND IsDeleted = 0{branch_filter}
            GROUP BY BranchId
        ),
        -- الاجتماعات
        MeetingsSummary AS (
            SELECT BranchId, COUNT(*) AS MeetingsCount
            FROM Sys_Meetings
            WHERE AddDate >= DATEADD(DAY, -@Days, CAST(GETDATE() AS DATE))
              AND AddDate < DATEADD(DAY, 1, CAST(GETDATE() AS DATE))
              AND IsDeleted = 0{branch_filter}
            GROUP BY BranchId
        ),
        -- التصميمات
        DesignsSummary AS (
            SELECT BranchId, COUNT(*) AS DesignsCount
            FROM Sys_Designs
            WHERE AddDate >= DATEADD(DAY, -@Days, CAST(GETDATE() AS DATE))
              AND AddDate < DATEADD(DAY, 1, CAST(GETDATE() AS DATE))
              AND IsDeleted = 0{branch_filter}
            GROUP BY BranchId
        ),
        -- العقود
        ContractsSummary AS (
            SELECT 
                BranchId,
                COUNT(*) AS ContractsCount,
                SUM(ISNULL(TotalValue, 0) + ISNULL(DiscountValue, 0)) AS ContractsTotalValue,
                SUM(ISNULL(DiscountValue, 0)) AS ContractsTotalDiscount,
                SUM(ISNULL(TotalValue, 0)) AS ContractsNetTotal
            FROM Acc_Contracts
            WHERE AddDate >= DATEADD(DAY, -@Days, CAST(GETDATE() AS DATE))
              AND AddDate < DATEADD(DAY, 1, CAST(GETDATE() AS DATE))
              AND IsDeleted = 0
            GROUP BY BranchId
        ),
        OffersSummary AS (
            SELECT 
                BranchId,
                COUNT(*) AS OffersCount,
                SUM(ISNULL(TotalValue, 0) + ISNULL(DiscountValue, 0)) AS OffersTotalValue,
                SUM(ISNULL(DiscountValue, 0)) AS OffersTotalDiscount,
                SUM(ISNULL(TotalValue, 0)) AS OffersNetTotal
            FROM Acc_Offers
            WHERE AddDate >= DATEADD(DAY, -@Days, CAST(GETDATE() AS DATE))
              AND AddDate < DATEADD(DAY, 1, CAST(GETDATE() AS DATE))
              AND IsDeleted = 0
            GROUP BY BranchId
        )

        -- التجميع النهائي
        SELECT
            B.BranchId,
            B.NameAr AS BranchName,
            ISNULL(CS.CustomersCount, 0) AS CustomersCount,
            ISNULL(PS.PreviewsCount, 0) AS PreviewsCount,
            ISNULL(PS.PreviewsTotalValue, 0) AS PreviewsTotalValue,
            ISNULL(PS.PreviewsPaidCount, 0) AS PreviewsPaidCount,
            ISNULL(PS.PreviewsFreeCount, 0) AS PreviewsFreeCount,
            ISNULL(MS.MeetingsCount, 0) AS MeetingsCount,
            ISNULL(DS.DesignsCount, 0) AS DesignsCount,
            ISNULL(CTS.ContractsCount, 0) AS ContractsCount,
            ISNULL(CTS.ContractsTotalValue, 0) AS ContractsTotalValue,
            ISNULL(CTS.ContractsTotalDiscount, 0) AS ContractsTotalDiscount,
            ISNULL(CTS.ContractsNetTotal, 0) AS ContractsNetTotal,
            ISNULL(OS.OffersCount, 0) AS OffersCount,
            ISNULL(OS.OffersTotalValue, 0) AS OffersTotalValue,
            ISNULL(OS.OffersTotalDiscount, 0) AS OffersTotalDiscount,
            ISNULL(OS.OffersNetTotal, 0) AS OffersNetTotal

        FROM Sys_Branches B
        LEFT JOIN CustomersSummary CS ON B.BranchId = CS.BranchId
        LEFT JOIN PreviewsSummary PS ON B.BranchId = PS.BranchId
        LEFT JOIN MeetingsSummary MS ON B.BranchId = MS.BranchId
        LEFT JOIN DesignsSummary DS ON B.BranchId = DS.BranchId
        LEFT JOIN ContractsSummary CTS ON B.BranchId = CTS.BranchId
        LEFT JOIN OffersSummary OS ON B.BranchId = OS.BranchId

        WHERE B.IsDeleted = 0

        ORDER BY B.NameAr
        """

        cursor.execute(query, (days,))
        results = cursor.fetchall()
        conn.close()

        return results

    except Exception as e:
        print(f"❌ خطأ في الحصول على التقرير الإحصائي: {str(e)}")
        if logger:
            logger.error(f"خطأ في الحصول على التقرير الإحصائي: {str(e)}")
        return None

def format_statistics_report(data, days, report_name=None, branch_ids=None):
    """تنسيق التقرير الإحصائي للإرسال"""
    if not data:
        return "❌ لا توجد بيانات للعرض"

    # حساب الإجماليات
    total_customers = sum(row[2] for row in data)
    total_previews = sum(row[3] for row in data)
    total_previews_value = sum(row[4] for row in data)
    total_meetings = sum(row[7] for row in data)
    total_designs = sum(row[8] for row in data)
    total_contracts = sum(row[9] for row in data)
    total_contracts_value = sum(row[10] for row in data)
    total_offers = sum(row[13] for row in data)
    total_offers_value = sum(row[14] for row in data)

    # عنوان التقرير
    title = report_name or f"تقرير إحصائي شامل - آخر {days} أيام"

    # معلومات الفروع المحددة
    branch_info = ""
    if branch_ids:
        if isinstance(branch_ids, str):
            branch_list = [x.strip() for x in branch_ids.split(',') if x.strip()]
            branch_info = f"\n🏢 الفروع المحددة: {', '.join(branch_list)}"
        elif isinstance(branch_ids, list):
            branch_info = f"\n🏢 الفروع المحددة: {', '.join(map(str, branch_ids))}"
    else:
        branch_info = "\n🏢 جميع الفروع"

    # تنسيق التقرير
    report = f"""📊 **{title}**
📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}{branch_info}

═══════════════════════════════════

📈 **الإجماليات العامة:**
👥 العملاء الجدد: {total_customers:,}
👁️ المعاينات: {total_previews:,} (قيمة: {total_previews_value:,.0f} جنيه)
🤝 الاجتماعات: {total_meetings:,}
🎨 التصميمات: {total_designs:,}
📋 العقود: {total_contracts:,} (قيمة: {total_contracts_value:,.0f} جنيه)
💼 العروض: {total_offers:,} (قيمة: {total_offers_value:,.0f} جنيه)

═══════════════════════════════════

🏢 **تفاصيل الفروع:**

"""

    for row in data:
        branch_name = row[1]
        customers = row[2]
        previews = row[3]
        previews_value = row[4]
        previews_paid = row[5]
        previews_free = row[6]
        meetings = row[7]
        designs = row[8]
        contracts = row[9]
        contracts_value = row[10]
        contracts_discount = row[11]
        contracts_net = row[12]
        offers = row[13]
        offers_value = row[14]
        offers_discount = row[15]
        offers_net = row[16]

        # تخطي الفروع التي لا تحتوي على أي بيانات
        if (customers + previews + meetings + designs + contracts + offers) == 0:
            continue

        report += f"""🏪 **{branch_name}**
├─ 👥 عملاء جدد: {customers:,}
├─ 👁 معاينات: {previews:,} (مدفوعة: {previews_paid}, مجانية: {previews_free})
│   💰 قيمة المعاينات: {previews_value:,.0f} جنيه
├─ 🤝 اجتماعات: {meetings:,}
├─ 🎨 تصميمات: {designs:,}
├─ 📋 عقود: {contracts:,}
│   💰 اجمالى العقود: {contracts_value:,.0f} جنيه
│   🏷 اجمالي خصم العقود: {contracts_discount:,.0f} جنيه
│   💵 صافي العقود: {contracts_net:,.0f} جنيه
└─ 💼 عدد طلب التسعير: {offers:,}
    💰 اجمالي طلب التسعير: {offers_value:,.0f} جنيه
    🏷 اجمالي خصومات طلب التسعير: {offers_discount:,.0f} جنيه
    💵 صافي طلبات التسعير: {offers_net:,.0f} جنيه

"""

    report += f"""
═══════════════════════════════════
⏰ تم إنشاء التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🤖 Terra Bot - نظام التقارير التلقائي"""

    return report

def send_custom_statistical_report(days_to_include, target_branches=None, report_name="التقرير الإحصائي المخصص"):
    """إرسال تقرير إحصائي مخصص"""
    try:
        # الحصول على بيانات التقرير الإحصائي
        data = get_statistics_report(days_to_include, target_branches)
        if data:
            # تنسيق التقرير مع اسم مخصص
            formatted_report = format_statistics_report(data, days_to_include, report_name, target_branches)

            # إرسال التقرير
            send_notification_sync(formatted_report)

            print(f"✅ تم إرسال {report_name} بنجاح")
            return True
        else:
            print(f"⚠️ لا توجد بيانات للتقرير: {report_name}")
            return False
    except Exception as e:
        print(f"❌ خطأ في إرسال التقرير {report_name}: {str(e)}")
        return False

def update_statistical_report_status(report_id):
    """تحديث حالة إرسال التقرير الإحصائي"""
    try:
        notifications_conn = connect_to_notifications_db()
        if not notifications_conn:
            return False

        cursor = notifications_conn.cursor()

        # تحديث حالة الإرسال والمواعيد
        cursor.execute("""
            UPDATE Statistical_Reports_Config
            SET SendStatus = N'تم الإرسال',
                LastSentTime = GETDATE(),
                NextSendTime = CASE
                    WHEN ScheduleType = 'daily' THEN DATEADD(DAY, IntervalDays, NextSendTime)
                    WHEN ScheduleType = 'weekly' THEN DATEADD(WEEK, IntervalDays, NextSendTime)
                    WHEN ScheduleType = 'monthly' THEN DATEADD(MONTH, IntervalDays, NextSendTime)
                    WHEN ScheduleType = 'hourly' THEN DATEADD(HOUR, IntervalDays, NextSendTime)
                    WHEN ScheduleType = 'custom' THEN DATEADD(MINUTE, IntervalDays, GETDATE())
                    ELSE DATEADD(DAY, IntervalDays, NextSendTime)
                END,
                UpdatedDate = GETDATE()
            WHERE ReportId = ?
        """, (report_id,))

        notifications_conn.commit()
        notifications_conn.close()

        print(f"✅ تم تحديث حالة التقرير {report_id}")
        return True

    except Exception as e:
        print(f"❌ خطأ في تحديث حالة التقرير {report_id}: {str(e)}")
        return False

# تم حذف دالة send_quarter_hourly_report() لأن QUARTER_HOURLY_STATS
# يستخدم الآن send_custom_statistical_report() مثل باقي التقارير

def check_scheduled_reports():
    """فحص التقارير المجدولة وإرسال التقارير المستحقة"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return

        cursor = conn.cursor()

        # التحقق من وجود الجداول المختلفة
        use_statistical_config = False
        use_new_structure = False

        # فحص جدول التقارير الإحصائية المخصصة
        try:
            cursor.execute("SELECT COUNT(*) FROM Statistical_Reports_Config WHERE 1=0")
            use_statistical_config = True
        except:
            pass

        # فحص الجداول الجديدة العامة
        if not use_statistical_config:
            try:
                cursor.execute("SELECT COUNT(*) FROM Report_Schedules WHERE 1=0")
                use_new_structure = True
            except:
                use_new_structure = False

        if use_statistical_config:
            # استخدام جدول التقارير الإحصائية المخصصة
            print("🔍 فحص جدول التقارير الإحصائية المخصصة...")

            # أولاً: عرض جميع التقارير للتشخيص
            cursor.execute("""
                SELECT
                    ReportId,
                    ReportName,
                    NextSendTime,
                    IsActive,
                    CASE WHEN NextSendTime <= GETDATE() THEN 'مستحق' ELSE 'ليس مستحق' END as Status
                FROM Statistical_Reports_Config
                ORDER BY NextSendTime
            """)

            all_reports = cursor.fetchall()
            print(f"📊 إجمالي التقارير: {len(all_reports)}")
            for report in all_reports:
                print(f"   {report[0]} - {report[1]} | {report[2]} | {'نشط' if report[3] else 'معطل'} | {report[4]}")

            # ثانياً: البحث عن التقارير المستحقة
            cursor.execute("""
                SELECT
                    ReportId,
                    ReportName,
                    ReportCode,
                    ScheduleType,
                    IntervalDays,
                    StatisticsPeriodDays,
                    BranchIds,
                    Notes,
                    ReportName
                FROM Statistical_Reports_Config
                WHERE IsActive = 1 AND NextSendTime <= GETDATE()
                ORDER BY NextSendTime
            """)
        elif use_new_structure:
            # استخدام الهيكل الجديد
            cursor.execute("""
                SELECT
                    s.ScheduleId,
                    s.ScheduleName,
                    t.ReportCode,
                    s.ScheduleType,
                    s.IntervalValue,
                    s.DaysToInclude,
                    s.BranchIds,
                    s.Notes,
                    t.ReportNameAr
                FROM Report_Schedules s
                INNER JOIN Report_Types t ON s.ReportTypeId = t.ReportTypeId
                WHERE s.IsActive = 1 AND s.NextRunTime <= GETDATE()
                ORDER BY s.NextRunTime
            """)
        else:
            # استخدام الهيكل القديم
            cursor.execute("""
                SELECT ReportId, ReportName, ReportType, ScheduleType, IntervalValue,
                       DaysToInclude, BranchIds, Notes, ReportName
                FROM Scheduled_Reports
                WHERE IsActive = 1 AND NextRunTime <= GETDATE()
                ORDER BY NextRunTime
            """)

        scheduled_reports = cursor.fetchall()
        print(f"⏰ التقارير المستحقة للإرسال: {len(scheduled_reports)}")

        for report in scheduled_reports:
            if use_statistical_config:
                # جدول التقارير الإحصائية المخصصة
                report_id = report[0]
                report_name = report[1]
                report_code = report[2]
                schedule_type = report[3]
                interval_value = report[4]
                days_to_include = report[5]
                branch_ids = report[6]
                # notes = report[7]  # متاح للاستخدام المستقبلي
                report_type_name = report[8]

                print(f"📊 تنفيذ التقرير المجدول: {report_name}")

                try:
                    # تحديد الفروع المطلوبة
                    if branch_ids:
                        target_branches = [int(x.strip()) for x in branch_ids.split(',') if x.strip().isdigit()]
                    else:
                        target_branches = None  # جميع الفروع

                    # تنفيذ التقرير حسب النوع
                    if report_code in ['CUSTOM_STATS_1', 'CUSTOM_STATS_5', 'WEEKLY_STATS', 'MONTHLY_STATS', 'QUARTER_HOURLY_STATS']:
                        send_custom_statistical_report(days_to_include, target_branches, report_name)
                    else:
                        print(f"⚠️ نوع تقرير غير معروف: {report_code}")
                        continue

                    # تحديث حالة الإرسال
                    update_statistical_report_status(report_id)

                    print(f"✅ تم إرسال التقرير: {report_name}")
                    if logger:
                        logger.info(f"تم إرسال التقرير المجدول: {report_name} (كود: {report_code})")

                except Exception as e:
                    error_msg = f"❌ خطأ في تنفيذ التقرير {report_name}: {str(e)}"
                    print(error_msg)
                    if logger:
                        logger.error(error_msg)

            elif use_new_structure:
                # الهيكل الجديد
                schedule_id = report[0]
                schedule_name = report[1]
                report_code = report[2]
                schedule_type = report[3]
                interval_value = report[4]
                days_to_include = report[5]
                branch_ids = report[6]
                notes = report[7]
                report_type_name = report[8]

                print(f"📊 تنفيذ التقرير المجدول: {schedule_name}")

                try:
                    # تنفيذ التقرير حسب الكود
                    if report_code in ['DAILY_STATS', 'WEEKLY_STATS', 'MONTHLY_STATS', 'CUSTOM_STATS', 'BRANCH_DAILY', 'PERFORMANCE_WEEKLY']:
                        # الحصول على بيانات التقرير الإحصائي
                        data = get_statistics_report(days_to_include, branch_ids)
                        if data:
                            # تنسيق التقرير مع اسم مخصص
                            custom_title = f"{report_type_name} - {schedule_name}"
                            formatted_report = format_statistics_report(data, days_to_include, custom_title, branch_ids)

                            # إرسال التقرير
                            send_notification_sync(formatted_report)

                            print(f"✅ تم إرسال التقرير: {schedule_name}")
                            if logger:
                                logger.info(f"تم إرسال التقرير المجدول: {schedule_name} (كود: {report_code})")
                        else:
                            print(f"⚠️ لا توجد بيانات للتقرير: {schedule_name}")
                    else:
                        print(f"⚠️ نوع تقرير غير مدعوم: {report_code}")

                    # تحديث موعد التشغيل التالي
                    update_next_run_time_new(schedule_id, schedule_type, interval_value)

                except Exception as e:
                    error_msg = f"❌ خطأ في تنفيذ التقرير {schedule_name}: {str(e)}"
                    print(error_msg)
                    if logger:
                        logger.error(error_msg)
            else:
                # الهيكل القديم
                report_id = report[0]
                report_name = report[1]
                report_type = report[2]
                schedule_type = report[3]
                interval_value = report[4]
                days_to_include = report[5]
                branch_ids = report[6]
                notes = report[7]

                print(f"📊 تنفيذ التقرير المجدول: {report_name}")

                try:
                    # تنفيذ التقرير حسب النوع
                    if report_type == 'statistics':
                        # الحصول على بيانات التقرير الإحصائي
                        data = get_statistics_report(days_to_include, branch_ids)
                        if data:
                            # تنسيق التقرير
                            formatted_report = format_statistics_report(data, days_to_include, report_name, branch_ids)

                            # إرسال التقرير
                            send_notification_sync(formatted_report)

                            print(f"✅ تم إرسال التقرير: {report_name}")
                            if logger:
                                logger.info(f"تم إرسال التقرير المجدول: {report_name}")
                        else:
                            print(f"⚠️ لا توجد بيانات للتقرير: {report_name}")

                    # تحديث موعد التشغيل التالي
                    update_next_run_time(report_id, schedule_type, interval_value)

                except Exception as e:
                    error_msg = f"❌ خطأ في تنفيذ التقرير {report_name}: {str(e)}"
                    print(error_msg)
                    if logger:
                        logger.error(error_msg)

        conn.close()

        if scheduled_reports:
            print(f"📊 تم فحص {len(scheduled_reports)} تقرير مجدول")

    except Exception as e:
        error_msg = f"❌ خطأ في فحص التقارير المجدولة: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)

def update_next_run_time(report_id, schedule_type, interval_value):
    """تحديث موعد التشغيل التالي للتقرير"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return

        cursor = conn.cursor()

        # حساب الموعد التالي حسب نوع الجدولة
        if schedule_type == 'hourly':
            next_run_sql = f"DATEADD(HOUR, {interval_value}, GETDATE())"
        elif schedule_type == 'daily':
            next_run_sql = f"DATEADD(DAY, {interval_value}, GETDATE())"
        elif schedule_type == 'weekly':
            next_run_sql = f"DATEADD(WEEK, {interval_value}, GETDATE())"
        elif schedule_type == 'monthly':
            next_run_sql = f"DATEADD(MONTH, {interval_value}, GETDATE())"
        else:
            next_run_sql = "DATEADD(DAY, 1, GETDATE())"  # افتراضي: يوم واحد

        # تحديث موعد التشغيل التالي وآخر تشغيل
        cursor.execute(f"""
            UPDATE Scheduled_Reports
            SET LastRunTime = GETDATE(),
                NextRunTime = {next_run_sql}
            WHERE ReportId = ?
        """, (report_id,))

        conn.commit()
        conn.close()

    except Exception as e:
        error_msg = f"❌ خطأ في تحديث موعد التقرير {report_id}: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)

def update_next_run_time_new(schedule_id, schedule_type, interval_value):
    """تحديث موعد التشغيل التالي للتقرير في الهيكل الجديد"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return

        cursor = conn.cursor()

        # حساب الموعد التالي حسب نوع الجدولة
        if schedule_type == 'hourly':
            next_run_sql = f"DATEADD(HOUR, {interval_value}, GETDATE())"
        elif schedule_type == 'daily':
            # للتقارير اليومية، نحافظ على نفس الوقت في اليوم التالي
            next_run_sql = f"DATEADD(DAY, {interval_value}, GETDATE())"
        elif schedule_type == 'weekly':
            next_run_sql = f"DATEADD(WEEK, {interval_value}, GETDATE())"
        elif schedule_type == 'monthly':
            next_run_sql = f"DATEADD(MONTH, {interval_value}, GETDATE())"
        else:
            next_run_sql = "DATEADD(DAY, 1, GETDATE())"  # افتراضي: يوم واحد

        # تحديث موعد التشغيل التالي وآخر تشغيل
        cursor.execute(f"""
            UPDATE Report_Schedules
            SET LastRunTime = GETDATE(),
                NextRunTime = {next_run_sql},
                UpdateDate = GETDATE()
            WHERE ScheduleId = ?
        """, (schedule_id,))

        conn.commit()
        conn.close()

        print(f"✅ تم تحديث موعد التقرير {schedule_id}")

    except Exception as e:
        error_msg = f"❌ خطأ في تحديث موعد التقرير {schedule_id}: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)

def is_notification_enabled(notification_type, branch_id=None):
    """التحقق من تفعيل نوع الإشعار"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return True  # افتراضي: مفعل إذا لم نتمكن من الاتصال

        cursor = conn.cursor()

        # البحث عن إعدادات هذا النوع من الإشعارات
        if branch_id:
            # البحث عن إعدادات خاصة بالفرع أولاً
            cursor.execute("""
                SELECT SendInstantNotification FROM Notification_Types_Settings
                WHERE NotificationType = ? AND (BranchId = ? OR BranchId IS NULL)
                ORDER BY BranchId DESC
            """, (notification_type, branch_id))
        else:
            # البحث عن الإعدادات العامة
            cursor.execute("""
                SELECT SendInstantNotification FROM Notification_Types_Settings
                WHERE NotificationType = ? AND BranchId IS NULL
            """, (notification_type,))

        result = cursor.fetchone()
        conn.close()

        if result:
            return bool(result[0])
        else:
            # إذا لم توجد إعدادات، افتراضي مفعل
            return True

    except Exception as e:
        print(f"⚠️ خطأ في فحص إعدادات الإشعار {notification_type}: {str(e)}")
        return True  # افتراضي: مفعل في حالة الخطأ

# تم حذف دالة get_latest_contract_with_details() غير المستخدمة

def send_notification_sync(message):
    """دالة مساعدة لإرسال الإشعارات من الدوال العادية"""
    import threading

    def run_async():
        try:
            asyncio.run(send_notification(message))
        except Exception as e:
            print(f"❌ خطأ في إرسال الإشعار: {str(e)}")
            if logger:
                logger.error(f"خطأ في إرسال الإشعار: {str(e)}")

    # تشغيل الدالة في thread منفصل لتجنب تضارب حلقات الأحداث
    thread = threading.Thread(target=run_async)
    thread.start()
    thread.join()  # انتظار انتهاء الإرسال

async def send_notification(message):
    """إرسال إشعار للمستخدمين المخولين"""
    try:
        if not message:
            warning_msg = "⚠️ محاولة إرسال رسالة فارغة"
            print(warning_msg)
            if logger:
                logger.warning(warning_msg)
            return

        bot = Bot(token=BOT_TOKEN)
        notification_text = f"🔔 **إشعار تلقائي من Terra Bot**\n\n{message}\n\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # الحصول على قائمة المستقبلين النشطين
        active_recipients = get_active_recipients()

        start_msg = f"📤 بدء إرسال إشعار إلى {len(active_recipients)} مستقبل"
        print(start_msg)
        if logger:
            logger.info(start_msg)

        for chat_id in active_recipients:
            try:
                await bot.send_message(
                    chat_id=chat_id,
                    text=notification_text,
                    parse_mode='Markdown'
                )
                success_msg = f"✅ تم إرسال إشعار إلى {chat_id}"
                print(success_msg)
                if logger:
                    logger.info(success_msg)

            except Exception as e:
                error_msg = f"❌ فشل إرسال إشعار إلى {chat_id}: {str(e)}"
                print(error_msg)
                if logger:
                    logger.error(error_msg)

    except Exception as e:
        error_msg = f"❌ خطأ في إرسال الإشعارات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)

def get_customer_contracts_and_payments(customer_code):
    """الحصول على تفاصيل العقود والمدفوعات لعميل معين"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # استعلام شامل للعقود والمدفوعات
        query = """
        DECLARE @CustomerCode NVARCHAR(50) = ?;

        -- المدفوعات التراكمية
        WITH PaymentsWithRunningTotal AS (
            SELECT
                C.CustomerCode,
                C.NameAr,
                B.NameAr AS BranchName,
                CT.ContractId,
                CT.ContractCode,
                CT.AddDate AS ContractAddDate,
                (CT.TotalValue + CT.DiscountValue) AS TotalContract,
                CT.DiscountValue AS DiscountOnly,
                CT.TotalValue AS NetContractValue,
                P.PaymentDate,
                P.AddDate AS PaymentActualDate,
                P.Amount AS PaymentAmount,
                SUM(P.Amount) OVER (PARTITION BY CT.ContractId ORDER BY P.PaymentDate, P.PaymentId) AS RunningPaid
            FROM Acc_Contracts CT
            INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
            INNER JOIN Sys_Branches B ON C.BranchId = B.BranchId
            INNER JOIN Sys_Payments P
                ON CT.ContractId = P.ContractId
                AND P.IsDeleted = 0
                AND P.IsPaid = 1
            WHERE CT.IsDeleted = 0
              AND C.CustomerCode = @CustomerCode
        ),

        -- الدفعات الغير مدفوعة
        UnpaidPayments AS (
            SELECT
                C.CustomerCode,
                C.NameAr,
                B.NameAr AS BranchName,
                CT.ContractId,
                CT.ContractCode,
                CT.AddDate AS ContractAddDate,
                CT.TotalValue AS NetContractValue,
                P.PaymentDate,
                P.AddDate AS PaymentActualDate,
                P.Amount AS PaymentAmount
            FROM Acc_Contracts CT
            INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
            INNER JOIN Sys_Branches B ON C.BranchId = B.BranchId
            INNER JOIN Sys_Payments P
                ON CT.ContractId = P.ContractId
                AND (P.IsDeleted = 0 OR P.IsDeleted IS NULL)
                AND (P.IsPaid = 0 OR P.IsPaid IS NULL)
            WHERE CT.IsDeleted = 0
              AND C.CustomerCode = @CustomerCode
        )

        -- التغليف الكامل مع تحديد الأعمدة
        SELECT *
        FROM (
            SELECT
                B.NameAr AS BranchName,
                C.NameAr,
                C.CustomerCode,
                CT.ContractCode,
                CT.ContractId,
                'ملخص العقد' AS RowType,
                (CT.TotalValue + CT.DiscountValue) AS TotalContract,
                CT.DiscountValue AS DiscountOnly,
                CT.TotalValue AS NetContractValue,
                CT.AddDate AS ContractAddDate,
                NULL AS PaymentDate,
                NULL AS PaymentActualDate,
                NULL AS PaymentAmount,
                NULL AS RunningPaid,
                NULL AS RemainingAfterThisPayment
            FROM Acc_Contracts CT
            INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
            INNER JOIN Sys_Branches B ON C.BranchId = B.BranchId
            WHERE CT.IsDeleted = 0
              AND C.CustomerCode = @CustomerCode

            UNION ALL

            SELECT
                BranchName,
                NameAr,
                CustomerCode,
                ContractCode,
                ContractId,
                'دفعة' AS RowType,
                NULL AS TotalContract,
                NULL AS DiscountOnly,
                NetContractValue,
                ContractAddDate,
                PaymentDate,
                PaymentActualDate,
                PaymentAmount,
                RunningPaid,
                NetContractValue - RunningPaid AS RemainingAfterThisPayment
            FROM PaymentsWithRunningTotal

            UNION ALL

            SELECT
                BranchName,
                NameAr,
                CustomerCode,
                ContractCode,
                ContractId,
                'دفعة غير مدفوعة' AS RowType,
                NULL AS TotalContract,
                NULL AS DiscountOnly,
                NetContractValue,
                ContractAddDate,
                PaymentDate,
                PaymentActualDate,
                PaymentAmount,
                NULL AS RunningPaid,
                NULL AS RemainingAfterThisPayment
            FROM UnpaidPayments
        ) AS FinalResult

        ORDER BY
            ContractId,
            CASE
                WHEN RowType = 'ملخص العقد' THEN 0
                WHEN RowType = 'دفعة' THEN 1
                ELSE 2
            END,
            PaymentDate;
        """

        cursor.execute(query, (customer_code,))
        results = cursor.fetchall()
        conn.close()

        return results

    except Exception as e:
        error_msg = f"❌ خطأ في الحصول على بيانات العميل {customer_code}: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

def safe_format_date(date_value, default='غير محدد'):
    """تنسيق التاريخ بشكل آمن مع معالجة الأخطاء"""
    if not date_value:
        return default

    try:
        if hasattr(date_value, 'strftime'):
            return date_value.strftime('%Y-%m-%d')
        else:
            # إذا كان string، أخذ أول 10 أحرف
            date_str = str(date_value)[:10]
            # التحقق من صحة التنسيق
            if len(date_str) == 10 and date_str[4] == '-' and date_str[7] == '-':
                return date_str
            else:
                return str(date_value)
    except:
        return str(date_value) if date_value else default

def calculate_days_difference(date_value):
    """حساب الفرق بالأيام بين التاريخ المعطى واليوم الحالي"""
    if not date_value:
        return None

    try:
        if hasattr(date_value, 'date'):
            target_date = date_value.date()
        elif hasattr(date_value, 'strftime'):
            target_date = date_value
        else:
            # محاولة تحويل النص لتاريخ
            from datetime import datetime as dt
            date_str = str(date_value)[:10]
            target_date = dt.strptime(date_str, '%Y-%m-%d').date()

        return (datetime.now().date() - target_date).days
    except:
        return None

def format_customer_contracts_report(data, customer_code, for_private_chat=False):
    """تنسيق تقرير العقود والمدفوعات للعميل"""
    if not data:
        return f"❌ لا توجد بيانات للعميل: {customer_code}"

    # تجميع البيانات حسب العقد
    contracts = {}
    customer_name = ""
    branch_name = ""

    for row in data:
        branch_name = row[0]
        customer_name = row[1]
        contract_id = row[4]
        row_type = row[5]

        if contract_id not in contracts:
            contracts[contract_id] = {
                'contract_code': row[3],
                'summary': None,
                'payments': [],
                'unpaid_payments': []
            }

        if row_type == 'ملخص العقد':
            contracts[contract_id]['summary'] = row
        elif row_type == 'دفعة':
            contracts[contract_id]['payments'].append(row)
        elif row_type == 'دفعة غير مدفوعة':
            contracts[contract_id]['unpaid_payments'].append(row)

    # تنسيق التقرير
    report = f"""📋 **تقرير العقود والمدفوعات**
🏢 الفرع: {branch_name}
👤 العميل: {customer_name}
🔢 كود العميل: {customer_code}
📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

═══════════════════════════════════

"""

    total_contracts_value = 0
    total_paid = 0
    total_remaining = 0
    total_unpaid_installments = 0

    for contract_id, contract_data in contracts.items():
        summary = contract_data['summary']
        payments = contract_data['payments']
        unpaid_payments = contract_data['unpaid_payments']

        if summary:
            contract_code = summary[3]
            total_contract = summary[6] or 0
            discount = summary[7] or 0
            net_value = summary[8] or 0

            # تنسيق التاريخ باستخدام الدالة المساعدة
            contract_date = safe_format_date(summary[9])

            total_contracts_value += net_value

            # حساب إجمالي المدفوع لهذا العقد
            contract_paid = sum(payment[12] or 0 for payment in payments if payment[12])
            contract_remaining = net_value - contract_paid

            total_paid += contract_paid
            total_remaining += contract_remaining
            total_unpaid_installments += len(unpaid_payments)

            report += f"""📄 **عقد رقم: {contract_code}**
├─ 📅 تاريخ العقد: {contract_date}
├─ 💰 إجمالي العقد: {total_contract:,.0f} جنيه
├─ 🏷️ الخصم: {discount:,.0f} جنيه
├─ 💵 صافي العقد: {net_value:,.0f} جنيه
├─ ✅ المدفوع: {contract_paid:,.0f} جنيه
└─ ⏳ المتبقي: {contract_remaining:,.0f} جنيه

"""

            # عرض المدفوعات
            if payments:
                report += "💳 **المدفوعات:**\n"
                for payment in payments:
                    # تنسيق تاريخ المدفوعات باستخدام الدالة المساعدة
                    payment_date = safe_format_date(payment[10])

                    payment_amount = payment[12] or 0
                    running_paid = payment[13] or 0
                    remaining = payment[14] or 0

                    report += f"   • {payment_date}: {payment_amount:,.0f} جنيه (إجمالي مدفوع: {running_paid:,.0f}, متبقي: {remaining:,.0f})\n"
                report += "\n"

            # عرض الأقساط غير المدفوعة
            if unpaid_payments:
                report += "⏰ **الأقساط المستحقة:**\n"
                for unpaid in unpaid_payments:
                    # تنسيق تاريخ الاستحقاق باستخدام الدالة المساعدة
                    due_date = safe_format_date(unpaid[10])
                    amount = unpaid[12] or 0

                    # حساب الأيام المتبقية/المتأخرة
                    days_overdue = calculate_days_difference(unpaid[10])

                    # تحديد حالة الاستحقاق
                    if days_overdue is not None:
                        if days_overdue > 0:
                            status = f"🔴 متأخر {days_overdue} يوم"
                        elif days_overdue == 0:
                            status = "🟡 مستحق اليوم"
                        else:
                            status = f"🟢 باقي {abs(days_overdue)} يوم"
                    else:
                        status = "⚪ غير محدد"

                    report += f"   • {due_date}: {amount:,.0f} جنيه - {status}\n"
                report += "\n"

        report += "─" * 40 + "\n\n"

    # الملخص العام
    report += f"""📊 **الملخص العام:**
💼 إجمالي قيمة العقود: {total_contracts_value:,.0f} جنيه
✅ إجمالي المدفوع: {total_paid:,.0f} جنيه
⏳ إجمالي المتبقي: {total_remaining:,.0f} جنيه
📋 عدد الأقساط المستحقة: {total_unpaid_installments}
📈 نسبة السداد: {(total_paid/total_contracts_value*100) if total_contracts_value > 0 else 0:.1f}%

═══════════════════════════════════
⏰ تم إنشاء التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🤖 Terra Bot - نظام التقارير التلقائي"""

    return report

def check_customer_exists(customer_code):
    """التحقق من وجود العميل وإرجاع معلوماته الأساسية"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # البحث عن العميل
        cursor.execute("""
            SELECT
                C.CustomerId,
                C.CustomerCode,
                C.NameAr,
                C.NameEn,
                C.MainPhoneNo,
                C.Email,
                B.NameAr AS BranchName,
                C.AddDate
            FROM Acc_Customers C
            LEFT JOIN Sys_Branches B ON C.BranchId = B.BranchId
            WHERE C.CustomerCode = ? AND C.IsDeleted = 0
        """, (customer_code,))

        result = cursor.fetchone()
        conn.close()

        return result

    except Exception as e:
        print(f"❌ خطأ في البحث عن العميل {customer_code}: {str(e)}")
        if logger:
            logger.error(f"خطأ في البحث عن العميل {customer_code}: {str(e)}")
        return None

def get_customer_contracts_count(customer_code):
    """الحصول على عدد العقود للعميل"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return 0

        cursor = conn.cursor()

        cursor.execute("""
            SELECT COUNT(*)
            FROM Acc_Contracts CT
            INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
            WHERE C.CustomerCode = ? AND CT.IsDeleted = 0
        """, (customer_code,))

        result = cursor.fetchone()
        conn.close()

        return result[0] if result else 0

    except Exception as e:
        print(f"❌ خطأ في حساب عقود العميل {customer_code}: {str(e)}")
        return 0

def get_customer_payments_summary(customer_code):
    """الحصول على ملخص المدفوعات للعميل"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        cursor.execute("""
            SELECT
                COUNT(CASE WHEN P.IsPaid = 1 THEN 1 END) AS PaidCount,
                COUNT(CASE WHEN P.IsPaid = 0 OR P.IsPaid IS NULL THEN 1 END) AS UnpaidCount,
                SUM(CASE WHEN P.IsPaid = 1 THEN P.Amount ELSE 0 END) AS TotalPaid,
                SUM(CASE WHEN P.IsPaid = 0 OR P.IsPaid IS NULL THEN P.Amount ELSE 0 END) AS TotalUnpaid
            FROM Acc_Contracts CT
            INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
            INNER JOIN Sys_Payments P ON CT.ContractId = P.ContractId
            WHERE C.CustomerCode = ? AND CT.IsDeleted = 0 AND P.IsDeleted = 0
        """, (customer_code,))

        result = cursor.fetchone()
        conn.close()

        return result

    except Exception as e:
        print(f"❌ خطأ في حساب ملخص المدفوعات للعميل {customer_code}: {str(e)}")
        return None

def send_customer_contracts_report(customer_code):
    """إرسال تقرير العقود والمدفوعات لعميل معين مع خطوات الاستفسار"""
    try:
        print(f"🔍 بدء عملية البحث عن العميل: {customer_code}")

        # الخطوة 1: التحقق من وجود العميل
        print("📋 الخطوة 1: التحقق من وجود العميل...")
        customer_info = check_customer_exists(customer_code)

        if not customer_info:
            error_msg = f"❌ العميل غير موجود: {customer_code}"
            print(error_msg)
            send_notification_sync(error_msg)
            return False

        customer_name = customer_info[2]
        branch_name = customer_info[6]
        print(f"✅ تم العثور على العميل: {customer_name} - {branch_name}")

        # الخطوة 2: فحص عدد العقود
        print("📋 الخطوة 2: فحص عدد العقود...")
        contracts_count = get_customer_contracts_count(customer_code)
        print(f"📊 عدد العقود: {contracts_count}")

        if contracts_count == 0:
            warning_msg = f"⚠️ العميل {customer_name} ({customer_code}) لا يملك أي عقود"
            print(warning_msg)
            send_notification_sync(warning_msg)
            return False

        # الخطوة 3: فحص ملخص المدفوعات
        print("📋 الخطوة 3: فحص ملخص المدفوعات...")
        payments_summary = get_customer_payments_summary(customer_code)

        if payments_summary:
            paid_count = payments_summary[0] or 0
            unpaid_count = payments_summary[1] or 0
            total_paid = payments_summary[2] or 0
            total_unpaid = payments_summary[3] or 0
            print(f"💰 ملخص المدفوعات: مدفوع ({paid_count}) = {total_paid:,.0f} جنيه، مستحق ({unpaid_count}) = {total_unpaid:,.0f} جنيه")

        # الخطوة 4: الحصول على التفاصيل الكاملة
        print("📋 الخطوة 4: جلب التفاصيل الكاملة للعقود والمدفوعات...")
        data = get_customer_contracts_and_payments(customer_code)

        if data:
            print(f"📊 تم جلب {len(data)} سجل من البيانات")

            # الخطوة 5: تنسيق التقرير
            print("📋 الخطوة 5: تنسيق التقرير...")
            formatted_report = format_customer_contracts_report(data, customer_code)

            # الخطوة 6: إرسال التقرير
            print("📋 الخطوة 6: إرسال التقرير...")
            send_notification_sync(formatted_report)

            success_msg = f"✅ تم إرسال تقرير العميل {customer_name} ({customer_code}) بنجاح"
            print(success_msg)
            if logger:
                logger.info(f"تم إرسال تقرير العميل {customer_code}")
            return True
        else:
            error_msg = f"❌ فشل في جلب بيانات العميل: {customer_code}"
            print(error_msg)
            send_notification_sync(error_msg)
            return False

    except Exception as e:
        error_msg = f"❌ خطأ في إرسال تقرير العميل {customer_code}: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        send_notification_sync(f"❌ خطأ في إنشاء تقرير العميل {customer_code}")
        return False

def test_customer_contracts_report():
    """اختبار تقرير العقود والمدفوعات مع عميل تجريبي"""
    try:
        # يمكن تغيير كود العميل هنا للاختبار
        test_customer_code = "1213"  # كود العميل المطلوب اختباره

        print(f"🧪 اختبار تقرير العقود والمدفوعات للعميل: {test_customer_code}")

        # إرسال التقرير
        result = send_customer_contracts_report(test_customer_code)

        if result:
            print("✅ نجح الاختبار - تم إرسال التقرير")
        else:
            print("❌ فشل الاختبار - لم يتم إرسال التقرير")

        return result

    except Exception as e:
        error_msg = f"❌ خطأ في اختبار تقرير العقود: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return False

# دالة مساعدة لإرسال تقرير عميل من خلال كود العميل (للاستخدام الخارجي)
def get_customer_contracts_report_text(customer_code):
    """الحصول على نص تقرير العميل بدون إرسال"""
    try:
        print(f"🔍 جاري إنشاء تقرير للعميل: {customer_code}")

        # الخطوة 1: التحقق من وجود العميل
        customer_info = check_customer_exists(customer_code)

        if not customer_info:
            return f"❌ العميل غير موجود: {customer_code}"

        customer_name = customer_info[2]
        branch_name = customer_info[6]
        print(f"✅ تم العثور على العميل: {customer_name} - {branch_name}")

        # الخطوة 2: فحص عدد العقود
        contracts_count = get_customer_contracts_count(customer_code)
        print(f"📊 عدد العقود: {contracts_count}")

        if contracts_count == 0:
            return f"⚠️ العميل {customer_name} ({customer_code}) لا يملك أي عقود"

        # الخطوة 3: الحصول على التفاصيل الكاملة
        data = get_customer_contracts_and_payments(customer_code)

        if data:
            print(f"📊 تم جلب {len(data)} سجل من البيانات")

            # تنسيق التقرير للمحادثة الخاصة
            formatted_report = format_customer_contracts_report(data, customer_code, for_private_chat=True)

            print(f"✅ تم إنشاء تقرير العميل {customer_name} ({customer_code}) بنجاح")
            return formatted_report
        else:
            return f"❌ فشل في جلب بيانات العميل: {customer_code}"

    except Exception as e:
        error_msg = f"❌ خطأ في إنشاء تقرير العميل {customer_code}: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return f"❌ خطأ في إنشاء تقرير العميل {customer_code}"

def check_contracts_and_payments_updates():
    """فحص التحديثات في العقود والمدفوعات وإرسال تقارير تلقائية"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # فحص العقود المحدثة حديثاً (آخر ساعة)
        cursor.execute("""
            SELECT
                C.CustomerCode,
                C.NameAr AS CustomerName,
                CT.ContractCode,
                CT.ContractId,
                CT.UpdateDate,
                B.NameAr AS BranchName
            FROM Acc_Contracts CT
            INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
            INNER JOIN Sys_Branches B ON C.BranchId = B.BranchId
            WHERE CT.UpdateDate >= DATEADD(HOUR, -1, GETDATE())
              AND CT.IsDeleted = 0
            ORDER BY CT.UpdateDate DESC
        """)

        updated_contracts = cursor.fetchall()

        # فحص المدفوعات الجديدة (آخر ساعة)
        cursor.execute("""
            SELECT
                C.CustomerCode,
                C.NameAr AS CustomerName,
                CT.ContractCode,
                P.Amount,
                P.PaymentDate,
                P.AddDate,
                B.NameAr AS BranchName
            FROM Sys_Payments P
            INNER JOIN Acc_Contracts CT ON P.ContractId = CT.ContractId
            INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
            INNER JOIN Sys_Branches B ON C.BranchId = B.BranchId
            WHERE P.AddDate >= DATEADD(HOUR, -1, GETDATE())
              AND P.IsDeleted = 0
              AND P.IsPaid = 1
            ORDER BY P.AddDate DESC
        """)

        new_payments = cursor.fetchall()
        conn.close()

        # إرسال تحديثات العقود
        for contract in updated_contracts:
            customer_code = contract[0]
            customer_name = contract[1]
            contract_code = contract[2]
            branch_name = contract[5]

            update_msg = f"""🔄 **تحديث عقد**

👤 العميل: {customer_name} ({customer_code})
🏢 الفرع: {branch_name}
📋 العقد: {contract_code}
⏰ وقت التحديث: {datetime.now().strftime('%Y-%m-%d %H:%M')}

📊 **تقرير محدث للعميل:**"""

            # إرسال التحديث
            send_notification_sync(update_msg)

            # إرسال التقرير المحدث
            send_customer_contracts_report(customer_code)

        # إرسال تحديثات المدفوعات
        for payment in new_payments:
            customer_code = payment[0]
            customer_name = payment[1]
            contract_code = payment[2]
            amount = payment[3]
            payment_date = safe_format_date(payment[4])
            branch_name = payment[6]

            payment_msg = f"""💰 **دفعة جديدة**

👤 العميل: {customer_name} ({customer_code})
🏢 الفرع: {branch_name}
📋 العقد: {contract_code}
💵 المبلغ: {amount:,.0f} جنيه
📅 تاريخ الدفعة: {payment_date}
⏰ وقت الإضافة: {datetime.now().strftime('%Y-%m-%d %H:%M')}

📊 **تقرير محدث للعميل:**"""

            # إرسال التحديث
            send_notification_sync(payment_msg)

            # إرسال التقرير المحدث
            send_customer_contracts_report(customer_code)

        if updated_contracts or new_payments:
            print(f"📊 تم إرسال {len(updated_contracts)} تحديث عقد و {len(new_payments)} دفعة جديدة")

        return len(updated_contracts) + len(new_payments)

    except Exception as e:
        error_msg = f"❌ خطأ في فحص تحديثات العقود والمدفوعات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

def get_all_customers_by_branch(branch_id):
    """الحصول على جميع العملاء الذين لديهم عقود في فرع معين"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # الحصول على جميع العملاء الذين لديهم عقود في الفرع المحدد
        cursor.execute("""
            SELECT DISTINCT
                C.CustomerCode,
                C.NameAr,
                B.NameAr AS BranchName,
                COUNT(CT.ContractId) AS ContractsCount,
                SUM(CT.TotalValue) AS TotalContractsValue,
                SUM(ISNULL(P.Amount, 0)) AS TotalPaid
            FROM Acc_Customers C
            INNER JOIN Acc_Contracts CT ON C.CustomerId = CT.CustomerId
            INNER JOIN Sys_Branches B ON C.BranchId = B.BranchId
            LEFT JOIN Sys_Payments P ON CT.ContractId = P.ContractId AND P.IsPaid = 1 AND P.IsDeleted = 0
            WHERE C.BranchId = ? AND C.IsDeleted = 0 AND CT.IsDeleted = 0
            GROUP BY C.CustomerCode, C.NameAr, B.NameAr
            ORDER BY C.NameAr
        """, (branch_id,))

        results = cursor.fetchall()
        conn.close()

        return results

    except Exception as e:
        error_msg = f"❌ خطأ في الحصول على عملاء الفرع {branch_id}: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

def send_all_customers_reports_by_branch(branch_id):
    """إرسال تقارير جميع العملاء في فرع معين"""
    try:
        # الحصول على قائمة العملاء
        customers = get_all_customers_by_branch(branch_id)

        if not customers:
            return "❌ لا توجد عملاء في هذا الفرع"

        branch_name = customers[0][2]  # اسم الفرع من أول سجل

        # إرسال ملخص أولاً
        summary_msg = f"""📊 **تقارير عملاء فرع {branch_name}**

📋 عدد العملاء: {len(customers)}
📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}

🔄 جاري إرسال التقارير التفصيلية..."""

        send_notification_sync(summary_msg)

        # إرسال تقرير لكل عميل
        success_count = 0
        for customer in customers:
            customer_code = customer[0]
            customer_name = customer[1]
            contracts_count = customer[3]

            print(f"📋 إرسال تقرير للعميل: {customer_name} ({customer_code})")

            # إرسال تقرير العميل
            if send_customer_contracts_report(customer_code):
                success_count += 1

            # انتظار قصير بين التقارير لتجنب الإرهاق
            import time
            time.sleep(2)

        # إرسال ملخص نهائي
        final_msg = f"""✅ **انتهى إرسال تقارير فرع {branch_name}**

📊 تم إرسال {success_count} من {len(customers)} تقرير بنجاح
⏰ وقت الانتهاء: {datetime.now().strftime('%Y-%m-%d %H:%M')}"""

        send_notification_sync(final_msg)

        return f"✅ تم إرسال {success_count} تقرير من {len(customers)} عميل"

    except Exception as e:
        error_msg = f"❌ خطأ في إرسال تقارير الفرع {branch_id}: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return error_msg

def send_customer_report_by_code(customer_code):
    """دالة مبسطة لإرسال تقرير عميل - للاستخدام كزر"""
    if not customer_code:
        print("❌ يجب تحديد كود العميل")
        return False

    print(f"🎯 طلب تقرير للعميل: {customer_code}")
    return send_customer_contracts_report(customer_code)

def check_new_customers():
    """فحص العملاء الجدد مع التفاصيل الشاملة والتحقق من قاعدة البيانات حسب الفرع"""

    # التحقق من تفعيل إشعارات العملاء
    if not is_notification_enabled('customers'):
        return None

    try:
        # الحصول على كل العملاء الجدد بعد آخر معرف
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # الحصول على آخر معرف تم إرساله لهذا الفرع
        def get_last_sent_customer_id(branch_id):
            try:
                notifications_conn = connect_to_notifications_db()
                if not notifications_conn:
                    return 0

                notifications_cursor = notifications_conn.cursor()
                # جلب آخر معرف عميل (CustomerId) من نوع العملاء فقط عالم
                notifications_cursor.execute("""
                    SELECT TOP 1 RecordUniqueId FROM Notifications_Log
                    WHERE DataType = 'عميل' AND IsActive = 1 AND RecordUniqueId IS NOT NULL
                    ORDER BY RecordUniqueId DESC
                """)

                result = notifications_cursor.fetchone()
                notifications_conn.close()

                last_id = result[0] if result else 0
                print(f"📊 آخر معرف عميل (CustomerId): {last_id}")
                return last_id
            except Exception as e:
                print(f"❌ خطأ في الحصول على آخر معرف عميل: {str(e)}")
                return 0

        # استعلام للحصول على العملاء الجدد بعد آخر معرف تم إرساله
        query = """
        SELECT
            c.CustomerId, c.CustomerCode, c.NameAr, c.NameEn, c.MainPhoneNo,
            c.SubMainPhoneNo, c.Email, c.Address, c.NationalId, c.Notes,
            c.AddDate, c.UpdateDate, c.BranchId,
            ISNULL(branch.NameAr, 'فرع غير محدد') AS BranchName,
            ISNULL(city.NameAr, 'غير محدد') AS CityName,
            ISNULL(pay.NameAr, 'غير محدد') AS PayTypeName,
            ISNULL(social.NameAr, 'غير محدد') AS SocialMediaName,
            ISNULL(addUser.UserName, 'غير محدد') AS AddedByUser,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            ISNULL(updateUser.UserName, '') AS UpdatedByUser,
            ISNULL(updateUser.FullName, '') AS UpdatedByFullName
        FROM Acc_Customers c
        LEFT JOIN Sys_Branches branch ON c.BranchId = branch.BranchId
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Acc_PayType pay ON c.PayTypeId = pay.PayTypeId
        LEFT JOIN Sys_SocialMedia social ON c.SocialMediaId = social.SocialMediaId
        LEFT JOIN Sys_Users addUser ON c.AddUser = addUser.UserId
        LEFT JOIN Sys_Users updateUser ON c.UpdateUser = updateUser.UserId
        WHERE c.IsDeleted = 0 AND c.BranchId = ? AND c.CustomerId > ?
        ORDER BY c.CustomerId ASC
        """

        # فحص كل فرع منفصل
        notifications_sent = 0
        # الحصول على جميع الفروع من قاعدة البيانات تلقائ
        cursor.execute("SELECT DISTINCT BranchId FROM Acc_Customers WHERE IsDeleted = 0 AND BranchId IS NOT NULL ORDER BY BranchId")
        branches = [row[0] for row in cursor.fetchall()]
        print(f"🏢 فحص العملاء في الفروع: {branches}")

        for branch_id in branches:
            # الحصول على آخر معرف تم إرساله لهذا الفرع
            last_sent_id = get_last_sent_customer_id(branch_id)
            print(f"📊 آخر معرف عميل تم إرساله للفرع {branch_id}: {last_sent_id}")

            # تنفيذ الاستعلام للحصول على العملاء الجدد بعد آخر معرف
            cursor.execute(query, (branch_id, last_sent_id))
            new_customers = cursor.fetchall()

            # معالجة كل عميل جديد في هذا الفرع
            for customer in new_customers:

                # تحديد أيقونة الفرع
                branch_icon = "🏢" if customer[12] == 2 else "🏬" if customer[12] == 3 else "🏪"

                message = f"""👤 **عميل جديد تم إضافته**

{branch_icon} **الفرع:** {customer[13]}

🔢 **الكود:** {customer[1]}
👤 **الاسم العربي:** {customer[2] or 'غير محدد'}
🔤 **الاسم الإنجليزي:** {customer[3] or 'غير محدد'}
📱 **الهاتف الأساسي:** {customer[4] or 'غير محدد'}
📞 **الهاتف الفرعي:** {customer[5] or 'غير محدد'}
📧 **الإيميل:** {customer[6] or 'غير محدد'}
🏠 **العنوان:** {customer[7] or 'غير محدد'}
🆔 **الرقم القومي:** {customer[8] or 'غير محدد'}
📝 **ملاحظات:** {customer[9] or 'لا توجد'}

🌍 **المدينة/المنطقة:** {customer[14]}
💳 **طريقة الدفع:** {customer[15]}
📱 **وسيلة التواصل:** {customer[16]}

👨‍💼 **تم الإنشاء بواسطة:** {customer[18]}
📅 **تاريخ الإضافة:** {customer[10].strftime('%Y-%m-%d %H:%M') if customer[10] else 'غير محدد'}"""

                if customer[11]:  # إذا كان هناك تاريخ تحديث
                    message += f"\n🔄 **آخر تحديث:** {customer[11].strftime('%Y-%m-%d %H:%M')}"
                    if customer[19]:  # إذا كان هناك من حدث
                        message += f"\n👨‍🔧 **تم التحديث بواسطة:** {customer[20]}"

                # حفظ في قاعدة البيانات
                save_notification_to_db(
                    'عميل',
                    customer[1],  # CustomerCode
                    customer[2] or 'غير محدد',  # NameAr
                    customer[12],  # BranchId
                    customer[13],  # BranchName
                    customer[0],   # CustomerId (المعرف الفريد)
                    customer[10],  # AddDate
                    customer[18]   # AddedByFullName
                )

                # إرسال الإشعار
                send_notification_sync(message)
                notifications_sent += 1
                print(f"✅ تم إرسال إشعار للعميل {customer[1]} (ID: {customer[0]})")

        conn.close()

        if notifications_sent > 0:
            print(f"📊 تم إرسال {notifications_sent} إشعار عميل جديد")
            return None  # لا نرسل رسالة إضافية لأن كل عميل أرسل إشعار منفصل

        return None

    except Exception as e:
        print(f"❌ خطأ في فحص العملاء الجدد: {str(e)}")
        return None

def check_new_previews():
    """فحص المعاينات الجديدة مع التفاصيل الشاملة والتحقق من قاعدة البيانات حسب الفرع"""

    # التحقق من تفعيل إشعارات المعاينات
    if not is_notification_enabled('previews'):
        return None

    try:
        # الحصول على كل المعاينات الجديدة بعد آخر معرف لكل فرع
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # الحصول على آخر معرف تم إرساله لهذا الفرع
        def get_last_sent_preview_id(branch_id):
            try:
                notifications_conn = connect_to_notifications_db()
                if not notifications_conn:
                    return 0

                notifications_cursor = notifications_conn.cursor()

                # جلب آخر معرف معاينة (PreviewId) من نوع المعاينات فقط عالم
                notifications_cursor.execute("""
                    SELECT TOP 1 RecordUniqueId FROM Notifications_Log
                    WHERE DataType = 'معاينة' AND IsActive = 1 AND RecordUniqueId IS NOT NULL
                    ORDER BY RecordUniqueId DESC
                """)

                result = notifications_cursor.fetchone()
                notifications_conn.close()

                last_id = result[0] if result else 0
                print(f"📊 آخر معرف معاينة (PreviewId): {last_id}")
                return last_id
            except Exception as e:
                print(f"❌ خطأ في الحصول على آخر معرف معاينة: {str(e)}")
                return 0

        # استعلام للحصول على المعاينات الجديدة بعد آخر معرف تم إرساله
        query = """
        SELECT
            p.PreviewId, p.PreviewCode, p.Date AS PreviewDate, p.Notes, p.BranchId,
            ISNULL(branch.NameAr, 'فرع غير محدد') AS BranchName,
            c.CustomerCode, c.NameAr AS CustomerName, c.MainPhoneNo, c.Address,
            ISNULL(city.NameAr, 'غير محدد') AS CityName,
            ISNULL(addUser.UserName, 'غير محدد') AS AddedByUser,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            p.AddDate
        FROM Sys_Previews p
        INNER JOIN Acc_Customers c ON p.CustomerId = c.CustomerId
        LEFT JOIN Sys_Branches branch ON p.BranchId = branch.BranchId
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON p.AddUser = addUser.UserId
        WHERE p.IsDeleted = 0 AND c.IsDeleted = 0 AND c.BranchId = ? AND p.PreviewId > ?
        ORDER BY p.PreviewId ASC
        """

        # فحص كل فرع منفصل
        notifications_sent = 0
        # الحصول على جميع الفروع من قاعدة البيانات تلقائ
        cursor.execute("SELECT DISTINCT BranchId FROM Acc_Customers WHERE IsDeleted = 0 AND BranchId IS NOT NULL ORDER BY BranchId")
        branches = [row[0] for row in cursor.fetchall()]
        print(f"🏢 فحص المعاينات في الفروع: {branches}")

        for branch_id in branches:
            # الحصول على آخر معرف تم إرساله لهذا الفرع
            last_sent_id = get_last_sent_preview_id(branch_id)
            print(f"📊 آخر معرف معاينة تم إرساله للفرع {branch_id}: {last_sent_id}")

            # تنفيذ الاستعلام للحصول على المعاينات الجديدة بعد آخر معرف
            cursor.execute(query, (branch_id, last_sent_id))
            new_previews = cursor.fetchall()

            # معالجة كل معاينة جديدة في هذا الفرع
            for preview in new_previews:

                # تحديد أيقونة الفرع
                branch_icon = "🏢" if preview[4] == 2 else "🏬" if preview[4] == 3 else "🏪"

                message = f"""👁️ **معاينة جديدة تم إضافتها**

{branch_icon} **الفرع:** {preview[5]}

🔢 **كود المعاينة:** {preview[1]}
📅 **تاريخ المعاينة:** {preview[2].strftime('%Y-%m-%d %H:%M') if preview[2] else 'غير محدد'}
📝 **ملاحظات المعاينة:** {preview[3] or 'لا توجد'}

👤 **بيانات العميل:**
🔢 **كود العميل:** {preview[6]}
👤 **اسم العميل:** {preview[7] or 'غير محدد'}
📱 **هاتف العميل:** {preview[8] or 'غير محدد'}
🏠 **عنوان العميل:** {preview[9] or 'غير محدد'}
🌍 **المدينة/المنطقة:** {preview[10] or 'غير محدد'}

👨‍💼 **تم الإنشاء بواسطة:** {preview[12] or 'غير محدد'}
📅 **تاريخ الإضافة:** {preview[13].strftime('%Y-%m-%d %H:%M') if preview[13] else 'غير محدد'}"""

                # حفظ في قاعدة البيانات
                save_notification_to_db(
                    'معاينة',
                    preview[1],  # PreviewCode
                    f"معاينة للعميل {preview[7] or 'غير محدد'}",
                    preview[4],  # BranchId
                    preview[5],  # BranchName
                    preview[0],  # PreviewId (المعرف الفريد)
                    preview[13], # AddDate
                    preview[12] or 'غير محدد'  # AddedByFullName
                )

                # إرسال الإشعار
                send_notification_sync(message)
                notifications_sent += 1
                print(f"✅ تم إرسال إشعار للمعاينة {preview[1]} (ID: {preview[0]})")

        conn.close()

        if notifications_sent > 0:
            print(f"📊 تم إرسال {notifications_sent} إشعار معاينة جديدة")
            return None  # لا نرسل رسالة إضافية لأن كل معاينة أرسلت إشعار منفصل

        return None

    except Exception as e:
        print(f"❌ خطأ في فحص المعاينات الجديدة: {str(e)}")
        return None

def check_new_meetings():
    """فحص الاجتماعات الجديدة مع التفاصيل الشاملة والتحقق من قاعدة البيانات حسب الفرع"""

    # التحقق من تفعيل إشعارات الاجتماعات
    if not is_notification_enabled('meetings'):
        return None

    try:
        # الحصول على كل الاجتماعات الجديدة بعد آخر معرف لكل فرع
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # الحصول على آخر معرف تم إرساله لهذا الفرع
        def get_last_sent_meeting_id(branch_id):
            try:
                notifications_conn = connect_to_notifications_db()
                if not notifications_conn:
                    return 0

                notifications_cursor = notifications_conn.cursor()
                # جلب آخر معرف اجتماع (MeetingId) من نوع الاجتماعات فقط عالم
                notifications_cursor.execute("""
                    SELECT TOP 1 RecordUniqueId FROM Notifications_Log
                    WHERE DataType = 'اجتماع' AND IsActive = 1 AND RecordUniqueId IS NOT NULL
                    ORDER BY RecordUniqueId DESC
                """)

                result = notifications_cursor.fetchone()
                notifications_conn.close()

                last_id = result[0] if result else 0
                print(f"📊 آخر معرف اجتماع (MeetingId): {last_id}")
                return last_id
            except Exception as e:
                print(f"❌ خطأ في الحصول على آخر معرف اجتماع: {str(e)}")
                return 0

        # استعلام للحصول على الاجتماعات الجديدة بعد آخر معرف تم إرساله
        query = """
        SELECT
            m.MeetingId, m.MeetingCode, m.Date AS MeetingDate, m.Notes, m.BranchId,
            ISNULL(branch.NameAr, 'فرع غير محدد') AS BranchName,
            c.CustomerCode, c.NameAr AS CustomerName, c.MainPhoneNo, c.Address,
            ISNULL(city.NameAr, 'غير محدد') AS CityName,
            ISNULL(addUser.UserName, 'غير محدد') AS AddedByUser,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            m.AddDate
        FROM Sys_Meetings m
        INNER JOIN Acc_Customers c ON m.CustomerId = c.CustomerId
        LEFT JOIN Sys_Branches branch ON m.BranchId = branch.BranchId
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON m.AddUser = addUser.UserId
        WHERE m.IsDeleted = 0 AND c.IsDeleted = 0 AND c.BranchId = ? AND m.MeetingId > ?
        ORDER BY m.MeetingId ASC
        """

        # فحص كل فرع منفصل
        notifications_sent = 0
        # الحصول على جميع الفروع من قاعدة البيانات تلقائ
        cursor.execute("SELECT DISTINCT BranchId FROM Acc_Customers WHERE IsDeleted = 0 AND BranchId IS NOT NULL ORDER BY BranchId")
        branches = [row[0] for row in cursor.fetchall()]
        print(f"🏢 فحص الاجتماعات في الفروع: {branches}")

        for branch_id in branches:
            # الحصول على آخر معرف تم إرساله لهذا الفرع
            last_sent_id = get_last_sent_meeting_id(branch_id)
            print(f"📊 آخر معرف اجتماع تم إرساله للفرع {branch_id}: {last_sent_id}")

            # تنفيذ الاستعلام للحصول على الاجتماعات الجديدة بعد آخر معرف
            cursor.execute(query, (branch_id, last_sent_id))
            new_meetings = cursor.fetchall()

            # معالجة كل اجتماع جديد في هذا الفرع
            for meeting in new_meetings:

                # تحديد أيقونة الفرع
                branch_icon = "🏢" if meeting[4] == 2 else "🏬" if meeting[4] == 3 else "🏪"

                message = f"""🤝 **اجتماع جديد تم إضافته**

{branch_icon} **الفرع:** {meeting[5]}

🔢 **كود الاجتماع:** {meeting[1]}
📅 **تاريخ الاجتماع:** {meeting[2].strftime('%Y-%m-%d %H:%M') if meeting[2] else 'غير محدد'}
📝 **ملاحظات الاجتماع:** {meeting[3] or 'لا توجد'}

👤 **بيانات العميل:**
🔢 **كود العميل:** {meeting[6]}
👤 **اسم العميل:** {meeting[7] or 'غير محدد'}
📱 **هاتف العميل:** {meeting[8] or 'غير محدد'}
🏠 **عنوان العميل:** {meeting[9] or 'غير محدد'}
🌍 **المدينة/المنطقة:** {meeting[10] or 'غير محدد'}

👨‍💼 **تم الإنشاء بواسطة:** {meeting[12] or 'غير محدد'}
📅 **تاريخ الإضافة:** {meeting[13].strftime('%Y-%m-%d %H:%M') if meeting[13] else 'غير محدد'}"""

                # حفظ في قاعدة البيانات
                save_notification_to_db(
                    'اجتماع',
                    meeting[1],  # MeetingCode
                    f"اجتماع مع العميل {meeting[7] or 'غير محدد'}",
                    meeting[4],  # BranchId
                    meeting[5],  # BranchName
                    meeting[0],  # MeetingId (المعرف الفريد)
                    meeting[13], # AddDate
                    meeting[12] or 'غير محدد'  # AddedByFullName
                )

                # إرسال الإشعار
                send_notification_sync(message)
                notifications_sent += 1
                print(f"✅ تم إرسال إشعار للاجتماع {meeting[1]} (ID: {meeting[0]})")

        conn.close()

        if notifications_sent > 0:
            print(f"📊 تم إرسال {notifications_sent} إشعار اجتماع جديد")
            return None  # لا نرسل رسالة إضافية لأن كل اجتماع أرسل إشعار منفصل

        return None

    except Exception as e:
        error_msg = f"❌ خطأ في فحص الاجتماعات الجديدة: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

def check_new_designs():
    """فحص التصميمات الجديدة مع التفاصيل الشاملة والتحقق من قاعدة البيانات حسب الفرع"""

    try:
        # الحصول على كل التصميمات الجديدة بعد آخر معرف لكل فرع
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # الحصول على آخر معرف تم إرساله لهذا الفرع
        def get_last_sent_design_id(branch_id):
            try:
                notifications_conn = connect_to_notifications_db()
                if not notifications_conn:
                    return 0

                notifications_cursor = notifications_conn.cursor()
                # جلب آخر معرف تصميم (DesignId) من نوع التصميمات فقط عالم
                notifications_cursor.execute("""
                    SELECT TOP 1 RecordUniqueId FROM Notifications_Log
                    WHERE DataType = 'تصميم' AND IsActive = 1 AND RecordUniqueId IS NOT NULL
                    ORDER BY RecordUniqueId DESC
                """)

                result = notifications_cursor.fetchone()
                notifications_conn.close()

                last_id = result[0] if result else 0
                print(f"📊 آخر معرف تصميم (DesignId): {last_id}")
                return last_id
            except Exception as e:
                print(f"❌ خطأ في الحصول على آخر معرف تصميم: {str(e)}")
                return 0

        # استعلام للحصول على التصميمات الجديدة بعد آخر معرف تم إرساله
        query = """
        SELECT
            d.DesignId, d.DesignCode, d.Date AS DesignDate, d.Notes, d.BranchId,
            ISNULL(branch.NameAr, 'فرع غير محدد') AS BranchName,
            c.CustomerCode, c.NameAr AS CustomerName, c.MainPhoneNo, c.Address,
            ISNULL(city.NameAr, 'غير محدد') AS CityName,
            ISNULL(addUser.UserName, 'غير محدد') AS AddedByUser,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            d.AddDate
        FROM Sys_Designs d
        INNER JOIN Acc_Customers c ON d.CustomerId = c.CustomerId
        LEFT JOIN Sys_Branches branch ON d.BranchId = branch.BranchId
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON d.AddUser = addUser.UserId
        WHERE d.IsDeleted = 0 AND c.IsDeleted = 0 AND c.BranchId = ? AND d.DesignId > ?
        ORDER BY d.DesignId ASC
        """

        # فحص كل فرع منفصل
        notifications_sent = 0
        # الحصول على جميع الفروع من قاعدة البيانات تلقائ
        cursor.execute("SELECT DISTINCT BranchId FROM Acc_Customers WHERE IsDeleted = 0 AND BranchId IS NOT NULL ORDER BY BranchId")
        branches = [row[0] for row in cursor.fetchall()]
        print(f"🏢 فحص التصميمات في الفروع: {branches}")

        for branch_id in branches:
            # الحصول على آخر معرف تم إرساله لهذا الفرع
            last_sent_id = get_last_sent_design_id(branch_id)
            print(f"📊 آخر معرف تصميم تم إرساله للفرع {branch_id}: {last_sent_id}")

            # تنفيذ الاستعلام للحصول على التصميمات الجديدة بعد آخر معرف
            cursor.execute(query, (branch_id, last_sent_id))
            new_designs = cursor.fetchall()

            # معالجة كل تصميم جديد في هذا الفرع
            for design in new_designs:

                # تحديد أيقونة الفرع
                branch_icon = "🏢" if design[4] == 2 else "🏬" if design[4] == 3 else "🏪"

                message = f"""🎨 **تصميم جديد تم إضافته**

{branch_icon} **الفرع:** {design[5]}

🔢 **كود التصميم:** {design[1]}
📅 **تاريخ التصميم:** {design[2].strftime('%Y-%m-%d %H:%M') if design[2] else 'غير محدد'}
📝 **ملاحظات التصميم:** {design[3] or 'لا توجد'}

👤 **بيانات العميل:**
🔢 **كود العميل:** {design[6]}
👤 **اسم العميل:** {design[7] or 'غير محدد'}
📱 **هاتف العميل:** {design[8] or 'غير محدد'}
🏠 **عنوان العميل:** {design[9] or 'غير محدد'}
🌍 **المدينة/المنطقة:** {design[10] or 'غير محدد'}

👨‍💼 **تم الإنشاء بواسطة:** {design[12] or 'غير محدد'}
📅 **تاريخ الإضافة:** {design[13].strftime('%Y-%m-%d %H:%M') if design[13] else 'غير محدد'}"""

                # حفظ في قاعدة البيانات
                save_notification_to_db(
                    'تصميم',
                    design[1],  # DesignCode
                    f"تصميم للعميل {design[7] or 'غير محدد'}",
                    design[4],  # BranchId
                    design[5],  # BranchName
                    design[0],  # DesignId (المعرف الفريد)
                    design[13], # AddDate
                    design[12] or 'غير محدد'  # AddedByFullName
                )

                # إرسال الإشعار
                send_notification_sync(message)
                notifications_sent += 1
                print(f"✅ تم إرسال إشعار للتصميم {design[1]} (ID: {design[0]})")

        conn.close()

        if notifications_sent > 0:
            print(f"📊 تم إرسال {notifications_sent} إشعار تصميم جديد")
            return None  # لا نرسل رسالة إضافية لأن كل تصميم أرسل إشعار منفصل

        return None

    except Exception as e:
        error_msg = f"❌ خطأ في فحص التصميمات الجديدة: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

def check_new_contracts():
    """فحص العقود الجديدة مع التفاصيل الشاملة والتحقق من قاعدة البيانات حسب الفرع"""

    try:
        # الحصول على كل العقود الجديدة بعد آخر معرف لكل فرع
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # الحصول على آخر معرف تم إرساله لهذا الفرع
        def get_last_sent_contract_id(branch_id):
            try:
                notifications_conn = connect_to_notifications_db()
                if not notifications_conn:
                    return 0

                notifications_cursor = notifications_conn.cursor()
                # جلب آخر معرف عقد (ContractId) من نوع العقود فقط عالم
                notifications_cursor.execute("""
                    SELECT TOP 1 RecordUniqueId FROM Notifications_Log
                    WHERE DataType = 'عقد' AND IsActive = 1 AND RecordUniqueId IS NOT NULL
                    ORDER BY RecordUniqueId DESC
                """)

                result = notifications_cursor.fetchone()
                notifications_conn.close()

                last_id = result[0] if result else 0
                print(f"📊 آخر معرف عقد (ContractId): {last_id}")
                return last_id
            except Exception as e:
                print(f"❌ خطأ في الحصول على آخر معرف عقد: {str(e)}")
                return 0

        # استعلام للحصول على العقود الجديدة بعد آخر معرف تم إرساله
        query = """
        SELECT
            ct.ContractId, ct.ContractCode, ct.Date AS ContractDate, ct.Notes, ct.BranchId,
            ISNULL(branch.NameAr, 'فرع غير محدد') AS BranchName,
            c.CustomerCode, c.NameAr AS CustomerName, c.MainPhoneNo, c.Address,
            ISNULL(city.NameAr, 'غير محدد') AS CityName,
            ISNULL(addUser.UserName, 'غير محدد') AS AddedByUser,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            ct.AddDate
        FROM Acc_Contracts ct
        INNER JOIN Acc_Customers c ON ct.CustomerId = c.CustomerId
        LEFT JOIN Sys_Branches branch ON ct.BranchId = branch.BranchId
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON ct.AddUser = addUser.UserId
        WHERE ct.IsDeleted = 0 AND c.IsDeleted = 0 AND c.BranchId = ? AND ct.ContractId > ?
        ORDER BY ct.ContractId ASC
        """

        # فحص كل فرع منفصل
        notifications_sent = 0
        # الحصول على جميع الفروع من قاعدة البيانات تلقائ
        cursor.execute("SELECT DISTINCT BranchId FROM Acc_Customers WHERE IsDeleted = 0 AND BranchId IS NOT NULL ORDER BY BranchId")
        branches = [row[0] for row in cursor.fetchall()]
        print(f"🏢 فحص العقود في الفروع: {branches}")

        for branch_id in branches:
            # الحصول على آخر معرف تم إرساله لهذا الفرع
            last_sent_id = get_last_sent_contract_id(branch_id)
            print(f"📊 آخر معرف عقد تم إرساله للفرع {branch_id}: {last_sent_id}")

            # تنفيذ الاستعلام للحصول على العقود الجديدة بعد آخر معرف
            cursor.execute(query, (branch_id, last_sent_id))
            new_contracts = cursor.fetchall()

            # معالجة كل عقد جديد في هذا الفرع
            for contract in new_contracts:

                # تحديد أيقونة الفرع
                branch_icon = "🏢" if contract[4] == 2 else "🏬" if contract[4] == 3 else "🏪"

                message = f"""📄 **عقد جديد تم إضافته**

{branch_icon} **الفرع:** {contract[5]}

🔢 **كود العقد:** {contract[1]}
📅 **تاريخ العقد:** {contract[2].strftime('%Y-%m-%d %H:%M') if contract[2] else 'غير محدد'}
📝 **ملاحظات العقد:** {contract[3] or 'لا توجد'}

👤 **بيانات العميل:**
🔢 **كود العميل:** {contract[6]}
👤 **اسم العميل:** {contract[7] or 'غير محدد'}
📱 **هاتف العميل:** {contract[8] or 'غير محدد'}
🏠 **عنوان العميل:** {contract[9] or 'غير محدد'}
🌍 **المدينة/المنطقة:** {contract[10] or 'غير محدد'}

👨‍💼 **تم الإنشاء بواسطة:** {contract[12] or 'غير محدد'}
📅 **تاريخ الإضافة:** {contract[13].strftime('%Y-%m-%d %H:%M') if contract[13] else 'غير محدد'}"""

                # حفظ في قاعدة البيانات
                save_notification_to_db(
                    'عقد',
                    contract[1],  # ContractCode
                    f"عقد للعميل {contract[7] or 'غير محدد'}",
                    contract[4],  # BranchId
                    contract[5],  # BranchName
                    contract[0],  # ContractId (المعرف الفريد)
                    contract[13], # AddDate
                    contract[12] or 'غير محدد'  # AddedByFullName
                )

                # إرسال الإشعار
                send_notification_sync(message)
                notifications_sent += 1
                print(f"✅ تم إرسال إشعار للعقد {contract[1]} (ID: {contract[0]})")

        conn.close()

        if notifications_sent > 0:
            print(f"📊 تم إرسال {notifications_sent} إشعار عقد جديد")
            return None  # لا نرسل رسالة إضافية لأن كل عقد أرسل إشعار منفصل

        return None

    except Exception as e:
        error_msg = f"❌ خطأ في فحص العقود الجديدة: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

# تم حذف دالة initialize_last_ids() غير المستخدمة

async def monitor_all_data():
    """مراقبة جميع البيانات مع التفاصيل الشاملة"""
    start_msg = "🔍 بدء مراقبة شاملة لجميع البيانات..."
    print(start_msg)
    print("📊 سيتم عرض تفاصيل كاملة مع الربط بجميع الجداول")
    print("⚠️ قراءة فقط - لا يتم كتابة أي بيانات")

    if logger:
        logger.info(start_msg)
        logger.info("📊 سيتم عرض تفاصيل كاملة مع الربط بجميع الجداول")

    # تهيئة قاعدة بيانات الإشعارات
    init_notifications_db()

    # 🧪 اختبار تقرير العميل عند بدء المراقبة
    print("🧪 اختبار تقرير العقود والمدفوعات...")
    try:
        test_customer_code = "1213"  # يمكن تغيير كود العميل هنا
        print(f"📋 إرسال تقرير تجريبي للعميل: {test_customer_code}")

        # إرسال التقرير
        result = send_customer_contracts_report(test_customer_code)

        if result:
            print("✅ تم إرسال تقرير العميل التجريبي بنجاح")
            if logger:
                logger.info(f"تم إرسال تقرير العميل التجريبي {test_customer_code}")
        else:
            print("⚠️ لم يتم إرسال تقرير العميل التجريبي")

    except Exception as e:
        error_msg = f"❌ خطأ في اختبار تقرير العميل: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)

    cycle = 0

    while True:
        try:
            cycle += 1
            current_time = datetime.now().strftime('%H:%M:%S')
            cycle_msg = f"🔄 دورة مراقبة شاملة #{cycle} - {current_time}"
            print(f"\n{cycle_msg}")

            if logger and cycle % 10 == 1:  # كل 10 دورات
                logger.info(cycle_msg)

            # فحص العملاء الجدد
            new_customer = check_new_customers()
            if new_customer:
                discovery_msg = "🔔 تم اكتشاف عميل جديد!"
                print(discovery_msg)
                if logger:
                    logger.info(discovery_msg)
                await send_notification(new_customer)

            # فحص المعاينات الجديدة
            new_preview = check_new_previews()
            if new_preview:
                discovery_msg = "🔔 تم اكتشاف معاينة جديدة!"
                print(discovery_msg)
                if logger:
                    logger.info(discovery_msg)
                await send_notification(new_preview)

            # فحص الاجتماعات الجديدة
            new_meeting = check_new_meetings()
            if new_meeting:
                discovery_msg = "🔔 تم اكتشاف اجتماع جديد!"
                print(discovery_msg)
                if logger:
                    logger.info(discovery_msg)
                await send_notification(new_meeting)

            # فحص التصميمات الجديدة
            new_design = check_new_designs()
            if new_design:
                discovery_msg = "🔔 تم اكتشاف تصميم جديد!"
                print(discovery_msg)
                if logger:
                    logger.info(discovery_msg)
                await send_notification(new_design)

            # فحص العقود الجديدة
            new_contract = check_new_contracts()
            if new_contract:
                discovery_msg = "🔔 تم اكتشاف عقد جديد!"
                print(discovery_msg)
                if logger:
                    logger.info(discovery_msg)
                await send_notification(new_contract)

            # فحص التقارير المجدولة (كل دورة للدقة في التوقيت)
            try:
                check_scheduled_reports()
                # إضافة فحص تقارير التصميمات
                check_and_send_designs_without_files_report()

                # فحص تحديثات العقود والمدفوعات
                updates_count = check_contracts_and_payments_updates()
                if updates_count and updates_count > 0:
                    print(f"📊 تم إرسال {updates_count} تحديث للعقود والمدفوعات")

            except Exception as e:
                error_msg = f"❌ خطأ في فحص التقارير المجدولة: {str(e)}"
                print(error_msg)
                if logger:
                    logger.error(error_msg)

            if not any([new_customer, new_preview, new_meeting, new_design, new_contract]):
                no_data_msg = "📊 لا توجد بيانات جديدة"
                print(no_data_msg)
                if logger and cycle % 20 == 0:  # كل 20 دورة
                    logger.debug(no_data_msg)

            # انتظار 30 ثانية
            await asyncio.sleep(30)

        except KeyboardInterrupt:
            stop_msg = "\n⏹️ تم إيقاف المراقبة"
            print(stop_msg)
            if logger:
                logger.info(stop_msg)
            break
        except Exception as e:
            error_msg = f"❌ خطأ في المراقبة: {str(e)}"
            print(error_msg)
            if logger:
                logger.error(error_msg)
            await asyncio.sleep(60)

# دوال البوت
async def start(update, context):
    """دالة البداية"""
    if not update.effective_user or not update.message:
        return

    user_id = update.effective_user.id
    user_name = update.effective_user.first_name or "مستخدم"

    welcome_text = f"""🌟 **مرحباً بك في Terra Bot المحسن** 🌟

👋 أهلاً {user_name}!

🔔 **نظام الإشعارات التلقائية نشط**
📊 **مراقبة قاعدة البيانات مستمرة**
📈 **نظام التقارير المجدولة نشط**

💡 **الأوامر المتاحة:**
/start - العودة للقائمة الرئيسية
/test - اختبار قاعدة البيانات
/id - عرض معرف المستخدم/الجروب
/recipients - عرض قائمة مستقبلي الإشعارات
/reports - عرض التقارير المجدولة
/testreport - إرسال تقرير تجريبي
/e - التقارير الإحصائية المحفوظة (مثال: /e أو /e 1)
/manage_reports - إدارة التقارير الإحصائية
/d - إحصائيات التصميمات الغير مرفوعة
/de - تفاصيل التصميمات الغير مرفوعة

⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

    await update.message.reply_text(
        welcome_text,
        parse_mode='Markdown'
    )

async def get_id(update, context):
    """دالة عرض معرف المستخدم أو الجروب"""
    if not update.effective_user or not update.message:
        return

    try:
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        chat_type = update.effective_chat.type

        # معلومات المستخدم
        user_info = f"👤 **معلومات المستخدم:**\n"
        user_info += f"🆔 **معرف المستخدم:** `{user_id}`\n"
        user_info += f"👤 **الاسم:** {update.effective_user.first_name or 'غير محدد'}"

        if update.effective_user.last_name:
            user_info += f" {update.effective_user.last_name}"

        if update.effective_user.username:
            user_info += f"\n📝 **اسم المستخدم:** @{update.effective_user.username}"

        # معلومات المحادثة
        chat_info = f"\n\n💬 **معلومات المحادثة:**\n"
        chat_info += f"🆔 **معرف المحادثة:** `{chat_id}`\n"

        if chat_type == "private":
            chat_info += f"📱 **نوع المحادثة:** محادثة خاصة"
        elif chat_type == "group":
            chat_info += f"👥 **نوع المحادثة:** جروب"
            if update.effective_chat.title:
                chat_info += f"\n🏷️ **اسم الجروب:** {update.effective_chat.title}"
        elif chat_type == "supergroup":
            chat_info += f"👥 **نوع المحادثة:** سوبر جروب"
            if update.effective_chat.title:
                chat_info += f"\n🏷️ **اسم الجروب:** {update.effective_chat.title}"
        elif chat_type == "channel":
            chat_info += f"📢 **نوع المحادثة:** قناة"
            if update.effective_chat.title:
                chat_info += f"\n🏷️ **اسم القناة:** {update.effective_chat.title}"

        # ملاحظة للإشعارات
        notification_note = f"\n\n🔔 **للإشعارات التلقائية:**\n"
        if chat_type == "private":
            notification_note += f"استخدم هذا المعرف: `{user_id}`"
        else:
            notification_note += f"استخدم هذا المعرف: `{chat_id}`"

        notification_note += f"\n\n💡 **ملاحظة:** أرسل هذا المعرف للمطور لإضافتك لقائمة الإشعارات التلقائية"

        full_message = user_info + chat_info + notification_note

        await update.message.reply_text(
            full_message,
            parse_mode='Markdown'
        )

        print(f"📨 طلب معرف من: {update.effective_user.first_name or 'مستخدم'} - معرف المستخدم: {user_id}, معرف المحادثة: {chat_id}")

    except Exception as e:
        print(f"❌ خطأ في دالة get_id: {str(e)}")
        await update.message.reply_text(
            "❌ حدث خطأ في الحصول على المعرف",
            parse_mode='Markdown'
        )

async def test_db(update, context):
    """اختبار قاعدة البيانات"""
    if not update.effective_user or not update.message:
        return

    await update.message.reply_text("🔄 جاري اختبار قاعدة البيانات...")

    try:
        conn = connect_to_db()
        if not conn:
            await update.message.reply_text("❌ فشل الاتصال بقاعدة البيانات")
            return

        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM Acc_Customers WHERE IsDeleted=0")
        result = cursor.fetchone()
        total_customers = result[0] if result else 0
        conn.close()

        report = f"""🔧 **معلومات قاعدة البيانات:**

🖥️ **الخادم:** `{MAIN_DB_CONFIG['server']}`
🗄️ **قاعدة البيانات الأصلية:** `{MAIN_DB_CONFIG['database']}`
🗄️ **قاعدة بيانات الإشعارات:** `{NOTIFICATIONS_DB_CONFIG['database']}`
👥 **إجمالي العملاء:** {total_customers:,} عميل

✅ **الاتصال سليم**"""

        await update.message.reply_text(report, parse_mode='Markdown')

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في فحص قاعدة البيانات: {str(e)}")

async def manage_recipients(update, context):
    """إدارة قائمة المستقبلين للإشعارات"""
    if not update.effective_user or not update.message:
        return

    try:
        conn = connect_to_notifications_db()
        if not conn:
            await update.message.reply_text("❌ لا يمكن الاتصال بقاعدة بيانات الإشعارات")
            return

        cursor = conn.cursor()
        cursor.execute("""
            SELECT ChatId, RecipientName, RecipientType, IsActive
            FROM Notification_Recipients
            ORDER BY RecipientType, RecipientName
        """)

        results = cursor.fetchall()
        conn.close()

        if results:
            report = "📋 **قائمة مستقبلي الإشعارات:**\n\n"

            active_count = 0
            inactive_count = 0

            for row in results:
                chat_id, name, recipient_type, is_active = row
                status = "✅ نشط" if is_active else "❌ معطل"
                icon = "👤" if recipient_type == 'User' else "👥"

                report += f"{icon} **{name}**\n"
                report += f"🆔 المعرف: `{chat_id}`\n"
                report += f"📊 الحالة: {status}\n\n"

                if is_active:
                    active_count += 1
                else:
                    inactive_count += 1

            report += f"📊 **الإحصائيات:**\n"
            report += f"✅ نشط: {active_count}\n"
            report += f"❌ معطل: {inactive_count}\n"
            report += f"📋 الإجمالي: {len(results)}"

        else:
            report = "⚠️ لا توجد مستقبلين مسجلين في قاعدة البيانات"

        await update.message.reply_text(report, parse_mode='Markdown')

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في عرض المستقبلين: {str(e)}")

async def show_scheduled_reports(update, context):
    """عرض التقارير المجدولة"""
    if not update.effective_user or not update.message:
        return

    try:
        conn = connect_to_notifications_db()
        if not conn:
            await update.message.reply_text("❌ لا يمكن الاتصال بقاعدة بيانات الإشعارات")
            return

        cursor = conn.cursor()

        # التحقق من وجود الجداول الجديدة
        try:
            cursor.execute("SELECT COUNT(*) FROM Report_Schedules WHERE 1=0")
            use_new_structure = True
        except:
            use_new_structure = False

        if use_new_structure:
            cursor.execute("""
                SELECT
                    s.ScheduleId,
                    s.ScheduleName,
                    t.ReportNameAr,
                    s.ScheduleType,
                    s.IntervalValue,
                    s.DaysToInclude,
                    s.BranchIds,
                    s.IsActive,
                    s.LastRunTime,
                    s.NextRunTime,
                    s.Notes,
                    t.ReportCode
                FROM Report_Schedules s
                INNER JOIN Report_Types t ON s.ReportTypeId = t.ReportTypeId
                ORDER BY s.NextRunTime
            """)
        else:
            cursor.execute("""
                SELECT ReportId, ReportName, ReportType, ScheduleType, IntervalValue,
                       DaysToInclude, BranchIds, IsActive, LastRunTime, NextRunTime, Notes, ReportType
                FROM Scheduled_Reports
                ORDER BY NextRunTime
            """)

        results = cursor.fetchall()
        conn.close()

        if results:
            report = "📊 **التقارير المجدولة:**\n\n"

            for row in results:
                if use_new_structure:
                    schedule_id, schedule_name, report_type_name, schedule_type, interval_value = row[0:5]
                    days_include, branch_ids, is_active, last_run, next_run, notes, report_code = row[5:12]
                    display_name = schedule_name
                    type_info = f"{report_type_name} ({report_code})"
                else:
                    report_id, report_name, report_type, schedule_type, interval_value = row[0:5]
                    days_include, branch_ids, is_active, last_run, next_run, notes, _ = row[5:12]
                    schedule_id = report_id
                    display_name = report_name
                    type_info = report_type

                status = "✅ نشط" if is_active else "❌ معطل"

                # تنسيق نوع الجدولة
                if schedule_type == 'hourly':
                    schedule_text = f"كل {interval_value} ساعة"
                elif schedule_type == 'daily':
                    schedule_text = f"كل {interval_value} يوم"
                elif schedule_type == 'weekly':
                    schedule_text = f"كل {interval_value} أسبوع"
                elif schedule_type == 'monthly':
                    schedule_text = f"كل {interval_value} شهر"
                else:
                    schedule_text = schedule_type

                # تنسيق الفروع
                if branch_ids:
                    branches_text = f"الفروع: {branch_ids}"
                else:
                    branches_text = "جميع الفروع"

                # تنسيق آخر تشغيل
                last_run_text = last_run.strftime('%Y-%m-%d %H:%M') if last_run else "لم يتم التشغيل بعد"
                next_run_text = next_run.strftime('%Y-%m-%d %H:%M') if next_run else "غير محدد"

                report += f"""🔹 **{display_name}** (ID: {schedule_id})
├─ 📊 النوع: {type_info}
├─ ⏰ الجدولة: {schedule_text}
├─ 📅 فترة البيانات: آخر {days_include} أيام
├─ 🏢 {branches_text}
├─ 📊 الحالة: {status}
├─ ⏮️ آخر تشغيل: {last_run_text}
└─ ⏭️ التشغيل التالي: {next_run_text}

"""

            report += f"📋 **الإجمالي:** {len(results)} تقرير مجدول"

        else:
            report = "⚠️ لا توجد تقارير مجدولة"

        await update.message.reply_text(report, parse_mode='Markdown')

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في عرض التقارير المجدولة: {str(e)}")

async def test_report(update, context):
    """اختبار إرسال تقرير إحصائي"""
    if not update.effective_user or not update.message:
        return

    await update.message.reply_text("🔄 جاري إنشاء تقرير تجريبي...")

    try:
        # إنشاء تقرير تجريبي لآخر يوم
        data = get_statistics_report(days=1)
        if data:
            formatted_report = format_statistics_report(data, 1, "تقرير تجريبي - آخر 24 ساعة")
            await update.message.reply_text(formatted_report, parse_mode='Markdown')
        else:
            await update.message.reply_text("❌ لا توجد بيانات للتقرير التجريبي")

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في إنشاء التقرير التجريبي: {str(e)}")

async def statistics_report(update, context):
    """إرسال تقرير إحصائي - الأمر /e"""
    if not update.effective_user or not update.message:
        return

    await update.message.reply_text("📊 جاري البحث عن التقارير المتاحة...")

    try:
        # الحصول على التقارير المتاحة من قاعدة البيانات
        conn = connect_to_notifications_db()
        if not conn:
            await update.message.reply_text("❌ خطأ في الاتصال بقاعدة البيانات")
            return

        cursor = conn.cursor()

        # إذا لم يتم تحديد معاملات، عرض قائمة التقارير المتاحة
        if not context.args:
            cursor.execute("""
                SELECT ReportId, ReportName, StatisticsPeriodDays,
                       CASE WHEN BranchIds IS NULL THEN N'جميع الفروع' ELSE BranchIds END as Branches,
                       CASE WHEN IsActive = 1 THEN N'نشط' ELSE N'معطل' END as Status
                FROM Statistical_Reports_Config
                ORDER BY ReportId
            """)

            reports = cursor.fetchall()
            if reports:
                report_list = "📋 **التقارير الإحصائية المتاحة:**\n\n"
                for report in reports:
                    report_list += f"🔹 **{report[0]}** - {report[1]}\n"
                    report_list += f"   📅 فترة: {report[2]} أيام | 🏢 فروع: {report[3]} | ⚡ {report[4]}\n\n"

                report_list += "💡 **لتشغيل تقرير:** `/e [رقم_التقرير]`\n"
                report_list += "مثال: `/e 1` أو `/e 2`"

                await update.message.reply_text(report_list, parse_mode='Markdown')
            else:
                await update.message.reply_text("❌ لا توجد تقارير محفوظة في النظام")

            conn.close()
            return

        # إذا تم تحديد رقم التقرير
        try:
            report_id = int(context.args[0])
        except ValueError:
            await update.message.reply_text("⚠️ يرجى إدخال رقم التقرير صحيح\n\nمثال: `/e 1`")
            conn.close()
            return

        # البحث عن التقرير المحدد
        cursor.execute("""
            SELECT ReportName, StatisticsPeriodDays, BranchIds, IsActive
            FROM Statistical_Reports_Config
            WHERE ReportId = ? AND IsActive = 1
        """, (report_id,))

        report_config = cursor.fetchone()
        conn.close()

        if not report_config:
            await update.message.reply_text(f"❌ التقرير رقم {report_id} غير موجود أو معطل")
            return

        report_name = report_config[0]
        days = report_config[1]
        branch_ids = report_config[2]

        await update.message.reply_text(f"📊 جاري إنشاء {report_name}...")

        # إنشاء التقرير
        data = get_statistics_report(days=days, branch_ids=branch_ids)
        if data:
            formatted_report = format_statistics_report(data, days, report_name, branch_ids)

            # تقسيم الرسالة إذا كانت طويلة
            if len(formatted_report) > 4000:
                # تقسيم الرسالة
                parts = []
                current_part = ""
                lines = formatted_report.split('\n')

                for line in lines:
                    if len(current_part + line + '\n') > 4000:
                        if current_part:
                            parts.append(current_part)
                            current_part = line + '\n'
                        else:
                            parts.append(line)
                    else:
                        current_part += line + '\n'

                if current_part:
                    parts.append(current_part)

                # إرسال الأجزاء
                for i, part in enumerate(parts):
                    if i == 0:
                        await update.message.reply_text(part, parse_mode='Markdown')
                    else:
                        await update.message.reply_text(f"📊 **تكملة التقرير ({i+1}/{len(parts)}):**\n\n{part}", parse_mode='Markdown')
            else:
                await update.message.reply_text(formatted_report, parse_mode='Markdown')
        else:
            await update.message.reply_text("❌ لا توجد بيانات للتقرير الإحصائي")

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في إنشاء التقرير الإحصائي: {str(e)}")

async def manage_reports(update, context):
    """إدارة التقارير الإحصائية - الأمر /manage_reports"""
    if not update.effective_user or not update.message:
        return

    try:
        conn = connect_to_notifications_db()
        if not conn:
            await update.message.reply_text("❌ خطأ في الاتصال بقاعدة البيانات")
            return

        cursor = conn.cursor()

        if not context.args:
            # عرض قائمة الإدارة
            help_text = """🔧 **إدارة التقارير الإحصائية:**

📋 **عرض جميع التقارير:**
`/manage_reports list`

➕ **إضافة تقرير جديد:**
`/manage_reports add "اسم التقرير" كود_التقرير أيام_الإحصائيات ساعة:دقيقة [فروع]`
مثال: `/manage_reports add "تقرير يومي مخصص" CUSTOM_DAILY 5 14:30 2,3`

✏️ **تعديل تقرير:**
`/manage_reports edit رقم_التقرير "الاسم_الجديد" أيام_جديدة ساعة:دقيقة [فروع]`

🗑️ **حذف تقرير:**
`/manage_reports delete رقم_التقرير`

⚡ **تفعيل/تعطيل تقرير:**
`/manage_reports toggle رقم_التقرير`

🔄 **تشغيل تقرير فوري:**
`/manage_reports run رقم_التقرير`"""

            await update.message.reply_text(help_text, parse_mode='Markdown')
            conn.close()
            return

        command = context.args[0].lower()

        if command == "list":
            # عرض جميع التقارير
            cursor.execute("""
                SELECT ReportId, ReportName, ReportCode, StatisticsPeriodDays,
                       SendTime, BranchIds, IsActive, LastSentTime, NextSendTime
                FROM Statistical_Reports_Config
                ORDER BY ReportId
            """)

            reports = cursor.fetchall()
            if reports:
                report_text = "📋 **جميع التقارير الإحصائية:**\n\n"
                for report in reports:
                    status = "🟢 نشط" if report[6] else "🔴 معطل"
                    branches = report[5] if report[5] else "جميع الفروع"
                    last_sent = report[7].strftime('%Y-%m-%d %H:%M') if report[7] else "لم يرسل بعد"
                    next_send = report[8].strftime('%Y-%m-%d %H:%M') if report[8] else "غير محدد"

                    report_text += f"🔹 **{report[0]}** - {report[1]}\n"
                    report_text += f"   📝 كود: {report[2]} | 📅 فترة: {report[3]} أيام\n"
                    report_text += f"   ⏰ وقت الإرسال: {report[4]} | 🏢 فروع: {branches}\n"
                    report_text += f"   {status} | 📤 آخر إرسال: {last_sent}\n"
                    report_text += f"   🔄 الإرسال القادم: {next_send}\n\n"

                await update.message.reply_text(report_text, parse_mode='Markdown')
            else:
                await update.message.reply_text("❌ لا توجد تقارير في النظام")

        conn.close()

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في إدارة التقارير: {str(e)}")


def get_designs_without_files_report():
    """الحصول على تقرير التصميمات الغير مرفوعة"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()
        query = """
        SELECT 
            b.NameAr AS BranchName,
            c.NameAr AS CustomerName,
            e.NameAr AS EngineerName,
            c.CustomerCode,
            CAST(DATEDIFF(DAY, d.Date, GETDATE()) AS NVARCHAR) + N' يوم' AS DaysSinceDesign
        FROM Sys_Designs d
        LEFT JOIN Acc_Customers c ON d.CustomerId = c.CustomerId
        LEFT JOIN Emp_Employees e ON d.DesignChairperson = e.EmployeeId
        LEFT JOIN Sys_Branches b ON d.BranchId = b.BranchId
        LEFT JOIN (
            SELECT DISTINCT DesignId
            FROM Sys_Files
            WHERE IsDeleted = 0 AND DesignId IS NOT NULL
        ) f ON d.DesignId = f.DesignId
        WHERE d.IsDeleted = 0
          AND f.DesignId IS NULL
          AND b.BranchId IN (2, 3)
        ORDER BY b.BranchId, d.Date
        """
        cursor.execute(query)
        results = cursor.fetchall()
        conn.close()
        return results
    except Exception as e:
        print(f"❌ خطأ في الحصول على تقرير التصميمات الغير مرفوعة: {str(e)}")
        if logger:
            logger.error(f"خطأ في الحصول على تقرير التصميمات الغير مرفوعة: {str(e)}")
        return None

def format_designs_without_files_report(data):
    """تنسيق تقرير التصميمات الغير مرفوعة للإرسال مرتب حسب المهندسين"""
    if not data:
        return "ℹ️ لا توجد تصميمات غير مرفوعة"
    
    # تجميع البيانات حسب المهندسين
    engineers_data = {}
    branch_stats = {}
    total_count = 0
    
    for row in data:
        branch_name = row[0] or 'فرع غير محدد'
        customer_name = row[1] or 'غير محدد'
        engineer_name = row[2] or 'غير محدد'
        customer_code = row[3] or 'غير محدد'
        days_since = row[4] or 'غير محدد'
        
        # مفتاح فريد للمهندس (فرع + اسم)
        engineer_key = f"{branch_name}|{engineer_name}"
        
        if engineer_key not in engineers_data:
            engineers_data[engineer_key] = {
                'branch_name': branch_name,
                'engineer_name': engineer_name,
                'designs': []
            }
        
        engineers_data[engineer_key]['designs'].append({
            'customer_name': customer_name,
            'customer_code': customer_code,
            'days_since': days_since
        })
        
        # إحصائيات الفروع
        if branch_name not in branch_stats:
            branch_stats[branch_name] = 0
        branch_stats[branch_name] += 1
        total_count += 1
    
    # ترتيب المهندسين حسب عدد التصميمات (الأكثر أولاً)
    sorted_engineers = sorted(engineers_data.values(), 
                            key=lambda x: len(x['designs']), reverse=True)
    
    report = f"""🎨 *تقرير التصميمات الغير مرفوعة*
📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}
📊 العدد الإجمالي: {total_count} تصميم

═══════════════════════════════════

📈 *ملخص سريع:*
"""
    
    # عرض ملخص الفروع
    for branch_name, count in sorted(branch_stats.items()):
        branch_icon = "🏢" if "نصر" in branch_name else "🏬" if "تجمع" in branch_name else "🏪"
        report += f"{branch_icon} {branch_name}: {count} تصميم\n"
    
    report += f"""
═══════════════════════════════════

📋 *التفاصيل مرتبة حسب المهندسين:*

"""
    
    # عرض كل مهندس وتصاميمه
    for engineer_data in sorted_engineers:
        branch_name = engineer_data['branch_name']
        engineer_name = engineer_data['engineer_name']
        designs = engineer_data['designs']
        
        branch_icon = "🏢" if "نصر" in branch_name else "🏬" if "تجمع" in branch_name else "🏪"
        
        report += f"{branch_icon} *{branch_name}*\n"
        report += f"👨‍💼 *{engineer_name}* - {len(designs)} تصميم:\n"
        report += "─────────────────────────────────\n"
        
        # عرض كل تصميمات هذا المهندس
        for i, design in enumerate(designs, 1):
            report += f"{i}. 👤 *{design['customer_name']}*\n"
            report += f"   🔢 الكود: {design['customer_code']}\n"
            report += f"   ⏰ منذ: {design['days_since']}\n\n"
        
        report += "═══════════════════════════════════\n\n"
    
    report += f"""📊 *الإجمالي العام:* {total_count} تصميم غير مرفوع
⚠️ *يرجى متابعة رفع الملفات للتصميمات المذكورة*"""

    return report
def check_and_send_designs_without_files_report():
    """فحص وإرسال تقرير التصميمات الغير مرفوعة حسب الجدولة"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return

        cursor = conn.cursor()
        current_time = datetime.now()

        # البحث عن تقارير التصميمات المجدولة والمستحقة من الجدول الصحيح
        cursor.execute("""
            SELECT ReportId, ReportName, ReportCode, ScheduleType, IntervalDays, 
                   NextSendTime, BranchIds, StatisticsPeriodDays, IsActive
            FROM Statistical_Reports_Config 
            WHERE IsActive = 1 
            AND (ReportCode = 'DESIGNS_WITHOUT_FILES' OR ReportCode = 'DESIGNS_WITHOUT_FILES_3DAYS')
            AND NextSendTime <= ?
            ORDER BY NextSendTime
        """, (current_time,))

        scheduled_reports = cursor.fetchall()

        for report in scheduled_reports:
            report_id, report_name, report_code, schedule_type, interval_days, next_send_time, branch_ids_str, statistics_period_days, is_active = report

            try:
                print(f"🎨 تنفيذ تقرير التصميمات المجدول: {report_name}")
                
                # الحصول على بيانات التصميمات الغير مرفوعة
                data = get_designs_without_files_report()
                if data:
                    # تنسيق التقرير مع إضافة نوع التقرير
                    if interval_days == 3:
                        title_suffix = " (تقرير كل 3 أيام)"
                    else:
                        title_suffix = " (تقرير يومي)"
                    
                    formatted_report = format_designs_statistics_only(data)
                    formatted_report = formatted_report.replace("إحصائيات التصميمات", f"إحصائيات التصميمات{title_suffix}")
                    
                    # إرسال التقرير
                    send_notification_sync(formatted_report)
                    
                    print(f"✅ تم إرسال تقرير التصميمات: {report_name}")
                    if logger:
                        logger.info(f"تم إرسال تقرير التصميمات المجدول: {report_name}")
                else:
                    print(f"ℹ️ لا توجد تصميمات غير مرفوعة للتقرير: {report_name}")

                # تحديث موعد التشغيل التالي باستخدام الدالة الموجودة
                update_statistical_report_status(report_id)

            except Exception as e:
                error_msg = f"❌ خطأ في تنفيذ تقرير التصميمات {report_name}: {str(e)}"
                print(error_msg)
                if logger:
                    logger.error(error_msg)

        conn.close()

    except Exception as e:
        error_msg = f"❌ خطأ في فحص تقارير التصميمات المجدولة: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)






async def designs_without_files_command(update, context):
    """أمر لإرسال إحصائيات التصميمات الغير مرفوعة"""
    try:
        if not update.effective_user or not update.message:
            return
            
        user_id = update.effective_user.id
        
        await update.message.reply_text("🔄 جاري إعداد إحصائيات التصميمات الغير مرفوعة...")

        data = get_designs_without_files_report()
        if data:
            formatted_report = format_designs_statistics_only(data)
            await update.message.reply_text(formatted_report, parse_mode='Markdown')
            print(f"✅ تم إرسال إحصائيات التصميمات للمستخدم {user_id}")
            if logger:
                logger.info(f"تم إرسال إحصائيات التصميمات للمستخدم {user_id}")
        else:
            await update.message.reply_text("ℹ️ لا توجد تصميمات غير مرفوعة")
    except Exception as e:
        error_msg = f"❌ خطأ في إرسال الإحصائيات: {str(e)}"
        await update.message.reply_text(error_msg)
        print(error_msg)
        if logger:
            logger.error(error_msg)

async def designs_without_files_detailed_command(update, context):
    """أمر لإرسال تفاصيل التصميمات الغير مرفوعة"""
    try:
        if not update.effective_user or not update.message:
            return
            
        user_id = update.effective_user.id
        
        await update.message.reply_text("🔄 جاري إعداد تفاصيل التصميمات الغير مرفوعة...")

        data = get_designs_without_files_report()
        if data:
            formatted_report = format_designs_without_files_report(data)
            
            # تقسيم الرسالة إذا كانت طويلة
            if len(formatted_report) > 4000:
                parts = []
                current_part = ""
                lines = formatted_report.split('\n')

                for line in lines:
                    if len(current_part + line + '\n') > 4000:
                        if current_part:
                            parts.append(current_part)
                            current_part = line + '\n'
                        else:
                            parts.append(line)
                    else:
                        current_part += line + '\n'

                if current_part:
                    parts.append(current_part)

                # إرسال الأجزاء
                for i, part in enumerate(parts):
                    if i == 0:
                        await update.message.reply_text(part, parse_mode='Markdown')
                    else:
                        await update.message.reply_text(f"🎨 **تكملة التقرير ({i+1}/{len(parts)}):**\n\n{part}", parse_mode='Markdown')
            else:
                await update.message.reply_text(formatted_report, parse_mode='Markdown')
                
            print(f"✅ تم إرسال تفاصيل التصميمات للمستخدم {user_id}")
            if logger:
                logger.info(f"تم إرسال تفاصيل التصميمات للمستخدم {user_id}")
        else:
            await update.message.reply_text("ℹ️ لا توجد تصميمات غير مرفوعة")
    except Exception as e:
        error_msg = f"❌ خطأ في إرسال التفاصيل: {str(e)}"
        await update.message.reply_text(error_msg)
        print(error_msg)
        if logger:
            logger.error(error_msg)

def format_designs_statistics_only(data):
    """تنسيق إحصائيات التصميمات الغير مرفوعة فقط"""
    if not data:
        return "ℹ️ لا توجد تصميمات غير مرفوعة"
    
    # حساب إحصائيات المهندسين
    engineers_stats = {}
    branch_stats = {}
    
    for row in data:
        branch_name = row[0] or 'فرع غير محدد'
        engineer_name = row[2] or 'غير محدد'
        
        # إحصائيات المهندسين حسب الفرع
        if branch_name not in engineers_stats:
            engineers_stats[branch_name] = {}
        
        if engineer_name not in engineers_stats[branch_name]:
            engineers_stats[branch_name][engineer_name] = 0
        
        engineers_stats[branch_name][engineer_name] += 1
        
        # إحصائيات الفروع
        if branch_name not in branch_stats:
            branch_stats[branch_name] = 0
        branch_stats[branch_name] += 1
    
    report = f"""🎨 *إحصائيات التصميمات الغير مرفوعة*
📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}
📊 العدد الإجمالي: {len(data)} تصميم

═══════════════════════════════════

📈 *إحصائيات المهندسين:*

"""

    # عرض إحصائيات المهندسين حسب الفرع
    for branch_name, engineers in engineers_stats.items():
        branch_icon = "🏢" if "نصر" in branch_name else "🏬" if "تجمع" in branch_name else "🏪"
        report += f"{branch_icon} *{branch_name}*\n"
        
        # ترتيب المهندسين حسب عدد التصميمات (الأكثر أولاً)
        sorted_engineers = sorted(engineers.items(), key=lambda x: x[1], reverse=True)
        
        for engineer_name, count in sorted_engineers:
            report += f"👨‍💼 {engineer_name}: {count} تصميم\n"
        
        report += f"📊 إجمالي الفرع: {branch_stats[branch_name]} تصميم\n\n"

    report += f"""═══════════════════════════════════
📊 *الإجمالي العام:* {len(data)} تصميم غير مرفوع

💡 *للحصول على التفاصيل الكاملة استخدم:* /de"""

    return report

async def customer_report_command(update, context):
    """أمر إرسال تقرير العقود والمدفوعات مع أزرار تفاعلية"""
    if not update.effective_user or not update.message:
        return

    try:
        # التحقق من وجود معامل (كود العميل)
        if not context.args:
            # إرسال أزرار تفاعلية
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup

            keyboard = [
                [InlineKeyboardButton("👤 عميل معين", callback_data="report_single_customer")],
                [InlineKeyboardButton("👥 كل عملاء فرع", callback_data="report_all_customers")],
                [InlineKeyboardButton("⚡ عميل 1213 (سريع)", callback_data="customer_quick_1213")],
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            help_text = """📋 **تقارير العقود والمدفوعات**

🎯 **اختر نوع التقرير:**

👤 **عميل معين**: تقرير مفصل لعميل واحد
👥 **كل عملاء فرع**: تقارير جميع العملاء في فرع محدد
⚡ **سريع**: تقرير العميل 1213 مباشرة

📝 **أو استخدم الأمر مباشرة:**
`/customer [كود العميل]`

📊 **محتوى التقرير:**
• ملخص جميع العقود
• تفاصيل المدفوعات مع الرصيد التراكمي
• الأقساط المستحقة مع حالة التأخير
• إحصائيات عامة ونسبة السداد"""

            await update.message.reply_text(
                help_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )
            return

        # الحصول على كود العميل
        customer_code = context.args[0].strip()

        if not customer_code:
            await update.message.reply_text("❌ يجب تحديد كود العميل")
            return

        # إرسال رسالة تأكيد بدء العملية
        await update.message.reply_text(f"🔍 جاري إنشاء تقرير للعميل: {customer_code}")

        # الحصول على التقرير (بدون إرسال للجميع)
        report_text = get_customer_contracts_report_text(customer_code)

        # إرسال التقرير في المحادثة الحالية فقط
        await update.message.reply_text(report_text, parse_mode='Markdown')

    except Exception as e:
        error_msg = f"❌ خطأ في أمر تقرير العميل: {str(e)}"
        await update.message.reply_text(error_msg)
        if logger:
            logger.error(error_msg)

async def customer_button_callback(update, context):
    """معالج أزرار تقرير العميل"""
    query = update.callback_query
    await query.answer()

    try:
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup

        if query.data == "report_single_customer":
            # طلب كود العميل
            keyboard = [
                [InlineKeyboardButton("عميل 1213", callback_data="customer_quick_1213")],
                [InlineKeyboardButton("عميل 1000", callback_data="customer_quick_1000")],
                [InlineKeyboardButton("🔙 رجوع", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                "👤 **تقرير عميل معين**\n\n"
                "🔍 اختر عميل أو أرسل كود العميل:\n"
                "مثال: `/customer 1213`\n\n"
                "📝 أو اختر من الأزرار:",
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        elif query.data == "report_all_customers":
            # اختيار الفرع
            keyboard = [
                [InlineKeyboardButton("🏢 فرع مدينة نصر", callback_data="branch_2")],
                [InlineKeyboardButton("🏬 فرع التجمع", callback_data="branch_3")],
                [InlineKeyboardButton("🔙 رجوع", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                "👥 **تقارير كل عملاء فرع**\n\n"
                "🏢 اختر الفرع المطلوب:\n\n"
                "⚠️ **تنبيه**: سيتم إرسال تقرير منفصل لكل عميل",
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        elif query.data.startswith("customer_quick_"):
            # تقرير سريع لعميل معين
            customer_code = query.data.replace("customer_quick_", "")

            await query.edit_message_text(f"🔍 جاري إنشاء تقرير للعميل: {customer_code}")

            # الحصول على التقرير
            report_text = get_customer_contracts_report_text(customer_code)

            # إرسال التقرير في المحادثة الحالية فقط
            await query.message.reply_text(report_text, parse_mode='Markdown')

        elif query.data.startswith("branch_"):
            # تقارير جميع عملاء فرع
            branch_id = int(query.data.replace("branch_", ""))
            branch_name = "مدينة نصر" if branch_id == 2 else "التجمع"

            # تأكيد الإرسال
            keyboard = [
                [InlineKeyboardButton("✅ نعم، أرسل التقارير", callback_data=f"confirm_branch_{branch_id}")],
                [InlineKeyboardButton("❌ إلغاء", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                f"⚠️ **تأكيد إرسال تقارير فرع {branch_name}**\n\n"
                "سيتم إرسال تقرير منفصل لكل عميل في الفرع\n"
                "قد يستغرق هذا عدة دقائق\n\n"
                "هل تريد المتابعة؟",
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        elif query.data.startswith("confirm_branch_"):
            # تأكيد إرسال تقارير الفرع
            branch_id = int(query.data.replace("confirm_branch_", ""))
            branch_name = "مدينة نصر" if branch_id == 2 else "التجمع"

            await query.edit_message_text(f"🔄 جاري إرسال تقارير فرع {branch_name}...")

            # إرسال التقارير (هذا سيستغرق وقت)
            result = send_all_customers_reports_by_branch(branch_id)

            await query.message.reply_text(f"✅ {result}")

        elif query.data == "back_to_main":
            # العودة للقائمة الرئيسية
            keyboard = [
                [InlineKeyboardButton("👤 عميل معين", callback_data="report_single_customer")],
                [InlineKeyboardButton("👥 كل عملاء فرع", callback_data="report_all_customers")],
                [InlineKeyboardButton("⚡ عميل 1213 (سريع)", callback_data="customer_quick_1213")],
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                "📋 **تقارير العقود والمدفوعات**\n\n"
                "🎯 اختر نوع التقرير:",
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

    except Exception as e:
        error_msg = f"❌ خطأ في معالج أزرار العميل: {str(e)}"
        await query.edit_message_text(error_msg)
        if logger:
            logger.error(error_msg)

async def main():
    """الدالة الرئيسية للبوت"""
    print("🚀 بدء تشغيل Terra Bot المحسن مع الإشعارات")
    print("=" * 50)

    try:
        from telegram.ext import Application, CommandHandler

        # إنشاء التطبيق
        app = Application.builder().token(BOT_TOKEN).build()

        # تهيئة البوت
        await app.initialize()

        # إضافة المعالجات
        app.add_handler(CommandHandler("start", start))
        app.add_handler(CommandHandler("test", test_db))
        app.add_handler(CommandHandler("id", get_id))
        app.add_handler(CommandHandler("recipients", manage_recipients))
        app.add_handler(CommandHandler("reports", show_scheduled_reports))
        app.add_handler(CommandHandler("testreport", test_report))
        app.add_handler(CommandHandler("e", statistics_report))
        app.add_handler(CommandHandler("manage_reports", manage_reports))
        app.add_handler(CommandHandler("d", designs_without_files_command))
        app.add_handler(CommandHandler("de", designs_without_files_detailed_command))
        app.add_handler(CommandHandler("customer", customer_report_command))

        # إضافة معالج الأزرار التفاعلية
        from telegram.ext import CallbackQueryHandler
        app.add_handler(CallbackQueryHandler(customer_button_callback))

        setup_msg = "✅ البوت جاهز!"
        print(setup_msg)
        print("📱 أرسل /start في التليجرام")
        print("🆔 أرسل /id لعرض المعرف")
        print("🧪 أرسل /test لاختبار قاعدة البيانات")
        print("📊 أرسل /reports لعرض التقارير المجدولة")
        print("📈 أرسل /testreport لتقرير تجريبي")
        print("📊 أرسل /e لتقرير إحصائي (مثال: /e 3)")
        print("👤 أرسل /customer [كود] لتقرير عميل (مثال: /customer 1213)")
        print("📄 ملف Log: terra_bot.log")
        print("=" * 50)

        if logger:
            logger.info(setup_msg)
            logger.info("🔔 نظام الإشعارات نشط")
            logger.info("📊 مراقبة قاعدة البيانات مستمرة")
            logger.info("📈 نظام التقارير المجدولة نشط")

        # تشغيل مراقب قاعدة البيانات في مهمة منفصلة
        monitor_task = asyncio.create_task(monitor_all_data())
        print("🔍 تم بدء مراقب قاعدة البيانات")

        # تشغيل البوت
        await app.start()
        await app.updater.start_polling(drop_pending_updates=True)

        # انتظار إيقاف البوت
        await asyncio.Event().wait()

    except ImportError:
        print("❌ خطأ: مكتبات التليجرام غير مثبتة")
        print("💡 شغل: pip install python-telegram-bot")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
        if logger:
            logger.error(f"خطأ في تشغيل البوت: {e}")


        # تشغيل مراقب قاعدة البيانات في مهمة منفصلة
        monitor_task = asyncio.create_task(monitor_all_data())
        print("🔍 تم بدء مراقب قاعدة البيانات")

        # تشغيل البوت
        await app.start()
        await app.updater.start_polling(drop_pending_updates=True)

        # انتظار إيقاف البوت
        await asyncio.Event().wait()

    except ImportError:
        print("❌ خطأ: مكتبات التليجرام غير مثبتة")
        print("💡 شغل: pip install python-telegram-bot")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
        if logger:
            logger.error(f"خطأ في تشغيل البوت: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
