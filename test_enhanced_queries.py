#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الاستعلامات المحسنة مع التفاصيل الشاملة
"""

import pyodbc
from datetime import datetime

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

def connect_to_db():
    """الاتصال بقاعدة البيانات"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['user']};PWD={DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def test_customer_query():
    """اختبار استعلام العميل مع التفاصيل الشاملة"""
    print("🔍 اختبار استعلام العميل الشامل...")
    
    try:
        conn = connect_to_db()
        if not conn:
            return
        
        cursor = conn.cursor()
        
        # استعلام شامل للعميل
        query = """
        SELECT TOP 1 
            c.CustomerCode,
            c.NameAr,
            c.NameEn,
            c.MainPhoneNo,
            c.SubMainPhoneNo,
            c.Email,
            c.Address,
            c.NationalId,
            c.Notes,
            c.AddDate,
            c.UpdateDate,
            
            -- بيانات الفرع
            c.BranchId,
            CASE 
                WHEN c.BranchId = 2 THEN 'فرع مدينة نصر'
                WHEN c.BranchId = 3 THEN 'فرع التجمع'
                ELSE 'غير محدد'
            END AS BranchName,
            
            -- بيانات المدينة/المنطقة
            city.CityName,
            
            -- بيانات طريقة الدفع
            pay.PayTypeName,
            
            -- بيانات وسائل التواصل
            social.SocialMediaName,
            
            -- بيانات من أنشأ العميل
            addUser.UserName AS AddedByUser,
            addUser.FullName AS AddedByFullName,
            
            -- بيانات من حدث العميل
            updateUser.UserName AS UpdatedByUser,
            updateUser.FullName AS UpdatedByFullName
            
        FROM Acc_Customers c
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Acc_PayType pay ON c.PayTypeId = pay.PayTypeId
        LEFT JOIN Sys_SocialMedia social ON c.SocialMediaId = social.SocialMediaId
        LEFT JOIN Sys_Users addUser ON c.AddUser = addUser.UserId
        LEFT JOIN Sys_Users updateUser ON c.UpdateUser = updateUser.UserId
        
        WHERE c.IsDeleted = 0
        ORDER BY c.AddDate DESC
        """
        
        cursor.execute(query)
        customer = cursor.fetchone()
        
        if customer:
            print("✅ تم العثور على آخر عميل:")
            print(f"   🔢 الكود: {customer[0]}")
            print(f"   👤 الاسم العربي: {customer[1] or 'غير محدد'}")
            print(f"   🔤 الاسم الإنجليزي: {customer[2] or 'غير محدد'}")
            print(f"   📱 الهاتف الأساسي: {customer[3] or 'غير محدد'}")
            print(f"   📞 الهاتف الفرعي: {customer[4] or 'غير محدد'}")
            print(f"   📧 الإيميل: {customer[5] or 'غير محدد'}")
            print(f"   🏠 العنوان: {customer[6] or 'غير محدد'}")
            print(f"   🆔 الرقم القومي: {customer[7] or 'غير محدد'}")
            print(f"   📝 ملاحظات: {customer[8] or 'لا توجد'}")
            print(f"   🏢 الفرع: {customer[12]} ({customer[11]})")
            print(f"   🌍 المدينة/المنطقة: {customer[13] or 'غير محدد'}")
            print(f"   💳 طريقة الدفع: {customer[14] or 'غير محدد'}")
            print(f"   📱 وسيلة التواصل: {customer[15] or 'غير محدد'}")
            print(f"   👨‍💼 تم الإنشاء بواسطة: {customer[17] or 'غير محدد'} ({customer[16] or 'غير محدد'})")
            print(f"   📅 تاريخ الإضافة: {customer[9].strftime('%Y-%m-%d %H:%M') if customer[9] else 'غير محدد'}")
            
            if customer[10]:
                print(f"   🔄 آخر تحديث: {customer[10].strftime('%Y-%m-%d %H:%M')}")
                if customer[19]:
                    print(f"   👨‍🔧 تم التحديث بواسطة: {customer[19]} ({customer[18] or 'غير محدد'})")
        else:
            print("❌ لم يتم العثور على عملاء")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار استعلام العميل: {str(e)}")

def test_preview_query():
    """اختبار استعلام المعاينة مع التفاصيل الشاملة"""
    print("\n🔍 اختبار استعلام المعاينة الشامل...")
    
    try:
        conn = connect_to_db()
        if not conn:
            return
        
        cursor = conn.cursor()
        
        query = """
        SELECT TOP 1 
            p.PreviewCode,
            p.Date AS PreviewDate,
            p.Notes AS PreviewNotes,
            p.BranchId,
            CASE 
                WHEN p.BranchId = 2 THEN 'فرع مدينة نصر'
                WHEN p.BranchId = 3 THEN 'فرع التجمع'
                ELSE 'غير محدد'
            END AS BranchName,
            
            -- بيانات العميل
            c.CustomerCode,
            c.NameAr AS CustomerName,
            c.MainPhoneNo,
            c.Address,
            
            -- بيانات المدينة
            city.CityName,
            
            -- بيانات من أنشأ المعاينة
            addUser.UserName AS AddedByUser,
            addUser.FullName AS AddedByFullName,
            
            p.AddDate
            
        FROM Sys_Previews p
        INNER JOIN Acc_Customers c ON p.CustomerId = c.CustomerCode
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON p.AddUser = addUser.UserId
        
        WHERE p.IsDeleted = 0 AND c.IsDeleted = 0
        ORDER BY p.Date DESC
        """
        
        cursor.execute(query)
        preview = cursor.fetchone()
        
        if preview:
            print("✅ تم العثور على آخر معاينة:")
            print(f"   🔢 كود المعاينة: {preview[0]}")
            print(f"   📅 تاريخ المعاينة: {preview[1].strftime('%Y-%m-%d %H:%M') if preview[1] else 'غير محدد'}")
            print(f"   📝 ملاحظات المعاينة: {preview[2] or 'لا توجد'}")
            print(f"   🏢 الفرع: {preview[4]}")
            print(f"   👤 العميل: {preview[6] or 'غير محدد'} (كود: {preview[5]})")
            print(f"   📱 هاتف العميل: {preview[7] or 'غير محدد'}")
            print(f"   🏠 عنوان العميل: {preview[8] or 'غير محدد'}")
            print(f"   🌍 المدينة/المنطقة: {preview[9] or 'غير محدد'}")
            print(f"   👨‍💼 تم الإنشاء بواسطة: {preview[11] or 'غير محدد'} ({preview[10] or 'غير محدد'})")
            print(f"   📅 تاريخ الإضافة: {preview[12].strftime('%Y-%m-%d %H:%M') if preview[12] else 'غير محدد'}")
        else:
            print("❌ لم يتم العثور على معاينات")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار استعلام المعاينة: {str(e)}")

def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار الاستعلامات المحسنة")
    print("=" * 50)
    
    # اختبار استعلام العميل
    test_customer_query()
    
    # اختبار استعلام المعاينة
    test_preview_query()
    
    print("\n" + "=" * 50)
    print("✅ انتهى الاختبار")

if __name__ == "__main__":
    main()
