#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🚀 بدء الاختبار...")

import pyodbc

# إعدادات قاعدة البيانات الأصلية (Terra) - قراءة فقط
MAIN_DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

# إعدادات قاعدة بيانات الإشعارات (Terra_Notifications_DB1) - قراءة وكتابة
NOTIFICATIONS_DB_CONFIG = {
    'server': '.',
    'database': 'Terra_Notifications_DB1',
    'user': 'sa',
    'password': 'Ret_ME@'
}

def connect_to_main_db():
    """الاتصال بقاعدة البيانات الأصلية"""
    try:
        conn_str = (
            f"DRIVER={{ODBC Driver 17 for SQL Server}};"
            f"SERVER={MAIN_DB_CONFIG['server']};"
            f"DATABASE={MAIN_DB_CONFIG['database']};"
            f"UID={MAIN_DB_CONFIG['user']};"
            f"PWD={MAIN_DB_CONFIG['password']};"
        )
        return pyodbc.connect(conn_str)
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات الأصلية: {str(e)}")
        return None

def connect_to_notifications_db():
    """الاتصال بقاعدة بيانات الإشعارات"""
    try:
        conn_str = (
            f"DRIVER={{ODBC Driver 17 for SQL Server}};"
            f"SERVER={NOTIFICATIONS_DB_CONFIG['server']};"
            f"DATABASE={NOTIFICATIONS_DB_CONFIG['database']};"
            f"UID={NOTIFICATIONS_DB_CONFIG['user']};"
            f"PWD={NOTIFICATIONS_DB_CONFIG['password']};"
        )
        return pyodbc.connect(conn_str)
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة بيانات الإشعارات: {str(e)}")
        return None

def get_last_sent_customer_id():
    """الحصول على آخر معرف عميل تم إرساله عموماً"""
    try:
        print("🔍 جاري الاتصال بقاعدة بيانات الإشعارات...")
        notifications_conn = connect_to_notifications_db()
        if not notifications_conn:
            print("❌ فشل الاتصال بقاعدة بيانات الإشعارات")
            return 0

        print("✅ تم الاتصال بقاعدة بيانات الإشعارات")
        notifications_cursor = notifications_conn.cursor()
        
        print("🔍 جاري البحث عن آخر معرف عميل...")
        # جلب آخر معرف فريد عموماً بغض النظر عن الفرع
        notifications_cursor.execute("""
            SELECT TOP 1 RecordUniqueId FROM Notifications_Log
            WHERE DataType = 'عميل' AND IsActive = 1 AND RecordUniqueId IS NOT NULL
            ORDER BY RecordUniqueId DESC
        """)

        result = notifications_cursor.fetchone()
        notifications_conn.close()

        last_id = result[0] if result else 0
        print(f"📊 آخر معرف عميل عموماً: {last_id}")
        return last_id
    except Exception as e:
        print(f"❌ خطأ في الحصول على آخر معرف عميل: {str(e)}")
        return 0

def check_new_customers():
    """فحص العملاء الجدد"""
    try:
        print("🔍 جاري الاتصال بقاعدة البيانات الأصلية...")
        conn = connect_to_main_db()
        if not conn:
            print("❌ فشل الاتصال بقاعدة البيانات الأصلية")
            return None

        print("✅ تم الاتصال بقاعدة البيانات الأصلية")
        cursor = conn.cursor()

        # الحصول على آخر معرف تم إرساله
        last_sent_id = get_last_sent_customer_id()

        print(f"🔍 جاري البحث عن عملاء جدد بعد ID: {last_sent_id}")
        # استعلام للحصول على العملاء الجدد بعد آخر معرف تم إرساله
        query = """
        SELECT TOP 5
            c.CustomerId, c.CustomerCode, c.NameAr, c.BranchId, c.AddDate,
            CASE
                WHEN c.BranchId = 2 THEN 'فرع مدينة نصر'
                WHEN c.BranchId = 3 THEN 'فرع التجمع'
                ELSE 'فرع غير محدد'
            END AS BranchName,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName
        FROM Acc_Customers c
        LEFT JOIN Sys_Users addUser ON c.AddUser = addUser.UserId
        WHERE c.IsDeleted = 0 AND c.CustomerId > ?
        ORDER BY c.CustomerId ASC
        """

        cursor.execute(query, (last_sent_id,))
        results = cursor.fetchall()
        conn.close()

        if results:
            print(f"🔔 تم العثور على {len(results)} عميل جديد!")
            for customer in results:
                print(f"   - عميل {customer[1]} (ID: {customer[0]}) - {customer[5]}")
            return results
        else:
            print("📊 لا توجد عملاء جدد")
            return None

    except Exception as e:
        print(f"❌ خطأ في فحص العملاء الجدد: {str(e)}")
        return None

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء اختبار النظام...")
    print("=" * 50)
    
    # اختبار فحص العملاء الجدد
    print("\n🔍 اختبار فحص العملاء الجدد...")
    new_customers = check_new_customers()
    
    if new_customers:
        print("✅ النظام يعمل بشكل صحيح!")
    else:
        print("📊 النظام يعمل - لا توجد بيانات جديدة")
    
    print("\n=" * 50)
    print("🎯 انتهى الاختبار")

if __name__ == "__main__":
    main()
