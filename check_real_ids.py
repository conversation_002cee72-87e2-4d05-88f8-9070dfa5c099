#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pyodbc

# إعدادات قاعدة البيانات الأصلية (Terra)
MAIN_DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

def connect_to_main_db():
    """الاتصال بقاعدة البيانات الأصلية"""
    try:
        conn_str = (
            f"DRIVER={{ODBC Driver 17 for SQL Server}};"
            f"SERVER={MAIN_DB_CONFIG['server']};"
            f"DATABASE={MAIN_DB_CONFIG['database']};"
            f"UID={MAIN_DB_CONFIG['user']};"
            f"PWD={MAIN_DB_CONFIG['password']};"
        )
        return pyodbc.connect(conn_str)
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات الأصلية: {str(e)}")
        return None

def check_real_ids():
    """فحص المعرفات الحقيقية"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return
        
        cursor = conn.cursor()
        
        print("🔍 البحث عن معاينة 273...")
        cursor.execute("""
            SELECT PreviewId, PreviewCode, BranchId, AddDate, AddUser
            FROM Sys_Previews 
            WHERE PreviewCode = '273' AND IsDeleted = 0
            ORDER BY PreviewId DESC
        """)
        
        results = cursor.fetchall()
        if results:
            for row in results:
                print(f"   معاينة 273: PreviewId={row[0]}, BranchId={row[2]}, AddDate={row[3]}")
        else:
            print("   ❌ لم توجد معاينة 273")
        
        print("\n🔍 البحث عن اجتماع 65...")
        cursor.execute("""
            SELECT MeetingId, MeetingCode, BranchId, AddDate, AddUser
            FROM Sys_Meetings 
            WHERE MeetingCode = '65' AND IsDeleted = 0
            ORDER BY MeetingId DESC
        """)
        
        results = cursor.fetchall()
        if results:
            for row in results:
                print(f"   اجتماع 65: MeetingId={row[0]}, BranchId={row[2]}, AddDate={row[3]}")
        else:
            print("   ❌ لم يوجد اجتماع 65")
        
        print("\n🔍 آخر 3 معاينات...")
        cursor.execute("""
            SELECT TOP 3 PreviewId, PreviewCode, BranchId, AddDate
            FROM Sys_Previews 
            WHERE IsDeleted = 0
            ORDER BY PreviewId DESC
        """)
        
        results = cursor.fetchall()
        for row in results:
            print(f"   معاينة {row[1]}: PreviewId={row[0]}, BranchId={row[2]}, AddDate={row[3]}")
        
        print("\n🔍 آخر 3 اجتماعات...")
        cursor.execute("""
            SELECT TOP 3 MeetingId, MeetingCode, BranchId, AddDate
            FROM Sys_Meetings 
            WHERE IsDeleted = 0
            ORDER BY MeetingId DESC
        """)
        
        results = cursor.fetchall()
        for row in results:
            print(f"   اجتماع {row[1]}: MeetingId={row[0]}, BranchId={row[2]}, AddDate={row[3]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الفحص: {str(e)}")

def main():
    print("🚀 فحص المعرفات الحقيقية في قاعدة البيانات الأصلية...")
    print("=" * 60)
    check_real_ids()
    print("\n🎯 انتهى الفحص")

if __name__ == "__main__":
    main()
