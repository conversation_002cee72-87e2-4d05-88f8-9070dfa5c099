# Terra Bot Enhanced - دليل البناء والتشغيل التلقائي

## 🎯 نظرة عامة
Terra Bot Enhanced هو بوت تليجرام محسن مع نظام إشعارات تلقائية شامل لمراقبة قاعدة بيانات Terra.

## 📋 المتطلبات
- Python 3.8 أو أحدث
- Windows 10/11
- SQL Server
- اتصال بالإنترنت

## 🚀 خطوات البناء

### 1. تحضير الملفات
تأكد من وجود الملفات التالية:
- `enhanced_notifications.py` - الملف الرئيسي للبوت
- `setup_exe.py` - ملف إعداد البناء
- `build_exe.bat` - ملف البناء التلقائي

### 2. تشغيل البناء
```bash
# شغل ملف البناء
build_exe.bat
```

### 3. اتباع التعليمات
سيقوم الملف بـ:
1. ✅ فحص Python
2. ✅ تثبيت cx_Freeze
3. ✅ تثبيت المكتبات المطلوبة
4. ✅ بناء الملف التنفيذي
5. ✅ نسخ الملفات الإضافية

## 📁 الملفات المُنتجة

بعد البناء ستجد في مجلد `build/exe.win-amd64-3.x/`:

### الملفات الرئيسية:
- **`TerraBot_Enhanced.exe`** - الملف التنفيذي الرئيسي
- **`startup_manager.bat`** - إدارة التشغيل التلقائي
- **`README.txt`** - تعليمات الاستخدام

### المكتبات المطلوبة:
- جميع مكتبات Python المطلوبة مدمجة في الملف التنفيذي

## ⚙️ إعداد التشغيل التلقائي

### الطريقة الأولى: أثناء البناء
عند تشغيل `setup_exe.py` سيسألك:
```
هل تريد إضافة Terra Bot للتشغيل التلقائي عند بداية النظام؟
1. نعم - إضافة للتشغيل التلقائي
2. لا - عدم إضافة  
3. لاحقاً - يمكنك استخدام startup_manager.bat لاحقاً
```

### الطريقة الثانية: باستخدام startup_manager.bat
```bash
# شغل ملف إدارة التشغيل التلقائي
startup_manager.bat
```

خيارات المدير:
1. **إضافة للتشغيل التلقائي** - يضيف البوت لبداية تشغيل النظام
2. **إزالة من التشغيل التلقائي** - يزيل البوت من بداية التشغيل
3. **إلغاء** - عدم تغيير أي شيء

### الطريقة الثالثة: يدوياً
1. اضغط `Win + R`
2. اكتب `shell:startup`
3. انسخ `TerraBot_Enhanced.exe` للمجلد المفتوح

## 🔧 إعدادات البوت

### إعدادات قاعدة البيانات
في ملف `enhanced_notifications.py`:
```python
DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}
```

### إعدادات الإشعارات
```python
NOTIFICATION_CHAT_IDS = [
    "1107000748",      # مستخدم 1
    "1206533289",      # مستخدم 2
    # "-1002255521584"   # جروب (أضف البوت للجروب أولاً)
]
```

### توكن البوت
```python
BOT_TOKEN = "7556046481:AAEMttgLYzrMu1q2ogRlgsV8mgscqZKbbcY"
```

## 📱 استخدام البوت

### الأوامر المتاحة:
- **`/start`** - بدء البوت وعرض القائمة الرئيسية
- **`/test`** - اختبار الاتصال بقاعدة البيانات
- **`/id`** - عرض معرف المستخدم/الجروب للإشعارات

### أوضاع التشغيل:
1. **بوت تليجرام مع إشعارات** (افتراضي)
2. **مراقب قاعدة البيانات فقط**

## 🔔 نظام الإشعارات

### ما يراقبه البوت:
- ✅ **العملاء الجدد** - مع تفاصيل شاملة
- ✅ **المعاينات الجديدة** - مع بيانات العميل
- ✅ **الاجتماعات الجديدة** - مع بيانات العميل
- ✅ **التصميمات الجديدة** - مع بيانات العميل
- ✅ **العقود الجديدة** - مع بيانات العميل

### التفاصيل المرسلة:
- 🔢 الكود والاسم
- 📱 أرقام الهاتف
- 📧 الإيميل والعنوان
- 🏢 الفرع (التجمع/مدينة نصر)
- 🌍 المدينة/المنطقة
- 💳 طريقة الدفع
- 📱 وسيلة التواصل
- 👨‍💼 من أنشأ البيانات
- 📅 تاريخ الإضافة

## 🛠️ استكشاف الأخطاء

### مشاكل شائعة:

#### 1. خطأ في الاتصال بقاعدة البيانات
```
❌ خطأ في الاتصال بقاعدة البيانات
```
**الحل:**
- تأكد من تشغيل SQL Server
- تحقق من إعدادات قاعدة البيانات
- تأكد من صحة اسم المستخدم وكلمة المرور

#### 2. خطأ في البوت
```
❌ خطأ في تشغيل البوت
```
**الحل:**
- تحقق من صحة توكن البوت
- تأكد من الاتصال بالإنترنت
- تحقق من أن البوت مفعل في BotFather

#### 3. فشل الإشعارات
```
❌ فشل إرسال إشعار إلى [chat_id]
```
**الحل:**
- تأكد من صحة معرف المحادثة
- للجروبات: أضف البوت للجروب أولاً
- أعط البوت صلاحيات إرسال الرسائل

## 📞 الدعم الفني

للحصول على الدعم:
1. تحقق من ملف `README.txt` في مجلد البوت
2. راجع رسائل الخطأ في نافذة الكونسول
3. اتصل بفريق التطوير

## 🔒 الأمان

- ✅ البوت يقرأ فقط من قاعدة البيانات
- ✅ لا يكتب أو يعدل أي بيانات
- ✅ آمن للاستخدام في بيئة الإنتاج
- ✅ يتجاهل البيانات التجريبية

## 📈 الإصدارات

### الإصدار 2.0 (الحالي)
- ✅ دعم دالة `/id`
- ✅ إعداد التشغيل التلقائي
- ✅ واجهة محسنة
- ✅ مراقبة شاملة لجميع الجداول
- ✅ ربط مع جداول المدن وطرق الدفع ووسائل التواصل

---

**© 2024 Terra Company - جميع الحقوق محفوظة**
