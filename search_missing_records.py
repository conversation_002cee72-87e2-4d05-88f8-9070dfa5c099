#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pyodbc

# إعدادات قاعدة بيانات الإشعارات
NOTIFICATIONS_DB_CONFIG = {
    'server': '.',
    'database': 'Terra_Notifications_DB1',
    'user': 'sa',
    'password': 'Ret_ME@'
}

def connect_to_notifications_db():
    """الاتصال بقاعدة بيانات الإشعارات"""
    try:
        conn_str = (
            f"DRIVER={{ODBC Driver 17 for SQL Server}};"
            f"SERVER={NOTIFICATIONS_DB_CONFIG['server']};"
            f"DATABASE={NOTIFICATIONS_DB_CONFIG['database']};"
            f"UID={NOTIFICATIONS_DB_CONFIG['user']};"
            f"PWD={NOTIFICATIONS_DB_CONFIG['password']};"
        )
        return pyodbc.connect(conn_str)
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة بيانات الإشعارات: {str(e)}")
        return None

def search_specific_records():
    """البحث عن السجلات المحددة"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return
        
        cursor = conn.cursor()
        
        print("🔍 البحث عن معاينة 273 (معرف فريد: 1405)...")
        cursor.execute("""
            SELECT LogId, DataType, RecordId, RecordName, RecordUniqueId, IsActive, NotificationDate
            FROM Notifications_Log 
            WHERE RecordUniqueId = 1405
        """)
        
        results = cursor.fetchall()
        if results:
            for row in results:
                print(f"   وُجدت: LogId={row[0]}, DataType={row[1]}, RecordId={row[2]}, IsActive={row[5]}")
        else:
            print("   ❌ لم توجد")
        
        print("\n🔍 البحث عن اجتماع 65 (معرف فريد: 150)...")
        cursor.execute("""
            SELECT LogId, DataType, RecordId, RecordName, RecordUniqueId, IsActive, NotificationDate
            FROM Notifications_Log 
            WHERE RecordUniqueId = 150
        """)
        
        results = cursor.fetchall()
        if results:
            for row in results:
                print(f"   وُجدت: LogId={row[0]}, DataType={row[1]}, RecordId={row[2]}, IsActive={row[5]}")
        else:
            print("   ❌ لم توجد")
        
        print("\n🔍 البحث عن جميع السجلات غير النشطة (IsActive = 0)...")
        cursor.execute("""
            SELECT COUNT(*) FROM Notifications_Log WHERE IsActive = 0
        """)
        
        result = cursor.fetchone()
        print(f"   عدد السجلات غير النشطة: {result[0] if result else 0}")
        
        if result and result[0] > 0:
            print("\n📋 آخر 5 سجلات غير نشطة:")
            cursor.execute("""
                SELECT TOP 5 LogId, DataType, RecordId, RecordUniqueId, NotificationDate
                FROM Notifications_Log 
                WHERE IsActive = 0
                ORDER BY LogId DESC
            """)
            
            inactive_results = cursor.fetchall()
            for row in inactive_results:
                print(f"   LogId={row[0]}, {row[1]}, كود={row[2]}, معرف فريد={row[3]}")
        
        print("\n🔍 البحث عن آخر معرفات للمعاينات والاجتماعات...")
        
        # آخر معاينة
        cursor.execute("""
            SELECT TOP 5 LogId, RecordId, RecordUniqueId, IsActive, NotificationDate
            FROM Notifications_Log 
            WHERE DataType = 'معاينة'
            ORDER BY RecordUniqueId DESC
        """)
        
        results = cursor.fetchall()
        print("\n📊 آخر 5 معاينات:")
        for row in results:
            print(f"   كود={row[1]}, معرف فريد={row[2]}, نشط={row[3]}")
        
        # آخر اجتماع
        cursor.execute("""
            SELECT TOP 5 LogId, RecordId, RecordUniqueId, IsActive, NotificationDate
            FROM Notifications_Log 
            WHERE DataType = 'اجتماع'
            ORDER BY RecordUniqueId DESC
        """)
        
        results = cursor.fetchall()
        print("\n📊 آخر 5 اجتماعات:")
        for row in results:
            print(f"   كود={row[1]}, معرف فريد={row[2]}, نشط={row[3]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في البحث: {str(e)}")

def main():
    print("🚀 البحث عن السجلات المفقودة...")
    print("=" * 50)
    search_specific_records()
    print("\n🎯 انتهى البحث")

if __name__ == "__main__":
    main()
