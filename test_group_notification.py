#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إرسال إشعار للجروب
"""

import asyncio
from datetime import datetime
from telegram import Bot

# إعدادات
BOT_TOKEN = "**********************************************"
GROUP_CHAT_ID = "-1002255521584"  # جروب TERRABOT

async def test_group_notification():
    """اختبار إرسال إشعار للجروب"""
    print("🧪 اختبار إرسال إشعار لجروب TERRABOT")
    print("=" * 50)
    
    try:
        bot = Bot(token=BOT_TOKEN)
        
        # اختبار معلومات البوت
        print("1️⃣ اختبار البوت...")
        bot_info = await bot.get_me()
        print(f"✅ البوت: {bot_info.first_name} (@{bot_info.username})")
        
        # اختبار معلومات الجروب
        print("\n2️⃣ اختبار معلومات الجروب...")
        try:
            chat_info = await bot.get_chat(GROUP_CHAT_ID)
            print(f"✅ الجروب: {chat_info.title}")
            print(f"   النوع: {chat_info.type}")
            print(f"   المعرف: {chat_info.id}")
        except Exception as e:
            print(f"❌ خطأ في الحصول على معلومات الجروب: {str(e)}")
            return
        
        # اختبار إرسال رسالة
        print("\n3️⃣ اختبار إرسال رسالة...")
        
        test_message = f"""🧪 **اختبار إشعارات جروب TERRABOT**

🤖 **البوت:** {bot_info.first_name}
👥 **الجروب:** {chat_info.title}
🆔 **المعرف:** {GROUP_CHAT_ID}

✅ **النظام يعمل بنجاح!**

⏰ **الوقت:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
        
        await bot.send_message(
            chat_id=GROUP_CHAT_ID,
            text=test_message,
            parse_mode='Markdown'
        )
        
        print("✅ تم إرسال رسالة الاختبار بنجاح!")
        
        # اختبار إشعار عميل وهمي
        print("\n4️⃣ اختبار إشعار عميل وهمي...")
        
        customer_notification = f"""🔔 **إشعار تلقائي من Terra Bot**

👤 **عميل جديد تم إضافته**

🔢 **الكود:** TEST123
👤 **الاسم:** عميل تجريبي للاختبار
📱 **الهاتف:** 01012345678
🏢 **الفرع:** فرع التجمع
📅 **تاريخ الإضافة:** {datetime.now().strftime('%Y-%m-%d %H:%M')}

⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
        
        await bot.send_message(
            chat_id=GROUP_CHAT_ID,
            text=customer_notification,
            parse_mode='Markdown'
        )
        
        print("✅ تم إرسال إشعار العميل التجريبي بنجاح!")
        
        print("\n" + "=" * 50)
        print("🎉 جميع الاختبارات نجحت! الجروب جاهز للإشعارات")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")
        
        if "Forbidden" in str(e):
            print("\n💡 الحل:")
            print("1. تأكد من أن البوت عضو في الجروب")
            print("2. أعط البوت صلاحية إرسال الرسائل")
            print("3. أو اجعل البوت Admin في الجروب")
        
        elif "Chat not found" in str(e):
            print("\n💡 الحل:")
            print("1. تأكد من صحة معرف الجروب")
            print("2. تأكد من أن البوت تم إضافته للجروب")
            print("3. جرب إرسال /start في الجروب")

if __name__ == "__main__":
    asyncio.run(test_group_notification())
