#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص أسماء الجداول في قاعدة البيانات Terra
"""

import pyodbc

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

def connect_to_db():
    """الاتصال بقاعدة البيانات"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['user']};PWD={DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def check_all_tables():
    """فحص جميع الجداول في قاعدة البيانات"""
    try:
        conn = connect_to_db()
        if not conn:
            return
        
        cursor = conn.cursor()
        
        print("🔍 فحص جميع الجداول في قاعدة البيانات Terra...")
        print("=" * 60)
        
        # الحصول على قائمة جميع الجداول
        cursor.execute("""
            SELECT TABLE_NAME, TABLE_TYPE 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
        """)
        
        tables = cursor.fetchall()
        
        if not tables:
            print("❌ لا توجد جداول في قاعدة البيانات")
            return
        
        print(f"📊 تم العثور على {len(tables)} جدول:")
        print()
        
        for i, table in enumerate(tables, 1):
            table_name = table[0]
            print(f"{i:2d}. {table_name}")
            
            # فحص عدد الصفوف في كل جدول
            try:
                cursor.execute(f"SELECT COUNT(*) FROM [{table_name}]")
                count = cursor.fetchone()[0]
                print(f"    📈 عدد الصفوف: {count}")
            except Exception as e:
                print(f"    ❌ خطأ في قراءة الجدول: {str(e)}")
            
            print()
        
        print("=" * 60)
        print("🔍 فحص الجداول المطلوبة للبوت:")
        
        required_tables = ['Customers', 'Previews', 'Meetings', 'Designs', 'Contracts']
        existing_tables = [table[0] for table in tables]
        
        for req_table in required_tables:
            if req_table in existing_tables:
                print(f"✅ {req_table} - موجود")
            else:
                print(f"❌ {req_table} - غير موجود")
                # البحث عن جداول مشابهة
                similar = [t for t in existing_tables if req_table.lower() in t.lower() or t.lower() in req_table.lower()]
                if similar:
                    print(f"   🔍 جداول مشابهة: {', '.join(similar)}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص الجداول: {str(e)}")

def check_table_structure(table_name):
    """فحص بنية جدول معين"""
    try:
        conn = connect_to_db()
        if not conn:
            return
        
        cursor = conn.cursor()
        
        print(f"\n🔍 فحص بنية الجدول: {table_name}")
        print("=" * 40)
        
        # الحصول على معلومات الأعمدة
        cursor.execute(f"""
            SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                COLUMN_DEFAULT
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = '{table_name}'
            ORDER BY ORDINAL_POSITION
        """)
        
        columns = cursor.fetchall()
        
        if not columns:
            print(f"❌ الجدول {table_name} غير موجود")
            return
        
        print("📋 الأعمدة:")
        for col in columns:
            nullable = "نعم" if col[2] == "YES" else "لا"
            default = col[3] if col[3] else "بدون"
            print(f"  • {col[0]} ({col[1]}) - فارغ: {nullable} - افتراضي: {default}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص بنية الجدول: {str(e)}")

if __name__ == "__main__":
    print("🌟 فحص قاعدة بيانات Terra 🌟")
    print()
    
    # فحص جميع الجداول
    check_all_tables()
    
    print("\n" + "=" * 60)
    input("اضغط Enter للمتابعة وفحص بنية الجداول...")
    
    # فحص بنية الجداول المهمة
    important_tables = ['Customers', 'Customer', 'العملاء', 'clients']
    
    for table in important_tables:
        check_table_structure(table)
