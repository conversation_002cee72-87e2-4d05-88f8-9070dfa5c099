#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حذف البيانات التجريبية المضافة خطأً
"""

import pyodbc
from datetime import datetime

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

def connect_to_db():
    """الاتصال بقاعدة البيانات"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['user']};PWD={DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def find_test_customers():
    """البحث عن العملاء التجريبيين"""
    try:
        conn = connect_to_db()
        if not conn:
            return []
        
        cursor = conn.cursor()
        
        # البحث عن العملاء التجريبيين
        cursor.execute("""
            SELECT CustomerCode, NameAr, MainPhoneNo, AddDate
            FROM Acc_Customers 
            WHERE NameAr LIKE '%تجريبي%' 
               OR NameAr LIKE '%اختبار%'
               OR NameAr LIKE '%test%'
               OR MainPhoneNo LIKE '0101234%'
            ORDER BY AddDate DESC
        """)
        
        test_customers = cursor.fetchall()
        conn.close()
        
        return test_customers
        
    except Exception as e:
        print(f"❌ خطأ في البحث: {str(e)}")
        return []

def delete_test_customers():
    """حذف العملاء التجريبيين"""
    test_customers = find_test_customers()
    
    if not test_customers:
        print("✅ لا توجد بيانات تجريبية للحذف")
        return
    
    print(f"⚠️ تم العثور على {len(test_customers)} عميل تجريبي:")
    for customer in test_customers:
        print(f"   • كود: {customer[0]} - اسم: {customer[1]} - هاتف: {customer[2]}")
    
    confirm = input("\n❓ هل تريد حذف هذه البيانات التجريبية؟ (y/n): ").strip().lower()
    
    if confirm != 'y':
        print("❌ تم إلغاء عملية الحذف")
        return
    
    try:
        conn = connect_to_db()
        if not conn:
            return
        
        cursor = conn.cursor()
        
        deleted_count = 0
        for customer in test_customers:
            customer_code = customer[0]
            
            # حذف العميل (وضع IsDeleted = 1)
            cursor.execute("""
                UPDATE Acc_Customers 
                SET IsDeleted = 1, DeleteDate = ?, DeleteUser = 1
                WHERE CustomerCode = ?
            """, (datetime.now(), customer_code))
            
            deleted_count += 1
            print(f"✅ تم حذف العميل: {customer_code}")
        
        conn.commit()
        conn.close()
        
        print(f"\n✅ تم حذف {deleted_count} عميل تجريبي بنجاح")
        
    except Exception as e:
        print(f"❌ خطأ في الحذف: {str(e)}")

def main():
    """الدالة الرئيسية"""
    print("🧹 تنظيف البيانات التجريبية")
    print("=" * 40)
    
    delete_test_customers()
    
    print("\n" + "=" * 40)
    print("✅ انتهى التنظيف")

if __name__ == "__main__":
    main()
