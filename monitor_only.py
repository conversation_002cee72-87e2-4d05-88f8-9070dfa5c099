#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مراقب البيانات فقط - بدون بوت تليجرام
"""

import asyncio
import pyodbc
from datetime import datetime

# إعدادات قاعدة البيانات الأصلية (Terra) - قراءة فقط
MAIN_DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

# إعدادات قاعدة بيانات الإشعارات (Terra_Notifications_DB1) - قراءة وكتابة
NOTIFICATIONS_DB_CONFIG = {
    'server': '.',
    'database': 'Terra_Notifications_DB1',
    'user': 'sa',
    'password': 'Ret_ME@'
}

def connect_to_main_db():
    """الاتصال بقاعدة البيانات الأصلية"""
    try:
        conn_str = (
            f"DRIVER={{ODBC Driver 17 for SQL Server}};"
            f"SERVER={MAIN_DB_CONFIG['server']};"
            f"DATABASE={MAIN_DB_CONFIG['database']};"
            f"UID={MAIN_DB_CONFIG['user']};"
            f"PWD={MAIN_DB_CONFIG['password']};"
        )
        return pyodbc.connect(conn_str)
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات الأصلية: {str(e)}")
        return None

def connect_to_notifications_db():
    """الاتصال بقاعدة بيانات الإشعارات"""
    try:
        conn_str = (
            f"DRIVER={{ODBC Driver 17 for SQL Server}};"
            f"SERVER={NOTIFICATIONS_DB_CONFIG['server']};"
            f"DATABASE={NOTIFICATIONS_DB_CONFIG['database']};"
            f"UID={NOTIFICATIONS_DB_CONFIG['user']};"
            f"PWD={NOTIFICATIONS_DB_CONFIG['password']};"
        )
        return pyodbc.connect(conn_str)
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة بيانات الإشعارات: {str(e)}")
        return None

def get_last_sent_customer_id():
    """الحصول على آخر معرف عميل تم إرساله عموماً"""
    try:
        notifications_conn = connect_to_notifications_db()
        if not notifications_conn:
            return 0

        notifications_cursor = notifications_conn.cursor()
        # جلب آخر معرف فريد عموماً بغض النظر عن الفرع
        notifications_cursor.execute("""
            SELECT TOP 1 RecordUniqueId FROM Notifications_Log
            WHERE DataType = 'عميل' AND IsActive = 1 AND RecordUniqueId IS NOT NULL
            ORDER BY RecordUniqueId DESC
        """)

        result = notifications_cursor.fetchone()
        notifications_conn.close()

        last_id = result[0] if result else 0
        print(f"📊 آخر معرف عميل عموماً: {last_id}")
        return last_id
    except Exception as e:
        print(f"❌ خطأ في الحصول على آخر معرف عميل: {str(e)}")
        return 0

def check_new_customers():
    """فحص العملاء الجدد"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # الحصول على آخر معرف تم إرساله
        last_sent_id = get_last_sent_customer_id()

        # استعلام للحصول على العملاء الجدد بعد آخر معرف تم إرساله
        query = """
        SELECT TOP 5
            c.CustomerId, c.CustomerCode, c.NameAr, c.BranchId, c.AddDate,
            CASE
                WHEN c.BranchId = 2 THEN 'فرع مدينة نصر'
                WHEN c.BranchId = 3 THEN 'فرع التجمع'
                ELSE 'فرع غير محدد'
            END AS BranchName,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName
        FROM Acc_Customers c
        LEFT JOIN Sys_Users addUser ON c.AddUser = addUser.UserId
        WHERE c.IsDeleted = 0 AND c.CustomerId > ?
        ORDER BY c.CustomerId ASC
        """

        cursor.execute(query, (last_sent_id,))
        results = cursor.fetchall()
        conn.close()

        if results:
            print(f"🔔 تم العثور على {len(results)} عميل جديد!")
            for customer in results:
                print(f"   - عميل {customer[1]} (ID: {customer[0]}) - {customer[5]}")
            return results
        else:
            return None

    except Exception as e:
        print(f"❌ خطأ في فحص العملاء الجدد: {str(e)}")
        return None

def get_last_sent_preview_id():
    """الحصول على آخر معرف معاينة تم إرساله عموماً"""
    try:
        notifications_conn = connect_to_notifications_db()
        if not notifications_conn:
            return 0

        notifications_cursor = notifications_conn.cursor()
        # جلب آخر معرف فريد عموماً بغض النظر عن الفرع
        notifications_cursor.execute("""
            SELECT TOP 1 RecordUniqueId FROM Notifications_Log
            WHERE DataType = 'معاينة' AND IsActive = 1 AND RecordUniqueId IS NOT NULL
            ORDER BY RecordUniqueId DESC
        """)

        result = notifications_cursor.fetchone()
        notifications_conn.close()

        last_id = result[0] if result else 0
        print(f"📊 آخر معرف معاينة عموماً: {last_id}")
        return last_id
    except Exception as e:
        print(f"❌ خطأ في الحصول على آخر معرف معاينة: {str(e)}")
        return 0

def check_new_previews():
    """فحص المعاينات الجديدة"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # الحصول على آخر معرف تم إرساله
        last_sent_id = get_last_sent_preview_id()

        # استعلام للحصول على المعاينات الجديدة بعد آخر معرف تم إرساله
        query = """
        SELECT TOP 5
            p.PreviewId, p.PreviewCode, p.BranchId, p.AddDate,
            CASE
                WHEN p.BranchId = 2 THEN 'فرع مدينة نصر'
                WHEN p.BranchId = 3 THEN 'فرع التجمع'
                ELSE 'فرع غير محدد'
            END AS BranchName,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName
        FROM Sys_Previews p
        LEFT JOIN Sys_Users addUser ON p.AddUser = addUser.UserId
        WHERE p.IsDeleted = 0 AND p.PreviewId > ?
        ORDER BY p.PreviewId ASC
        """

        cursor.execute(query, (last_sent_id,))
        results = cursor.fetchall()
        conn.close()

        if results:
            print(f"🔔 تم العثور على {len(results)} معاينة جديدة!")
            for preview in results:
                print(f"   - معاينة {preview[1]} (ID: {preview[0]}) - {preview[4]}")
            return results
        else:
            return None

    except Exception as e:
        print(f"❌ خطأ في فحص المعاينات الجديدة: {str(e)}")
        return None

async def monitor_data():
    """مراقبة البيانات"""
    print("🔍 بدء مراقبة البيانات...")
    print("📊 سيتم فحص العملاء والمعاينات الجديدة كل 30 ثانية")
    print("=" * 50)

    cycle = 0

    while True:
        try:
            cycle += 1
            current_time = datetime.now().strftime('%H:%M:%S')
            print(f"\n🔄 دورة مراقبة #{cycle} - {current_time}")

            # فحص العملاء الجدد
            new_customers = check_new_customers()
            if new_customers:
                print("✅ تم العثور على عملاء جدد!")
            else:
                print("📊 لا توجد عملاء جدد")

            # فحص المعاينات الجديدة
            new_previews = check_new_previews()
            if new_previews:
                print("✅ تم العثور على معاينات جديدة!")
            else:
                print("📊 لا توجد معاينات جديدة")

            if not new_customers and not new_previews:
                print("📊 لا توجد بيانات جديدة")

            # انتظار 30 ثانية
            print("⏳ انتظار 30 ثانية...")
            await asyncio.sleep(30)

        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف المراقبة")
            break
        except Exception as e:
            print(f"❌ خطأ في المراقبة: {str(e)}")
            await asyncio.sleep(30)

if __name__ == "__main__":
    try:
        asyncio.run(monitor_data())
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف المراقب بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
