# 🌟 Terra Bot - النسخة المحسنة

## 📋 نظرة عامة

نظام Terra المتكامل يتكون من:
- **🤖 Terra Bot**: بوت تليجرام للاستعلامات والتقارير
- **🔍 Database Monitor**: مراقب قاعدة البيانات للإشعارات التلقائية

## ✨ الميزات الجديدة

### 🎯 تحسينات واجهة المستخدم
- ✅ إزالة أزرار "العودة للقائمة الرئيسية" و"تجميل PDF"
- ✅ إضافة أوامر العودة في نص الرسائل (`/start` للعودة)
- ✅ تنظيم القائمة الرئيسية لتشمل الوظائف العاملة فقط
- ✅ واجهة أكثر نظافة وسهولة في الاستخدام

### 🔔 نظام الإشعارات التلقائية
- ✅ إشعارات فورية عند إضافة عميل جديد
- ✅ إشعارات عند إضافة معاينة جديدة
- ✅ إشعارات عند إضافة اجتماع جديد
- ✅ إشعارات عند إضافة تصميم جديد
- ✅ إشعارات عند إضافة عقد جديد

### 📊 الوظائف المتاحة
1. **🔍 بحث عميل** - البحث بكود العميل أو رقم الهاتف
2. **📊 استعلام عن العملاء** - قوائم العملاء حسب الفرع والفترة
3. **🌍 عدد العملاء حسب المناطق** - إحصائيات المعاينات بالمناطق
4. **📱 عدد عملاء وسائل التواصل** - إحصائيات وسائل التواصل
5. **📊 عملاء بدون معاينات** - العملاء الذين لم يتم عمل معاينات لهم
6. **👁️ معاينات بدون اجتماعات** - المعاينات التي لم يتم عمل اجتماعات لها
7. **🤝 اجتماعات بدون تصميمات** - الاجتماعات التي لم يتم عمل تصميمات لها
8. **🎨 تصميمات بدون عقود** - التصميمات التي لم يتم عمل عقود لها

## 🚀 طريقة التشغيل

### الطريقة الأولى: تشغيل النظام المتكامل (موصى به)
```bash
python run_terra_system.py
```
ثم اختر الخيار 3 لتشغيل النظام المتكامل

### الطريقة الثانية: تشغيل منفصل

#### تشغيل البوت فقط:
```bash
python terra_bot_improved.py
```

#### تشغيل مراقب قاعدة البيانات فقط:
```bash
python database_monitor.py
```

## ⚙️ إعدادات النظام

### إعدادات قاعدة البيانات
```python
DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}
```

### إعدادات البوت
```python
BOT_TOKEN = "7664606990:AAFbBWGShtg00af-qkbCm9VRpvqq7M--bU0"
```

## 📱 كيفية الاستخدام

### 1. بدء استخدام البوت
- ابحث عن البوت في تليجرام
- اكتب `/start` لبدء الاستخدام
- ستظهر لك القائمة الرئيسية المحسنة

### 2. الأوامر السريعة
- `/start` - العودة للقائمة الرئيسية
- `/test` - اختبار قاعدة البيانات

### 3. التنقل في البوت
- اختر أحد الأزرار من القائمة الرئيسية
- اتبع التعليمات المعروضة
- للإلغاء أو العودة اكتب `/start` في أي وقت

## 🔔 نظام الإشعارات

### تفعيل الإشعارات
- الإشعارات تُفعل تلقائياً عند استخدام البوت لأول مرة
- يتم حفظ معرف المستخدم في ملف `notification_users.txt`

### أنواع الإشعارات
1. **👤 عميل جديد**: عند إضافة عميل جديد لقاعدة البيانات
2. **👁️ معاينة جديدة**: عند إضافة معاينة جديدة
3. **🤝 اجتماع جديد**: عند إضافة اجتماع جديد
4. **🎨 تصميم جديد**: عند إضافة تصميم جديد
5. **📄 عقد جديد**: عند إضافة عقد جديد

### معلومات الإشعار
كل إشعار يحتوي على:
- نوع العملية (إضافة عميل/معاينة/اجتماع/تصميم/عقد)
- تفاصيل العنصر المضاف
- اسم العميل ورقم الهاتف
- الفرع المسؤول
- تاريخ ووقت الإضافة

## 📁 ملفات النظام

### الملفات الرئيسية
- `terra_bot_improved.py` - البوت المحسن
- `database_monitor.py` - مراقب قاعدة البيانات
- `run_terra_system.py` - مشغل النظام المتكامل

### ملفات البيانات
- `notification_users.txt` - قائمة المستخدمين للإشعارات
- `terra_bot.log` - سجل البوت
- `database_monitor.log` - سجل مراقب قاعدة البيانات
- `terra_system.log` - سجل النظام المتكامل

### الملفات القديمة (للمرجع)
- `terra_bot_clean.py` - النسخة الأصلية
- `test_db.py` - اختبار قاعدة البيانات

## 🔧 استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### 1. فشل الاتصال بقاعدة البيانات
```
❌ تعذر الاتصال بقاعدة البيانات
```
**الحل:**
- تأكد من تشغيل SQL Server
- تأكد من صحة بيانات الاتصال في `DB_CONFIG`
- تأكد من وجود قاعدة البيانات "Terra"

#### 2. البوت لا يستجيب
```
❌ البوت لا يرد على الرسائل
```
**الحل:**
- تأكد من صحة `BOT_TOKEN`
- تأكد من اتصال الإنترنت
- راجع ملف `terra_bot.log` للأخطاء

#### 3. الإشعارات لا تعمل
```
🔔 لا تصل إشعارات للعمليات الجديدة
```
**الحل:**
- تأكد من تشغيل `database_monitor.py`
- تأكد من وجود ملف `notification_users.txt`
- راجع ملف `database_monitor.log`

## 📞 الدعم

للحصول على المساعدة:
1. راجع ملفات السجل (`.log`)
2. تأكد من تشغيل جميع المتطلبات
3. اختبر الاتصال بقاعدة البيانات باستخدام `/test`

## 🎯 التحديثات المستقبلية

- إضافة المزيد من التقارير والإحصائيات
- تحسين واجهة المستخدم
- إضافة ميزات إدارية متقدمة
- دعم قواعد بيانات متعددة
