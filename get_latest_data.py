#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pyodbc
import asyncio
from datetime import datetime

# إعدادات قاعدة البيانات الأصلية
MAIN_DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

# إعدادات قاعدة بيانات الإشعارات
NOTIFICATIONS_DB_CONFIG = {
    'server': '.',
    'database': 'Terra_Notifications_DB1',
    'user': 'sa',
    'password': 'Ret_ME@'
}

def connect_to_main_db():
    """الاتصال بقاعدة البيانات الأصلية"""
    try:
        conn_str = (
            f"DRIVER={{ODBC Driver 17 for SQL Server}};"
            f"SERVER={MAIN_DB_CONFIG['server']};"
            f"DATABASE={MAIN_DB_CONFIG['database']};"
            f"UID={MAIN_DB_CONFIG['user']};"
            f"PWD={MAIN_DB_CONFIG['password']};"
        )
        return pyodbc.connect(conn_str)
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات الأصلية: {str(e)}")
        return None

def connect_to_notifications_db():
    """الاتصال بقاعدة بيانات الإشعارات"""
    try:
        conn_str = (
            f"DRIVER={{ODBC Driver 17 for SQL Server}};"
            f"SERVER={NOTIFICATIONS_DB_CONFIG['server']};"
            f"DATABASE={NOTIFICATIONS_DB_CONFIG['database']};"
            f"UID={NOTIFICATIONS_DB_CONFIG['user']};"
            f"PWD={NOTIFICATIONS_DB_CONFIG['password']};"
        )
        return pyodbc.connect(conn_str)
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة بيانات الإشعارات: {str(e)}")
        return None

def save_notification_to_db(data_type, record_id, description, branch_id, branch_name, record_unique_id, add_date, added_by):
    """حفظ الإشعار في قاعدة البيانات"""
    try:
        notifications_conn = connect_to_notifications_db()
        if not notifications_conn:
            return False

        notifications_cursor = notifications_conn.cursor()
        
        # إدراج السجل الجديد
        notifications_cursor.execute("""
            INSERT INTO Notifications_Log 
            (DataType, RecordId, Description, BranchId, BranchName, RecordUniqueId, AddDate, AddedBy, IsActive)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, 1)
        """, (data_type, record_id, description, branch_id, branch_name, record_unique_id, add_date, added_by))
        
        notifications_conn.commit()
        notifications_conn.close()
        return True
    except Exception as e:
        print(f"❌ خطأ في حفظ الإشعار: {str(e)}")
        return False

def get_latest_customers():
    """جلب آخر عملاء من كل فرع"""
    conn = connect_to_main_db()
    if not conn:
        return []
    
    cursor = conn.cursor()
    
    # جلب آخر عميل من كل فرع
    query = """
    WITH LatestCustomers AS (
        SELECT 
            c.CustomerId, c.CustomerCode, c.NameAr, c.BranchId, c.AddDate,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            CASE
                WHEN c.BranchId = 2 THEN 'فرع مدينة نصر'
                WHEN c.BranchId = 3 THEN 'فرع التجمع'
                ELSE 'فرع غير محدد'
            END AS BranchName,
            ROW_NUMBER() OVER (PARTITION BY c.BranchId ORDER BY c.CustomerId DESC) as rn
        FROM Acc_Customers c
        LEFT JOIN Sys_Users addUser ON c.AddUser = addUser.UserId
        WHERE c.IsDeleted = 0
    )
    SELECT * FROM LatestCustomers WHERE rn = 1
    """
    
    cursor.execute(query)
    results = cursor.fetchall()
    conn.close()
    
    return results

def get_latest_previews():
    """جلب آخر معاينات من كل فرع"""
    conn = connect_to_main_db()
    if not conn:
        return []
    
    cursor = conn.cursor()
    
    query = """
    WITH LatestPreviews AS (
        SELECT 
            p.PreviewId, p.PreviewCode, p.BranchId, p.AddDate,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            CASE
                WHEN p.BranchId = 2 THEN 'فرع مدينة نصر'
                WHEN p.BranchId = 3 THEN 'فرع التجمع'
                ELSE 'فرع غير محدد'
            END AS BranchName,
            ROW_NUMBER() OVER (PARTITION BY p.BranchId ORDER BY p.PreviewId DESC) as rn
        FROM Sys_Previews p
        LEFT JOIN Sys_Users addUser ON p.AddUser = addUser.UserId
        WHERE p.IsDeleted = 0
    )
    SELECT * FROM LatestPreviews WHERE rn = 1
    """
    
    cursor.execute(query)
    results = cursor.fetchall()
    conn.close()
    
    return results

def get_latest_meetings():
    """جلب آخر اجتماعات من كل فرع"""
    conn = connect_to_main_db()
    if not conn:
        return []
    
    cursor = conn.cursor()
    
    query = """
    WITH LatestMeetings AS (
        SELECT 
            m.MeetingId, m.MeetingCode, m.BranchId, m.AddDate,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            CASE
                WHEN m.BranchId = 2 THEN 'فرع مدينة نصر'
                WHEN m.BranchId = 3 THEN 'فرع التجمع'
                ELSE 'فرع غير محدد'
            END AS BranchName,
            ROW_NUMBER() OVER (PARTITION BY m.BranchId ORDER BY m.MeetingId DESC) as rn
        FROM Sys_Meetings m
        LEFT JOIN Sys_Users addUser ON m.AddUser = addUser.UserId
        WHERE m.IsDeleted = 0
    )
    SELECT * FROM LatestMeetings WHERE rn = 1
    """
    
    cursor.execute(query)
    results = cursor.fetchall()
    conn.close()
    
    return results

def get_latest_designs():
    """جلب آخر تصميمات من كل فرع"""
    conn = connect_to_main_db()
    if not conn:
        return []
    
    cursor = conn.cursor()
    
    query = """
    WITH LatestDesigns AS (
        SELECT 
            d.DesignId, d.DesignCode, d.BranchId, d.AddDate,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            CASE
                WHEN d.BranchId = 2 THEN 'فرع مدينة نصر'
                WHEN d.BranchId = 3 THEN 'فرع التجمع'
                ELSE 'فرع غير محدد'
            END AS BranchName,
            ROW_NUMBER() OVER (PARTITION BY d.BranchId ORDER BY d.DesignId DESC) as rn
        FROM Sys_Designs d
        LEFT JOIN Sys_Users addUser ON d.AddUser = addUser.UserId
        WHERE d.IsDeleted = 0
    )
    SELECT * FROM LatestDesigns WHERE rn = 1
    """
    
    cursor.execute(query)
    results = cursor.fetchall()
    conn.close()
    
    return results

def get_latest_contracts():
    """جلب آخر عقود من كل فرع"""
    conn = connect_to_main_db()
    if not conn:
        return []
    
    cursor = conn.cursor()
    
    query = """
    WITH LatestContracts AS (
        SELECT 
            ct.ContractId, ct.ContractCode, ct.BranchId, ct.AddDate,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            CASE
                WHEN ct.BranchId = 2 THEN 'فرع مدينة نصر'
                WHEN ct.BranchId = 3 THEN 'فرع التجمع'
                ELSE 'فرع غير محدد'
            END AS BranchName,
            ROW_NUMBER() OVER (PARTITION BY ct.BranchId ORDER BY ct.ContractId DESC) as rn
        FROM Sys_Contracts ct
        LEFT JOIN Sys_Users addUser ON ct.AddUser = addUser.UserId
        WHERE ct.IsDeleted = 0
    )
    SELECT * FROM LatestContracts WHERE rn = 1
    """
    
    cursor.execute(query)
    results = cursor.fetchall()
    conn.close()
    
    return results

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء جلب آخر البيانات وحفظها في قاعدة الإشعارات...")
    
    # جلب آخر العملاء
    print("\n📊 جلب آخر العملاء...")
    customers = get_latest_customers()
    for customer in customers:
        save_notification_to_db(
            'عميل',
            customer[1],  # CustomerCode
            f"عميل {customer[2] or 'غير محدد'}",
            customer[3],  # BranchId
            customer[6],  # BranchName
            customer[0],  # CustomerId
            customer[4],  # AddDate
            customer[5]   # AddedByFullName
        )
        print(f"✅ تم حفظ العميل {customer[1]} (ID: {customer[0]}) - {customer[6]}")
    
    # جلب آخر المعاينات
    print("\n📊 جلب آخر المعاينات...")
    previews = get_latest_previews()
    for preview in previews:
        save_notification_to_db(
            'معاينة',
            preview[1],  # PreviewCode
            f"معاينة {preview[1] or 'غير محدد'}",
            preview[2],  # BranchId
            preview[5],  # BranchName
            preview[0],  # PreviewId
            preview[3],  # AddDate
            preview[4]   # AddedByFullName
        )
        print(f"✅ تم حفظ المعاينة {preview[1]} (ID: {preview[0]}) - {preview[5]}")
    
    # جلب آخر الاجتماعات
    print("\n📊 جلب آخر الاجتماعات...")
    meetings = get_latest_meetings()
    for meeting in meetings:
        save_notification_to_db(
            'اجتماع',
            meeting[1],  # MeetingCode
            f"اجتماع {meeting[1] or 'غير محدد'}",
            meeting[2],  # BranchId
            meeting[5],  # BranchName
            meeting[0],  # MeetingId
            meeting[3],  # AddDate
            meeting[4]   # AddedByFullName
        )
        print(f"✅ تم حفظ الاجتماع {meeting[1]} (ID: {meeting[0]}) - {meeting[5]}")
    
    # جلب آخر التصميمات
    print("\n📊 جلب آخر التصميمات...")
    designs = get_latest_designs()
    for design in designs:
        save_notification_to_db(
            'تصميم',
            design[1],  # DesignCode
            f"تصميم {design[1] or 'غير محدد'}",
            design[2],  # BranchId
            design[5],  # BranchName
            design[0],  # DesignId
            design[3],  # AddDate
            design[4]   # AddedByFullName
        )
        print(f"✅ تم حفظ التصميم {design[1]} (ID: {design[0]}) - {design[5]}")
    
    # جلب آخر العقود
    print("\n📊 جلب آخر العقود...")
    contracts = get_latest_contracts()
    for contract in contracts:
        save_notification_to_db(
            'عقد',
            contract[1],  # ContractCode
            f"عقد {contract[1] or 'غير محدد'}",
            contract[2],  # BranchId
            contract[5],  # BranchName
            contract[0],  # ContractId
            contract[3],  # AddDate
            contract[4]   # AddedByFullName
        )
        print(f"✅ تم حفظ العقد {contract[1]} (ID: {contract[0]}) - {contract[5]}")
    
    print("\n🎯 تم الانتهاء من حفظ آخر البيانات!")
    print("الآن يمكن تشغيل النظام الأصلي وسيبدأ من هذه النقطة.")

if __name__ == "__main__":
    main()
