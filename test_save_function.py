#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pyodbc
from datetime import datetime

# إعدادات قاعدة بيانات الإشعارات
NOTIFICATIONS_DB_CONFIG = {
    'server': '.',
    'database': 'Terra_Notifications_DB1',
    'user': 'sa',
    'password': 'Ret_ME@'
}

def connect_to_notifications_db():
    """الاتصال بقاعدة بيانات الإشعارات"""
    try:
        conn_str = (
            f"DRIVER={{ODBC Driver 17 for SQL Server}};"
            f"SERVER={NOTIFICATIONS_DB_CONFIG['server']};"
            f"DATABASE={NOTIFICATIONS_DB_CONFIG['database']};"
            f"UID={NOTIFICATIONS_DB_CONFIG['user']};"
            f"PWD={NOTIFICATIONS_DB_CONFIG['password']};"
        )
        return pyodbc.connect(conn_str)
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة بيانات الإشعارات: {str(e)}")
        return None

def save_notification_to_db(data_type, code, name, branch_id, branch_name, unique_id, add_date, added_by):
    """حفظ بيانات الإشعار في قاعدة بيانات الإشعارات"""
    try:
        print(f"🔄 محاولة حفظ {data_type} - كود {code} - معرف فريد {unique_id}")
        
        conn = connect_to_notifications_db()
        if not conn:
            print("❌ فشل الاتصال بقاعدة بيانات الإشعارات")
            return False

        cursor = conn.cursor()

        # التحقق أولاً من وجود السجل لتجنب التكرار
        print(f"🔍 فحص وجود السجل: {data_type} - معرف فريد {unique_id}")
        cursor.execute("""
            SELECT COUNT(*) FROM Notifications_Log
            WHERE DataType = ? AND RecordUniqueId = ? AND IsActive = 1
        """, (data_type, unique_id))

        result = cursor.fetchone()
        if result and result[0] > 0:
            # السجل موجود بالفعل
            print(f"⚠️ السجل موجود بالفعل: {data_type} - معرف فريد {unique_id}")
            conn.close()
            return True

        # إدراج السجل الجديد
        print(f"💾 إدراج سجل جديد: {data_type} - كود {code}")
        cursor.execute("""
            INSERT INTO Notifications_Log
            (DataType, RecordId, RecordName, BranchId, BranchName, RecordUniqueId, AddDate, AddedBy, NotificationDate, NotificationStatus)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, GETDATE(), N'تم الإرسال')
        """, (data_type, code, name, branch_id, branch_name, unique_id, add_date, added_by))

        conn.commit()
        conn.close()

        success_msg = f"✅ تم حفظ {data_type} - كود {code} - معرف فريد {unique_id} في قاعدة بيانات الإشعارات"
        print(success_msg)
        return True

    except Exception as e:
        # تجاهل أخطاء UNIQUE KEY constraint لأنها تعني أن السجل موجود بالفعل
        if "UNIQUE KEY constraint" in str(e) or "2627" in str(e):
            print(f"⚠️ السجل موجود بالفعل (UNIQUE constraint): {data_type} - معرف فريد {unique_id}")
            return True

        error_msg = f"❌ خطأ في حفظ البيانات في قاعدة بيانات الإشعارات: {str(e)}"
        print(error_msg)
        return False

def main():
    print("🚀 اختبار دالة حفظ الإشعارات...")
    print("=" * 50)
    
    # اختبار حفظ معاينة 273
    print("\n🔍 اختبار حفظ معاينة 273...")
    result1 = save_notification_to_db(
        'معاينة', 
        '273', 
        'معاينة للعميل مادونا نبيل', 
        2, 
        'فرع مدينة نصر', 
        1405,  # معرف فريد للمعاينة 273
        datetime(2025, 6, 30, 7, 50),
        'admin'
    )
    print(f"نتيجة حفظ المعاينة: {result1}")
    
    # اختبار حفظ اجتماع 65
    print("\n🔍 اختبار حفظ اجتماع 65...")
    result2 = save_notification_to_db(
        'اجتماع', 
        '65', 
        'اجتماع مع العميل مادونا نبيل', 
        2, 
        'فرع مدينة نصر', 
        150,  # معرف فريد للاجتماع 65
        datetime(2025, 6, 30, 7, 50),
        'admin'
    )
    print(f"نتيجة حفظ الاجتماع: {result2}")
    
    print("\n=" * 50)
    print("🎯 انتهى الاختبار")

if __name__ == "__main__":
    main()
