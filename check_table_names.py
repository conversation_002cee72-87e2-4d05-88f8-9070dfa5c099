#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص أسماء الجداول الصحيحة في قاعدة البيانات
"""

import pyodbc

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

def connect_to_db():
    """الاتصال بقاعدة البيانات"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['user']};PWD={DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def get_all_tables():
    """الحصول على جميع أسماء الجداول"""
    try:
        conn = connect_to_db()
        if not conn:
            return
        
        cursor = conn.cursor()
        
        # الحصول على جميع الجداول
        cursor.execute("""
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_TYPE = 'BASE TABLE'
            ORDER BY TABLE_NAME
        """)
        
        tables = cursor.fetchall()
        
        print("📋 جميع الجداول في قاعدة البيانات:")
        for table in tables:
            print(f"   • {table[0]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في الحصول على الجداول: {str(e)}")

def check_specific_tables():
    """فحص الجداول المطلوبة"""
    try:
        conn = connect_to_db()
        if not conn:
            return
        
        cursor = conn.cursor()
        
        # البحث عن جداول المدن
        print("\n🔍 البحث عن جداول المدن:")
        cursor.execute("""
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_NAME LIKE '%City%' OR TABLE_NAME LIKE '%Cities%'
            ORDER BY TABLE_NAME
        """)
        city_tables = cursor.fetchall()
        for table in city_tables:
            print(f"   • {table[0]}")
        
        # البحث عن جداول المستخدمين
        print("\n🔍 البحث عن جداول المستخدمين:")
        cursor.execute("""
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_NAME LIKE '%User%' OR TABLE_NAME LIKE '%Users%'
            ORDER BY TABLE_NAME
        """)
        user_tables = cursor.fetchall()
        for table in user_tables:
            print(f"   • {table[0]}")
        
        # البحث عن جداول طرق الدفع
        print("\n🔍 البحث عن جداول طرق الدفع:")
        cursor.execute("""
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_NAME LIKE '%Pay%' OR TABLE_NAME LIKE '%Payment%'
            ORDER BY TABLE_NAME
        """)
        pay_tables = cursor.fetchall()
        for table in pay_tables:
            print(f"   • {table[0]}")
        
        # البحث عن جداول وسائل التواصل
        print("\n🔍 البحث عن جداول وسائل التواصل:")
        cursor.execute("""
            SELECT TABLE_NAME 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_NAME LIKE '%Social%' OR TABLE_NAME LIKE '%Media%'
            ORDER BY TABLE_NAME
        """)
        social_tables = cursor.fetchall()
        for table in social_tables:
            print(f"   • {table[0]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص الجداول: {str(e)}")

def check_customer_columns():
    """فحص أعمدة جدول العملاء"""
    try:
        conn = connect_to_db()
        if not conn:
            return
        
        cursor = conn.cursor()
        
        print("\n🔍 أعمدة جدول Acc_Customers:")
        cursor.execute("""
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'Acc_Customers'
            ORDER BY ORDINAL_POSITION
        """)
        
        columns = cursor.fetchall()
        for col in columns:
            print(f"   • {col[0]} ({col[1]}) - {col[2]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص أعمدة العملاء: {str(e)}")

def main():
    """الدالة الرئيسية"""
    print("🔍 فحص أسماء الجداول في قاعدة البيانات")
    print("=" * 60)
    
    # الحصول على جميع الجداول
    get_all_tables()
    
    # فحص الجداول المطلوبة
    check_specific_tables()
    
    # فحص أعمدة جدول العملاء
    check_customer_columns()
    
    print("\n" + "=" * 60)
    print("✅ انتهى الفحص")

if __name__ == "__main__":
    main()
