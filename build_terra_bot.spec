# -*- mode: python ; coding: utf-8 -*-

import sys
import os
from PyInstaller.utils.hooks import collect_data_files, collect_submodules

# جمع البيانات المطلوبة للمكتبات
telegram_data = collect_data_files('telegram')
asyncio_data = collect_data_files('asyncio')
pyodbc_data = collect_data_files('pyodbc')

# جمع الوحدات الفرعية
telegram_modules = collect_submodules('telegram')
asyncio_modules = collect_submodules('asyncio')

block_cipher = None

a = Analysis(
    ['enhanced_notifications.py'],
    pathex=[],
    binaries=[],
    datas=telegram_data + asyncio_data + pyodbc_data,
    hiddenimports=[
        'telegram',
        'telegram.ext',
        'telegram.error',
        'asyncio',
        'pyodbc',
        'logging',
        'datetime',
        'json',
        'sys',
        'os',
        'time',
        'traceback',
        'concurrent.futures',
        'threading',
        'queue',
        'signal',
        'platform',
        'socket',
        'ssl',
        'urllib3',
        'certifi',
        'charset_normalizer',
        'idna',
        'requests',
        'httpx',
        'h11',
        'sniffio',
        'anyio',
        'httpcore',
        'typing_extensions',
        'win32api',
        'win32con',
        'win32event',
        'win32service',
        'win32serviceutil',
        'servicemanager',
        'winsound'
    ] + telegram_modules + asyncio_modules,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'tkinter',
        'matplotlib',
        'numpy',
        'pandas',
        'scipy',
        'PIL',
        'cv2',
        'PyQt5',
        'PyQt6',
        'PySide2',
        'PySide6',
        'wx'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='TerraBot_Enhanced',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,  # --noconsole
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='terra_icon.ico' if os.path.exists('terra_icon.ico') else None,
    version='version_info.txt' if os.path.exists('version_info.txt') else None,
)
