#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص أسماء الأعمدة في الجداول المهمة
"""

import pyodbc

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

def connect_to_db():
    """الاتصال بقاعدة البيانات"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['user']};PWD={DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def check_table_columns(table_name):
    """فحص أعمدة جدول معين"""
    try:
        conn = connect_to_db()
        if not conn:
            return
        
        cursor = conn.cursor()
        
        print(f"\n🔍 فحص أعمدة الجدول: {table_name}")
        print("=" * 50)
        
        # الحصول على معلومات الأعمدة
        cursor.execute(f"""
            SELECT 
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                CHARACTER_MAXIMUM_LENGTH
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = '{table_name}'
            ORDER BY ORDINAL_POSITION
        """)
        
        columns = cursor.fetchall()
        
        if not columns:
            print(f"❌ الجدول {table_name} غير موجود أو لا يحتوي على أعمدة")
            return
        
        print("📋 الأعمدة:")
        for i, col in enumerate(columns, 1):
            nullable = "نعم" if col[2] == "YES" else "لا"
            length = f"({col[3]})" if col[3] else ""
            print(f"{i:2d}. {col[0]} - {col[1]}{length} - فارغ: {nullable}")
        
        # عرض بعض البيانات النموذجية
        print(f"\n📊 عينة من البيانات (أول 3 صفوف):")
        try:
            cursor.execute(f"SELECT TOP 3 * FROM {table_name}")
            rows = cursor.fetchall()
            
            if rows:
                # عرض أسماء الأعمدة
                column_names = [desc[0] for desc in cursor.description]
                print("الأعمدة:", " | ".join(column_names))
                print("-" * 80)
                
                # عرض البيانات
                for row in rows:
                    row_data = [str(item) if item is not None else "NULL" for item in row]
                    print(" | ".join(row_data))
            else:
                print("الجدول فارغ")
                
        except Exception as e:
            print(f"خطأ في قراءة البيانات: {str(e)}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص الجدول {table_name}: {str(e)}")

def main():
    """الدالة الرئيسية"""
    print("🌟 فحص أعمدة الجداول المهمة في قاعدة بيانات Terra 🌟")
    
    # الجداول المهمة للبوت
    important_tables = [
        'Acc_Customers',
        'Sys_Previews', 
        'Sys_Meetings',
        'Sys_Designs',
        'Acc_Contracts'
    ]
    
    for table in important_tables:
        check_table_columns(table)
    
    print("\n" + "=" * 60)
    print("✅ انتهى فحص الجداول")

if __name__ == "__main__":
    main()
