#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
فحص أسماء الأعمدة في الجداول المرتبطة
"""

import pyodbc

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

def connect_to_db():
    """الاتصال بقاعدة البيانات"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['user']};PWD={DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def check_table_columns(table_name):
    """فحص أعمدة جدول معين"""
    try:
        conn = connect_to_db()
        if not conn:
            return
        
        cursor = conn.cursor()
        
        print(f"\n🔍 أعمدة جدول {table_name}:")
        cursor.execute(f"""
            SELECT COLUMN_NAME, DATA_TYPE, IS_NULLABLE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = '{table_name}'
            ORDER BY ORDINAL_POSITION
        """)
        
        columns = cursor.fetchall()
        for col in columns:
            print(f"   • {col[0]} ({col[1]}) - {col[2]}")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص أعمدة {table_name}: {str(e)}")

def test_simple_joins():
    """اختبار ربط بسيط للتأكد من الأعمدة"""
    try:
        conn = connect_to_db()
        if not conn:
            return
        
        cursor = conn.cursor()
        
        print("\n🔍 اختبار ربط بسيط مع جدول المدن:")
        cursor.execute("""
            SELECT TOP 1 c.CustomerCode, c.NameAr, city.*
            FROM Acc_Customers c
            LEFT JOIN Sys_City city ON c.CityId = city.CityId
            WHERE c.IsDeleted = 0
        """)
        
        result = cursor.fetchone()
        if result:
            print("✅ نجح الربط مع جدول المدن")
            print(f"   العميل: {result[0]} - {result[1]}")
        else:
            print("❌ لم يتم العثور على نتائج")
        
        print("\n🔍 اختبار ربط بسيط مع جدول المستخدمين:")
        cursor.execute("""
            SELECT TOP 1 c.CustomerCode, c.NameAr, u.*
            FROM Acc_Customers c
            LEFT JOIN Sys_Users u ON c.AddUser = u.UserId
            WHERE c.IsDeleted = 0
        """)
        
        result = cursor.fetchone()
        if result:
            print("✅ نجح الربط مع جدول المستخدمين")
            print(f"   العميل: {result[0]} - {result[1]}")
        else:
            print("❌ لم يتم العثور على نتائج")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الربط: {str(e)}")

def main():
    """الدالة الرئيسية"""
    print("🔍 فحص أسماء الأعمدة في الجداول المرتبطة")
    print("=" * 60)
    
    # فحص أعمدة الجداول المهمة
    check_table_columns("Sys_City")
    check_table_columns("Acc_PayType")
    check_table_columns("Sys_SocialMedia")
    check_table_columns("Sys_Users")
    
    # اختبار ربط بسيط
    test_simple_joins()
    
    print("\n" + "=" * 60)
    print("✅ انتهى الفحص")

if __name__ == "__main__":
    main()
