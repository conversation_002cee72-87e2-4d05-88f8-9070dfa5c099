#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إشعارات محسن مع تفاصيل شاملة من جميع الجداول المرتبطة
قراءة فقط - لا يكتب أي بيانات في قاعدة البيانات
"""

import asyncio
import pyodbc
import os
import logging
from datetime import datetime
from telegram import Bot

# إعدادات
BOT_TOKEN = "**********************************************"
# معرفات المستخدمين والجروبات للإشعارات (احتياطية)
DEFAULT_NOTIFICATION_CHAT_IDS = [1107000748, 1206533289, -1002255521584, -1002308493862]

def get_active_recipients():
    """الحصول على قائمة المستقبلين النشطين من قاعدة بيانات الإشعارات"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            print("⚠️ لا يمكن الاتصال بقاعدة بيانات الإشعارات - استخدام القائمة الافتراضية")
            if logger:
                logger.warning("لا يمكن الاتصال بقاعدة بيانات الإشعارات - استخدام القائمة الافتراضية")
            return DEFAULT_NOTIFICATION_CHAT_IDS

        cursor = conn.cursor()

        # الحصول على المستقبلين النشطين
        cursor.execute("""
            SELECT ChatId, RecipientName, RecipientType
            FROM Notification_Recipients
            WHERE IsActive = 1
            ORDER BY RecipientType, RecipientName
        """)

        results = cursor.fetchall()
        conn.close()

        if results:
            chat_ids = [row[0] for row in results]
            print(f"📋 تم تحميل {len(chat_ids)} مستقبل نشط من قاعدة البيانات:")
            for row in results:
                recipient_type = "👤 مستخدم" if row[2] == 'User' else "👥 جروب"
                print(f"   {recipient_type}: {row[1]} ({row[0]})")

            if logger:
                logger.info(f"📋 تم تحميل {len(chat_ids)} مستقبل نشط من قاعدة البيانات")

            return chat_ids
        else:
            print("⚠️ لا توجد مستقبلين نشطين في قاعدة البيانات - استخدام القائمة الافتراضية")
            if logger:
                logger.warning("لا توجد مستقبلين نشطين في قاعدة البيانات - استخدام القائمة الافتراضية")
            return DEFAULT_NOTIFICATION_CHAT_IDS

    except Exception as e:
        error_msg = f"❌ خطأ في تحميل المستقبلين من قاعدة البيانات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return DEFAULT_NOTIFICATION_CHAT_IDS

# إعدادات قاعدة البيانات الأصلية (Terra) - قراءة فقط
MAIN_DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

# إعدادات قاعدة بيانات الإشعارات (Terra_Notifications_DB1) - قراءة وكتابةانا
NOTIFICATIONS_DB_CONFIG = {
    'server': '.',
    'database': 'Terra_Notifications_DB1',
    'user': 'sa',
    'password': 'Ret_ME@'
}

# متغيرات لتتبع آخر البيانات حسب الفرع
last_customer_ids = {2: 0, 3: 0}  # فرع مدينة نصر: 2، فرع التجمع: 3
last_preview_ids = {2: 0, 3: 0}
last_meeting_ids = {2: 0, 3: 0}
last_design_ids = {2: 0, 3: 0}
last_contract_ids = {2: 0, 3: 0}

# ملف Excel لحفظ سجل الإشعارات
EXCEL_FILE = "terra_notifications_log.xlsx"

# ملف Log لحفظ الأخطاء والأحداث
LOG_FILE = "terra_bot.log"

# إعداد نظام Logging
def setup_logging():
    """إعداد نظام logging لحفظ الأخطاء والأحداث"""
    try:
        # إنشاء logger
        logger = logging.getLogger('TerraBot')
        logger.setLevel(logging.DEBUG)

        # إزالة handlers القديمة إذا وجدت
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # إنشاء formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # إنشاء file handler
        file_handler = logging.FileHandler(LOG_FILE, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)

        # إنشاء console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(console_formatter)

        # إضافة handlers للlogger
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        return logger

    except Exception as e:
        print(f"❌ خطأ في إعداد نظام Logging: {str(e)}")
        return None

# إنشاء logger عام
logger = setup_logging()

def connect_to_main_db():
    """الاتصال بقاعدة البيانات الأصلية (Terra) - قراءة فقط"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={MAIN_DB_CONFIG['server']};DATABASE={MAIN_DB_CONFIG['database']};UID={MAIN_DB_CONFIG['user']};PWD={MAIN_DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        if logger:
            logger.info("✅ تم الاتصال بقاعدة البيانات الأصلية بنجاح")
        return conn
    except Exception as e:
        error_msg = f"❌ خطأ في الاتصال بقاعدة البيانات الأصلية: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

def connect_to_notifications_db():
    """الاتصال بقاعدة بيانات الإشعارات (Terra_Notifications_DB1) - قراءة وكتابة"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={NOTIFICATIONS_DB_CONFIG['server']};DATABASE={NOTIFICATIONS_DB_CONFIG['database']};UID={NOTIFICATIONS_DB_CONFIG['user']};PWD={NOTIFICATIONS_DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        if logger:
            logger.info("✅ تم الاتصال بقاعدة بيانات الإشعارات بنجاح")
        return conn
    except Exception as e:
        error_msg = f"❌ خطأ في الاتصال بقاعدة بيانات الإشعارات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

# للتوافق مع الكود القديم
def connect_to_db():
    """الاتصال بقاعدة البيانات الأصلية - للتوافق مع الكود القديم"""
    return connect_to_main_db()

def init_notifications_db():
    """التحقق من وجود جداول قاعدة بيانات الإشعارات وإنشاؤها إذا لزم الأمر"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return False

        cursor = conn.cursor()

        # فحص وجود جدول سجل الإشعارات
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Notifications_Log' AND xtype='U')
            BEGIN
                CREATE TABLE Notifications_Log (
                    LogId INT IDENTITY(1,1) PRIMARY KEY,
                    DataType NVARCHAR(50) NOT NULL,
                    RecordId INT NOT NULL,
                    RecordName NVARCHAR(255),
                    BranchId INT,
                    BranchName NVARCHAR(100),
                    RecordUniqueId INT,
                    AddDate DATETIME,
                    AddedBy NVARCHAR(100),
                    NotificationDate DATETIME DEFAULT GETDATE(),
                    NotificationStatus NVARCHAR(20) DEFAULT N'تم الإرسال',
                    IsActive BIT DEFAULT 1,
                    Notes NVARCHAR(500),
                    CONSTRAINT UK_Notifications_DataType_RecordId_Branch UNIQUE(DataType, RecordId, BranchId)
                );

                CREATE INDEX IX_Notifications_DataType ON Notifications_Log(DataType);
                CREATE INDEX IX_Notifications_RecordId ON Notifications_Log(RecordId);
                CREATE INDEX IX_Notifications_Date ON Notifications_Log(NotificationDate);
                CREATE INDEX IX_Notifications_Branch ON Notifications_Log(BranchId);
            END

            -- إضافة الأعمدة الجديدة إذا لم تكن موجودة
            IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Notifications_Log' AND COLUMN_NAME = 'BranchId')
            BEGIN
                ALTER TABLE Notifications_Log ADD BranchId INT;
            END

            IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Notifications_Log' AND COLUMN_NAME = 'RecordUniqueId')
            BEGIN
                ALTER TABLE Notifications_Log ADD RecordUniqueId INT;
            END
        """)

        # فحص وجود جدول المستقبلين
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Notification_Recipients' AND xtype='U')
            BEGIN
                CREATE TABLE Notification_Recipients (
                    RecipientId INT IDENTITY(1,1) PRIMARY KEY,
                    ChatId BIGINT NOT NULL UNIQUE,
                    RecipientName NVARCHAR(100) NOT NULL,
                    RecipientType NVARCHAR(20) NOT NULL CHECK (RecipientType IN ('User', 'Group')),
                    IsActive BIT DEFAULT 1,
                    AddDate DATETIME DEFAULT GETDATE(),
                    Notes NVARCHAR(500)
                );

                CREATE INDEX IX_Recipients_ChatId ON Notification_Recipients(ChatId);
                CREATE INDEX IX_Recipients_Active ON Notification_Recipients(IsActive);

                -- إدراج المستقبلين الافتراضيين
                INSERT INTO Notification_Recipients (ChatId, RecipientName, RecipientType, IsActive)
                VALUES
                    (1107000748, N'المستخدم الأول', 'User', 1),
                    (1206533289, N'المستخدم الثاني', 'User', 1),
                    (-1002308493862, N'الجروب الرئيسي', 'Group', 1),
                    (-1002255521584, N'جروب TERRABOT', 'Group', 1);
            END
        """)

        conn.commit()
        conn.close()

        success_msg = "✅ تم التحقق من جداول قاعدة بيانات الإشعارات"
        print(success_msg)
        if logger:
            logger.info(success_msg)
        return True

    except Exception as e:
        error_msg = f"❌ خطأ في إنشاء جداول قاعدة بيانات الإشعارات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return False

def save_notification_to_db(data_type, code, name, branch_id, branch_name, unique_id, add_date, added_by):
    """حفظ بيانات الإشعار في قاعدة بيانات الإشعارات"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return False

        cursor = conn.cursor()

        # التحقق أولاً من وجود السجل لتجنب التكرار
        cursor.execute("""
            SELECT COUNT(*) FROM Notifications_Log
            WHERE DataType = ? AND RecordUniqueId = ? AND IsActive = 1
        """, (data_type, unique_id))

        result = cursor.fetchone()
        if result and result[0] > 0:
            # السجل موجود بالفعل
            conn.close()
            return True

        # إدراج السجل الجديد
        cursor.execute("""
            INSERT INTO Notifications_Log
            (DataType, RecordId, RecordName, BranchId, BranchName, RecordUniqueId, AddDate, AddedBy, NotificationDate, NotificationStatus)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, GETDATE(), N'تم الإرسال')
        """, (data_type, code, name, branch_id, branch_name, unique_id, add_date, added_by))

        conn.commit()
        conn.close()

        success_msg = f"📊 تم حفظ {data_type} - كود {code} في قاعدة بيانات الإشعارات"
        print(success_msg)
        if logger:
            logger.info(success_msg)
        return True

    except Exception as e:
        # تجاهل أخطاء UNIQUE KEY constraint لأنها تعني أن السجل موجود بالفعل
        if "UNIQUE KEY constraint" in str(e) or "2627" in str(e):
            return True

        error_msg = f"❌ خطأ في حفظ البيانات في قاعدة بيانات الإشعارات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return False



def get_last_sent_id_for_branch(data_type, branch_id):
    """الحصول على آخر معرف تم إرساله لنوع بيانات معين في فرع معين"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return 0

        cursor = conn.cursor()
        cursor.execute("""
            SELECT TOP 1 RecordUniqueId FROM Notifications_Log
            WHERE DataType = ? AND BranchId = ? AND IsActive = 1 AND RecordUniqueId IS NOT NULL
            ORDER BY NotificationDate DESC
        """, (data_type, branch_id))

        result = cursor.fetchone()
        conn.close()

        return result[0] if result else 0

    except Exception as e:
        print(f"❌ خطأ في الحصول على آخر معرف: {str(e)}")
        return 0

def load_last_ids_from_db():
    """تحميل آخر المعرفات من قاعدة بيانات الإشعارات حسب الفرع"""
    global last_customer_ids, last_preview_ids, last_meeting_ids, last_design_ids, last_contract_ids

    try:
        conn = connect_to_notifications_db()
        if not conn:
            print("📊 قاعدة بيانات الإشعارات غير متاحة - سيتم البدء من الصفر")
            return

        cursor = conn.cursor()

        # الحصول على آخر معرف لكل نوع
        data_types = ['عميل', 'معاينة', 'اجتماع', 'تصميم', 'عقد']

        branches = [2, 3]  # فرع مدينة نصر = 2، فرع التجمع = 3

        for data_type in data_types:
            for branch_id in branches:
                cursor.execute("""
                    SELECT TOP 1 RecordUniqueId FROM Notifications_Log
                    WHERE DataType = ? AND BranchId = ? AND IsActive = 1 AND RecordUniqueId IS NOT NULL
                    ORDER BY NotificationDate DESC
                """, (data_type, branch_id))

                result = cursor.fetchone()
                if result:
                    unique_id = result[0]

                    if data_type == 'عميل':
                        last_customer_ids[branch_id] = unique_id
                    elif data_type == 'معاينة':
                        last_preview_ids[branch_id] = unique_id
                    elif data_type == 'اجتماع':
                        last_meeting_ids[branch_id] = unique_id
                    elif data_type == 'تصميم':
                        last_design_ids[branch_id] = unique_id
                    elif data_type == 'عقد':
                        last_contract_ids[branch_id] = unique_id

                    branch_name = "مدينة نصر" if branch_id == 2 else "التجمع"
                    print(f"📊 تم تحميل آخر {data_type} من فرع {branch_name}: {unique_id}")

        conn.close()

        print(f"📊 ملخص المعرفات المحملة من قاعدة البيانات حسب الفرع:")
        print(f"   🏢 فرع مدينة نصر (2): عميل: {last_customer_ids[2]}, معاينة: {last_preview_ids[2]}, اجتماع: {last_meeting_ids[2]}")
        print(f"   🏬 فرع التجمع (3): عميل: {last_customer_ids[3]}, معاينة: {last_preview_ids[3]}, اجتماع: {last_meeting_ids[3]}")

        if logger:
            logger.info(f"📊 تم تحميل المعرفات حسب الفرع - مدينة نصر: عميل:{last_customer_ids[2]}, معاينة:{last_preview_ids[2]} | التجمع: عميل:{last_customer_ids[3]}, معاينة:{last_preview_ids[3]}")

    except Exception as e:
        error_msg = f"❌ خطأ في تحميل البيانات من قاعدة بيانات الإشعارات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)

def get_latest_customer_with_details():
    """الحصول على آخر عميل مع جميع التفاصيل من الجداول المرتبطة"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return None
        
        cursor = conn.cursor()
        
        # استعلام شامل مع جميع الجداول المرتبطة
        query = """
        SELECT TOP 1
            c.CustomerId,                     -- 0 - المعرف الفريد
            c.CustomerCode,                   -- 1
            c.NameAr,                         -- 2
            c.NameEn,                         -- 3
            c.MainPhoneNo,                    -- 4
            c.SubMainPhoneNo,                 -- 5
            c.Email,                          -- 6
            c.Address,                        -- 7
            c.NationalId,                     -- 8
            c.Notes,                          -- 9
            c.AddDate,                        -- 10
            c.UpdateDate,                     -- 11
            c.BranchId,                       -- 12

            -- بيانات الفرع
            CASE
                WHEN c.BranchId = 2 THEN 'فرع مدينة نصر'
                WHEN c.BranchId = 3 THEN 'فرع التجمع'
                ELSE 'فرع غير محدد'
            END AS BranchName,                -- 13

            -- بيانات المدينة/المنطقة
            ISNULL(city.NameAr, 'غير محدد') AS CityName,     -- 14

            -- بيانات طريقة الدفع
            ISNULL(pay.NameAr, 'غير محدد') AS PayTypeName,   -- 15

            -- بيانات وسائل التواصل
            ISNULL(social.NameAr, 'غير محدد') AS SocialMediaName,  -- 16

            -- بيانات من أنشأ العميل
            ISNULL(addUser.UserName, 'غير محدد') AS AddedByUser,    -- 17
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName, -- 18

            -- بيانات من حدث العميل (إذا وجد)
            ISNULL(updateUser.UserName, '') AS UpdatedByUser,       -- 19
            ISNULL(updateUser.FullName, '') AS UpdatedByFullName    -- 20

        FROM Acc_Customers c
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Acc_PayType pay ON c.PayTypeId = pay.PayTypeId
        LEFT JOIN Sys_SocialMedia social ON c.SocialMediaId = social.SocialMediaId
        LEFT JOIN Sys_Users addUser ON c.AddUser = addUser.UserId
        LEFT JOIN Sys_Users updateUser ON c.UpdateUser = updateUser.UserId

        WHERE c.IsDeleted = 0
        ORDER BY c.AddDate DESC
        """
        
        cursor.execute(query)
        result = cursor.fetchone()
        conn.close()
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في الحصول على تفاصيل العميل: {str(e)}")
        return None

def get_latest_preview_with_details():
    """الحصول على آخر معاينة مع جميع التفاصيل"""
    try:
        conn = connect_to_db()
        if not conn:
            return None
        
        cursor = conn.cursor()
        
        query = """
        SELECT TOP 1
            p.PreviewId,              -- 0 - المعرف الفريد
            p.PreviewCode,            -- 1
            p.Date AS PreviewDate,    -- 2
            p.Notes AS PreviewNotes,  -- 3
            p.BranchId,               -- 4
            CASE
                WHEN p.BranchId = 2 THEN 'فرع مدينة نصر'
                WHEN p.BranchId = 3 THEN 'فرع التجمع'
                ELSE 'غير محدد'
            END AS BranchName,         -- 5

            -- بيانات العميل
            c.CustomerCode,            -- 6
            c.NameAr AS CustomerName,  -- 7
            c.MainPhoneNo,             -- 8
            c.Address,                 -- 9

            -- بيانات المدينة
            ISNULL(city.NameAr, 'غير محدد') AS CityName,   -- 10

            -- بيانات من أنشأ المعاينة
            addUser.UserName AS AddedByUser,     -- 11
            addUser.FullName AS AddedByFullName, -- 12

            p.AddDate                  -- 13
            
        FROM Sys_Previews p
        INNER JOIN Acc_Customers c ON p.CustomerId = c.CustomerCode
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON p.AddUser = addUser.UserId
        
        WHERE p.IsDeleted = 0 AND c.IsDeleted = 0
        ORDER BY p.AddDate DESC
        """
        
        cursor.execute(query)
        result = cursor.fetchone()
        conn.close()
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في الحصول على تفاصيل المعاينة: {str(e)}")
        return None

def get_latest_meeting_with_details():
    """الحصول على آخر اجتماع مع جميع التفاصيل"""
    try:
        conn = connect_to_db()
        if not conn:
            return None
        
        cursor = conn.cursor()
        
        query = """
        SELECT TOP 1
            m.MeetingId,              -- 0 - المعرف الفريد
            m.MeetingCode,            -- 1
            m.Date AS MeetingDate,    -- 2
            m.Notes AS MeetingNotes,  -- 3
            m.BranchId,               -- 4
            CASE
                WHEN m.BranchId = 2 THEN 'فرع مدينة نصر'
                WHEN m.BranchId = 3 THEN 'فرع التجمع'
                ELSE 'غير محدد'
            END AS BranchName,         -- 5

            -- بيانات العميل
            c.CustomerCode,            -- 6
            c.NameAr AS CustomerName,  -- 7
            c.MainPhoneNo,             -- 8
            c.Address,                 -- 9

            -- بيانات المدينة
            ISNULL(city.NameAr, 'غير محدد') AS CityName,   -- 10

            -- بيانات من أنشأ الاجتماع
            addUser.UserName AS AddedByUser,     -- 11
            addUser.FullName AS AddedByFullName, -- 12

            m.AddDate                  -- 13
            
        FROM Sys_Meetings m
        INNER JOIN Acc_Customers c ON m.CustomerId = c.CustomerCode
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON m.AddUser = addUser.UserId
        
        WHERE m.IsDeleted = 0 AND c.IsDeleted = 0
        ORDER BY m.AddDate DESC
        """
        
        cursor.execute(query)
        result = cursor.fetchone()
        conn.close()
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في الحصول على تفاصيل الاجتماع: {str(e)}")
        return None

def get_latest_design_with_details():
    """الحصول على آخر تصميم مع جميع التفاصيل"""
    try:
        conn = connect_to_db()
        if not conn:
            return None
        
        cursor = conn.cursor()
        
        query = """
        SELECT TOP 1
            d.DesignId,               -- 0 - المعرف الفريد
            d.DesignCode,             -- 1
            d.Date AS DesignDate,     -- 2
            d.Notes AS DesignNotes,   -- 3
            d.BranchId,               -- 4
            CASE
                WHEN d.BranchId = 2 THEN 'فرع مدينة نصر'
                WHEN d.BranchId = 3 THEN 'فرع التجمع'
                ELSE 'غير محدد'
            END AS BranchName,         -- 5

            -- بيانات العميل
            c.CustomerCode,            -- 6
            c.NameAr AS CustomerName,  -- 7
            c.MainPhoneNo,             -- 8
            c.Address,                 -- 9

            -- بيانات المدينة
            ISNULL(city.NameAr, 'غير محدد') AS CityName,   -- 10

            -- بيانات من أنشأ التصميم
            addUser.UserName AS AddedByUser,     -- 11
            addUser.FullName AS AddedByFullName, -- 12

            d.AddDate                  -- 13
            
        FROM Sys_Designs d
        INNER JOIN Acc_Customers c ON d.CustomerId = c.CustomerCode
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON d.AddUser = addUser.UserId
        
        WHERE d.IsDeleted = 0 AND c.IsDeleted = 0
        ORDER BY d.AddDate DESC
        """
        
        cursor.execute(query)
        result = cursor.fetchone()
        conn.close()
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في الحصول على تفاصيل التصميم: {str(e)}")
        return None

def get_latest_contract_with_details():
    """الحصول على آخر عقد مع جميع التفاصيل"""
    try:
        conn = connect_to_db()
        if not conn:
            return None
        
        cursor = conn.cursor()
        
        query = """
        SELECT TOP 1
            ct.ContractId,            -- 0 - المعرف الفريد
            ct.ContractCode,          -- 1
            ct.Date AS ContractDate,  -- 2
            ct.Notes AS ContractNotes,-- 3
            ct.BranchId,              -- 4
            CASE
                WHEN ct.BranchId = 2 THEN 'فرع مدينة نصر'
                WHEN ct.BranchId = 3 THEN 'فرع التجمع'
                ELSE 'غير محدد'
            END AS BranchName,         -- 5

            -- بيانات العميل
            c.CustomerCode,            -- 6
            c.NameAr AS CustomerName,  -- 7
            c.MainPhoneNo,             -- 8
            c.Address,                 -- 9

            -- بيانات المدينة
            ISNULL(city.NameAr, 'غير محدد') AS CityName,   -- 10

            -- بيانات من أنشأ العقد
            addUser.UserName AS AddedByUser,     -- 11
            addUser.FullName AS AddedByFullName, -- 12

            ct.AddDate                 -- 13
            
        FROM Acc_Contracts ct
        INNER JOIN Acc_Customers c ON ct.CustomerId = c.CustomerCode
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON ct.AddUser = addUser.UserId
        
        WHERE ct.IsDeleted = 0 AND c.IsDeleted = 0
        ORDER BY ct.AddDate DESC
        """
        
        cursor.execute(query)
        result = cursor.fetchone()
        conn.close()
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في الحصول على تفاصيل العقد: {str(e)}")
        return None

def send_notification_sync(message):
    """دالة مساعدة لإرسال الإشعارات من الدوال العادية"""
    import threading

    def run_async():
        try:
            asyncio.run(send_notification(message))
        except Exception as e:
            print(f"❌ خطأ في إرسال الإشعار: {str(e)}")
            if logger:
                logger.error(f"خطأ في إرسال الإشعار: {str(e)}")

    # تشغيل الدالة في thread منفصل لتجنب تضارب حلقات الأحداث
    thread = threading.Thread(target=run_async)
    thread.start()
    thread.join()  # انتظار انتهاء الإرسال

async def send_notification(message):
    """إرسال إشعار للمستخدمين المخولين"""
    try:
        if not message:
            warning_msg = "⚠️ محاولة إرسال رسالة فارغة"
            print(warning_msg)
            if logger:
                logger.warning(warning_msg)
            return

        bot = Bot(token=BOT_TOKEN)
        notification_text = f"🔔 **إشعار تلقائي من Terra Bot**\n\n{message}\n\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # الحصول على قائمة المستقبلين النشطين
        active_recipients = get_active_recipients()

        start_msg = f"📤 بدء إرسال إشعار إلى {len(active_recipients)} مستقبل"
        print(start_msg)
        if logger:
            logger.info(start_msg)

        for chat_id in active_recipients:
            try:
                await bot.send_message(
                    chat_id=chat_id,
                    text=notification_text,
                    parse_mode='Markdown'
                )
                success_msg = f"✅ تم إرسال إشعار إلى {chat_id}"
                print(success_msg)
                if logger:
                    logger.info(success_msg)

            except Exception as e:
                error_msg = f"❌ فشل إرسال إشعار إلى {chat_id}: {str(e)}"
                print(error_msg)
                if logger:
                    logger.error(error_msg)

    except Exception as e:
        error_msg = f"❌ خطأ في إرسال الإشعارات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)

def check_new_customers():
    """فحص العملاء الجدد مع التفاصيل الشاملة والتحقق من قاعدة البيانات حسب الفرع"""

    try:
        # الحصول على كل العملاء الجدد بعد آخر معرف
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # استعلام للحصول على كل العملاء الجدد
        query = """
        SELECT
            c.CustomerId, c.CustomerCode, c.NameAr, c.NameEn, c.MainPhoneNo,
            c.SubMainPhoneNo, c.Email, c.Address, c.NationalId, c.Notes,
            c.AddDate, c.UpdateDate, c.BranchId,
            CASE
                WHEN c.BranchId = 2 THEN 'فرع مدينة نصر'
                WHEN c.BranchId = 3 THEN 'فرع التجمع'
                ELSE 'فرع غير محدد'
            END AS BranchName,
            ISNULL(city.NameAr, 'غير محدد') AS CityName,
            ISNULL(pay.NameAr, 'غير محدد') AS PayTypeName,
            ISNULL(social.NameAr, 'غير محدد') AS SocialMediaName,
            ISNULL(addUser.UserName, 'غير محدد') AS AddedByUser,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            ISNULL(updateUser.UserName, '') AS UpdatedByUser,
            ISNULL(updateUser.FullName, '') AS UpdatedByFullName
        FROM Acc_Customers c
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Acc_PayType pay ON c.PayTypeId = pay.PayTypeId
        LEFT JOIN Sys_SocialMedia social ON c.SocialMediaId = social.SocialMediaId
        LEFT JOIN Sys_Users addUser ON c.AddUser = addUser.UserId
        LEFT JOIN Sys_Users updateUser ON c.UpdateUser = updateUser.UserId
        WHERE c.IsDeleted = 0 AND c.BranchId = ?
        AND c.CustomerId NOT IN (
            SELECT RecordUniqueId FROM Terra_Notifications_DB1.dbo.Notifications_Log
            WHERE DataType = 'عميل' AND BranchId = ? AND IsActive = 1 AND RecordUniqueId IS NOT NULL
        )
        ORDER BY c.CustomerId ASC
        """

        # فحص كل فرع منفصل
        notifications_sent = 0
        branches = [2, 3]  # فرع مدينة نصر = 2، فرع التجمع = 3

        for branch_id in branches:
            cursor.execute(query, (branch_id, branch_id))
            new_customers = cursor.fetchall()

            # معالجة كل عميل جديد في هذا الفرع
            for customer in new_customers:

                # تحديد أيقونة الفرع
                branch_icon = "🏢" if customer[12] == 2 else "🏬" if customer[12] == 3 else "🏪"

                message = f"""👤 **عميل جديد تم إضافته**

{branch_icon} **الفرع:** {customer[13]}

🔢 **الكود:** {customer[1]}
👤 **الاسم العربي:** {customer[2] or 'غير محدد'}
🔤 **الاسم الإنجليزي:** {customer[3] or 'غير محدد'}
📱 **الهاتف الأساسي:** {customer[4] or 'غير محدد'}
📞 **الهاتف الفرعي:** {customer[5] or 'غير محدد'}
📧 **الإيميل:** {customer[6] or 'غير محدد'}
🏠 **العنوان:** {customer[7] or 'غير محدد'}
🆔 **الرقم القومي:** {customer[8] or 'غير محدد'}
📝 **ملاحظات:** {customer[9] or 'لا توجد'}

🌍 **المدينة/المنطقة:** {customer[14]}
💳 **طريقة الدفع:** {customer[15]}
📱 **وسيلة التواصل:** {customer[16]}

👨‍💼 **تم الإنشاء بواسطة:** {customer[18]}
📅 **تاريخ الإضافة:** {customer[10].strftime('%Y-%m-%d %H:%M') if customer[10] else 'غير محدد'}"""

            if customer[11]:  # إذا كان هناك تاريخ تحديث
                message += f"\n🔄 **آخر تحديث:** {customer[11].strftime('%Y-%m-%d %H:%M')}"
                if customer[19]:  # إذا كان هناك من حدث
                    message += f"\n👨‍🔧 **تم التحديث بواسطة:** {customer[20]}"

            # حفظ في قاعدة البيانات
            save_notification_to_db(
                'عميل',
                customer[1],  # CustomerCode
                customer[2] or 'غير محدد',  # NameAr
                customer[12],  # BranchId
                customer[13],  # BranchName
                customer[0],   # CustomerId (المعرف الفريد)
                customer[10],  # AddDate
                customer[18]   # AddedByFullName
            )

            # إرسال الإشعار
            send_notification_sync(message)
            notifications_sent += 1
            print(f"✅ تم إرسال إشعار للعميل {customer[1]} (ID: {customer[0]})")

        conn.close()

        if notifications_sent > 0:
            print(f"📊 تم إرسال {notifications_sent} إشعار عميل جديد")
            return None  # لا نرسل رسالة إضافية لأن كل عميل أرسل إشعار منفصل

        return None

    except Exception as e:
        print(f"❌ خطأ في فحص العملاء الجدد: {str(e)}")
        return None

def check_new_previews():
    """فحص المعاينات الجديدة مع التفاصيل الشاملة والتحقق من قاعدة البيانات حسب الفرع"""

    try:
        # الحصول على كل المعاينات الجديدة بعد آخر معرف لكل فرع
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # استعلام للحصول على كل المعاينات الجديدة
        query = """
        SELECT
            p.PreviewId, p.PreviewCode, p.Date AS PreviewDate, p.Notes, p.BranchId,
            CASE
                WHEN p.BranchId = 2 THEN 'فرع مدينة نصر'
                WHEN p.BranchId = 3 THEN 'فرع التجمع'
                ELSE 'فرع غير محدد'
            END AS BranchName,
            c.CustomerCode, c.NameAr AS CustomerName, c.MainPhoneNo, c.Address,
            ISNULL(city.NameAr, 'غير محدد') AS CityName,
            ISNULL(addUser.UserName, 'غير محدد') AS AddedByUser,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            p.AddDate
        FROM Sys_Previews p
        INNER JOIN Acc_Customers c ON p.CustomerId = c.CustomerCode
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON p.AddUser = addUser.UserId
        WHERE p.IsDeleted = 0 AND c.IsDeleted = 0 AND c.BranchId = ?
        AND p.PreviewId NOT IN (
            SELECT RecordUniqueId FROM Terra_Notifications_DB1.dbo.Notifications_Log
            WHERE DataType = 'معاينة' AND BranchId = ? AND IsActive = 1 AND RecordUniqueId IS NOT NULL
        )
        ORDER BY p.PreviewId ASC
        """

        # فحص كل فرع منفصل
        notifications_sent = 0
        branches = [2, 3]  # فرع مدينة نصر = 2، فرع التجمع = 3

        for branch_id in branches:
            cursor.execute(query, (branch_id, branch_id))
            new_previews = cursor.fetchall()

            # معالجة كل معاينة جديدة في هذا الفرع
            for preview in new_previews:

                # تحديد أيقونة الفرع
                branch_icon = "🏢" if preview[4] == 2 else "🏬" if preview[4] == 3 else "🏪"

                message = f"""👁️ **معاينة جديدة تم إضافتها**

{branch_icon} **الفرع:** {preview[5]}

🔢 **كود المعاينة:** {preview[1]}
📅 **تاريخ المعاينة:** {preview[2].strftime('%Y-%m-%d %H:%M') if preview[2] else 'غير محدد'}
📝 **ملاحظات المعاينة:** {preview[3] or 'لا توجد'}

👤 **بيانات العميل:**
🔢 **كود العميل:** {preview[6]}
👤 **اسم العميل:** {preview[7] or 'غير محدد'}
📱 **هاتف العميل:** {preview[8] or 'غير محدد'}
🏠 **عنوان العميل:** {preview[9] or 'غير محدد'}
🌍 **المدينة/المنطقة:** {preview[10] or 'غير محدد'}

👨‍💼 **تم الإنشاء بواسطة:** {preview[12] or 'غير محدد'}
📅 **تاريخ الإضافة:** {preview[13].strftime('%Y-%m-%d %H:%M') if preview[13] else 'غير محدد'}"""

                # حفظ في قاعدة البيانات
                save_notification_to_db(
                    'معاينة',
                    preview[1],  # PreviewCode
                    f"معاينة للعميل {preview[7] or 'غير محدد'}",
                    preview[4],  # BranchId
                    preview[5],  # BranchName
                    preview[0],  # PreviewId (المعرف الفريد)
                    preview[13], # AddDate
                    preview[12] or 'غير محدد'  # AddedByFullName
                )

                # إرسال الإشعار
                send_notification_sync(message)
                notifications_sent += 1
                print(f"✅ تم إرسال إشعار للمعاينة {preview[1]} (ID: {preview[0]})")

        conn.close()

        if notifications_sent > 0:
            print(f"📊 تم إرسال {notifications_sent} إشعار معاينة جديدة")
            return None  # لا نرسل رسالة إضافية لأن كل معاينة أرسلت إشعار منفصل

        return None

    except Exception as e:
        print(f"❌ خطأ في فحص المعاينات الجديدة: {str(e)}")
        return None

def check_new_meetings():
    """فحص الاجتماعات الجديدة مع التفاصيل الشاملة والتحقق من قاعدة البيانات حسب الفرع"""

    try:
        # الحصول على كل الاجتماعات الجديدة بعد آخر معرف لكل فرع
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # استعلام للحصول على كل الاجتماعات الجديدة
        query = """
        SELECT
            m.MeetingId, m.MeetingCode, m.Date AS MeetingDate, m.Notes, m.BranchId,
            CASE
                WHEN m.BranchId = 2 THEN 'فرع مدينة نصر'
                WHEN m.BranchId = 3 THEN 'فرع التجمع'
                ELSE 'فرع غير محدد'
            END AS BranchName,
            c.CustomerCode, c.NameAr AS CustomerName, c.MainPhoneNo, c.Address,
            ISNULL(city.NameAr, 'غير محدد') AS CityName,
            ISNULL(addUser.UserName, 'غير محدد') AS AddedByUser,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            m.AddDate
        FROM Sys_Meetings m
        INNER JOIN Acc_Customers c ON m.CustomerId = c.CustomerCode
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON m.AddUser = addUser.UserId
        WHERE m.IsDeleted = 0 AND c.IsDeleted = 0 AND c.BranchId = ?
        AND m.MeetingId NOT IN (
            SELECT RecordUniqueId FROM Terra_Notifications_DB1.dbo.Notifications_Log
            WHERE DataType = 'اجتماع' AND BranchId = ? AND IsActive = 1 AND RecordUniqueId IS NOT NULL
        )
        ORDER BY m.MeetingId ASC
        """

        # فحص كل فرع منفصل
        notifications_sent = 0
        branches = [2, 3]  # فرع مدينة نصر = 2، فرع التجمع = 3

        for branch_id in branches:
            cursor.execute(query, (branch_id, branch_id))
            new_meetings = cursor.fetchall()

            # معالجة كل اجتماع جديد في هذا الفرع
            for meeting in new_meetings:

                # تحديد أيقونة الفرع
                branch_icon = "🏢" if meeting[4] == 2 else "🏬" if meeting[4] == 3 else "🏪"

                message = f"""🤝 **اجتماع جديد تم إضافته**

{branch_icon} **الفرع:** {meeting[5]}

🔢 **كود الاجتماع:** {meeting[1]}
📅 **تاريخ الاجتماع:** {meeting[2].strftime('%Y-%m-%d %H:%M') if meeting[2] else 'غير محدد'}
📝 **ملاحظات الاجتماع:** {meeting[3] or 'لا توجد'}

👤 **بيانات العميل:**
🔢 **كود العميل:** {meeting[6]}
👤 **اسم العميل:** {meeting[7] or 'غير محدد'}
📱 **هاتف العميل:** {meeting[8] or 'غير محدد'}
🏠 **عنوان العميل:** {meeting[9] or 'غير محدد'}
🌍 **المدينة/المنطقة:** {meeting[10] or 'غير محدد'}

👨‍💼 **تم الإنشاء بواسطة:** {meeting[12] or 'غير محدد'}
📅 **تاريخ الإضافة:** {meeting[13].strftime('%Y-%m-%d %H:%M') if meeting[13] else 'غير محدد'}"""

                # حفظ في قاعدة البيانات
                save_notification_to_db(
                    'اجتماع',
                    meeting[1],  # MeetingCode
                    f"اجتماع مع العميل {meeting[7] or 'غير محدد'}",
                    meeting[4],  # BranchId
                    meeting[5],  # BranchName
                    meeting[0],  # MeetingId (المعرف الفريد)
                    meeting[13], # AddDate
                    meeting[12] or 'غير محدد'  # AddedByFullName
                )

                # إرسال الإشعار
                send_notification_sync(message)
                notifications_sent += 1
                print(f"✅ تم إرسال إشعار للاجتماع {meeting[1]} (ID: {meeting[0]})")

        conn.close()

        if notifications_sent > 0:
            print(f"📊 تم إرسال {notifications_sent} إشعار اجتماع جديد")
            return None  # لا نرسل رسالة إضافية لأن كل اجتماع أرسل إشعار منفصل

        return None

    except Exception as e:
        error_msg = f"❌ خطأ في فحص الاجتماعات الجديدة: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

def check_new_designs():
    """فحص التصميمات الجديدة مع التفاصيل الشاملة والتحقق من قاعدة البيانات حسب الفرع"""

    try:
        # الحصول على كل التصميمات الجديدة بعد آخر معرف لكل فرع
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # استعلام للحصول على كل التصميمات الجديدة
        query = """
        SELECT
            d.DesignId, d.DesignCode, d.Date AS DesignDate, d.Notes, d.BranchId,
            CASE
                WHEN d.BranchId = 2 THEN 'فرع مدينة نصر'
                WHEN d.BranchId = 3 THEN 'فرع التجمع'
                ELSE 'فرع غير محدد'
            END AS BranchName,
            c.CustomerCode, c.NameAr AS CustomerName, c.MainPhoneNo, c.Address,
            ISNULL(city.NameAr, 'غير محدد') AS CityName,
            ISNULL(addUser.UserName, 'غير محدد') AS AddedByUser,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            d.AddDate
        FROM Sys_Designs d
        INNER JOIN Acc_Customers c ON d.CustomerId = c.CustomerCode
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON d.AddUser = addUser.UserId
        WHERE d.IsDeleted = 0 AND c.IsDeleted = 0 AND c.BranchId = ?
        AND d.DesignId NOT IN (
            SELECT RecordUniqueId FROM Terra_Notifications_DB1.dbo.Notifications_Log
            WHERE DataType = 'تصميم' AND BranchId = ? AND IsActive = 1 AND RecordUniqueId IS NOT NULL
        )
        ORDER BY d.DesignId ASC
        """

        # فحص كل فرع منفصل
        notifications_sent = 0
        branches = [2, 3]  # فرع مدينة نصر = 2، فرع التجمع = 3

        for branch_id in branches:
            cursor.execute(query, (branch_id, branch_id))
            new_designs = cursor.fetchall()

            # معالجة كل تصميم جديد في هذا الفرع
            for design in new_designs:

                # تحديد أيقونة الفرع
                branch_icon = "🏢" if design[4] == 2 else "🏬" if design[4] == 3 else "🏪"

                message = f"""🎨 **تصميم جديد تم إضافته**

{branch_icon} **الفرع:** {design[5]}

🔢 **كود التصميم:** {design[1]}
📅 **تاريخ التصميم:** {design[2].strftime('%Y-%m-%d %H:%M') if design[2] else 'غير محدد'}
📝 **ملاحظات التصميم:** {design[3] or 'لا توجد'}

👤 **بيانات العميل:**
🔢 **كود العميل:** {design[6]}
👤 **اسم العميل:** {design[7] or 'غير محدد'}
📱 **هاتف العميل:** {design[8] or 'غير محدد'}
🏠 **عنوان العميل:** {design[9] or 'غير محدد'}
🌍 **المدينة/المنطقة:** {design[10] or 'غير محدد'}

👨‍💼 **تم الإنشاء بواسطة:** {design[12] or 'غير محدد'}
📅 **تاريخ الإضافة:** {design[13].strftime('%Y-%m-%d %H:%M') if design[13] else 'غير محدد'}"""

                # حفظ في قاعدة البيانات
                save_notification_to_db(
                    'تصميم',
                    design[1],  # DesignCode
                    f"تصميم للعميل {design[7] or 'غير محدد'}",
                    design[4],  # BranchId
                    design[5],  # BranchName
                    design[0],  # DesignId (المعرف الفريد)
                    design[13], # AddDate
                    design[12] or 'غير محدد'  # AddedByFullName
                )

                # إرسال الإشعار
                send_notification_sync(message)
                notifications_sent += 1
                print(f"✅ تم إرسال إشعار للتصميم {design[1]} (ID: {design[0]})")

        conn.close()

        if notifications_sent > 0:
            print(f"📊 تم إرسال {notifications_sent} إشعار تصميم جديد")
            return None  # لا نرسل رسالة إضافية لأن كل تصميم أرسل إشعار منفصل

        return None

    except Exception as e:
        error_msg = f"❌ خطأ في فحص التصميمات الجديدة: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

def check_new_contracts():
    """فحص العقود الجديدة مع التفاصيل الشاملة والتحقق من قاعدة البيانات حسب الفرع"""

    try:
        # الحصول على كل العقود الجديدة بعد آخر معرف لكل فرع
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # استعلام للحصول على كل العقود الجديدة
        query = """
        SELECT
            ct.ContractId, ct.ContractCode, ct.Date AS ContractDate, ct.Notes, ct.BranchId,
            CASE
                WHEN ct.BranchId = 2 THEN 'فرع مدينة نصر'
                WHEN ct.BranchId = 3 THEN 'فرع التجمع'
                ELSE 'فرع غير محدد'
            END AS BranchName,
            c.CustomerCode, c.NameAr AS CustomerName, c.MainPhoneNo, c.Address,
            ISNULL(city.NameAr, 'غير محدد') AS CityName,
            ISNULL(addUser.UserName, 'غير محدد') AS AddedByUser,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            ct.AddDate
        FROM Acc_Contracts ct
        INNER JOIN Acc_Customers c ON ct.CustomerId = c.CustomerCode
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON ct.AddUser = addUser.UserId
        WHERE ct.IsDeleted = 0 AND c.IsDeleted = 0 AND c.BranchId = ?
        AND ct.ContractId NOT IN (
            SELECT RecordUniqueId FROM Terra_Notifications_DB1.dbo.Notifications_Log
            WHERE DataType = 'عقد' AND BranchId = ? AND IsActive = 1 AND RecordUniqueId IS NOT NULL
        )
        ORDER BY ct.ContractId ASC
        """

        # فحص كل فرع منفصل
        notifications_sent = 0
        branches = [2, 3]  # فرع مدينة نصر = 2، فرع التجمع = 3

        for branch_id in branches:
            cursor.execute(query, (branch_id, branch_id))
            new_contracts = cursor.fetchall()

            # معالجة كل عقد جديد في هذا الفرع
            for contract in new_contracts:

                # تحديد أيقونة الفرع
                branch_icon = "🏢" if contract[4] == 2 else "🏬" if contract[4] == 3 else "🏪"

                message = f"""📄 **عقد جديد تم إضافته**

{branch_icon} **الفرع:** {contract[5]}

🔢 **كود العقد:** {contract[1]}
📅 **تاريخ العقد:** {contract[2].strftime('%Y-%m-%d %H:%M') if contract[2] else 'غير محدد'}
📝 **ملاحظات العقد:** {contract[3] or 'لا توجد'}

👤 **بيانات العميل:**
🔢 **كود العميل:** {contract[6]}
👤 **اسم العميل:** {contract[7] or 'غير محدد'}
📱 **هاتف العميل:** {contract[8] or 'غير محدد'}
🏠 **عنوان العميل:** {contract[9] or 'غير محدد'}
🌍 **المدينة/المنطقة:** {contract[10] or 'غير محدد'}

👨‍💼 **تم الإنشاء بواسطة:** {contract[12] or 'غير محدد'}
📅 **تاريخ الإضافة:** {contract[13].strftime('%Y-%m-%d %H:%M') if contract[13] else 'غير محدد'}"""

                # حفظ في قاعدة البيانات
                save_notification_to_db(
                    'عقد',
                    contract[1],  # ContractCode
                    f"عقد للعميل {contract[7] or 'غير محدد'}",
                    contract[4],  # BranchId
                    contract[5],  # BranchName
                    contract[0],  # ContractId (المعرف الفريد)
                    contract[13], # AddDate
                    contract[12] or 'غير محدد'  # AddedByFullName
                )

                # إرسال الإشعار
                send_notification_sync(message)
                notifications_sent += 1
                print(f"✅ تم إرسال إشعار للعقد {contract[1]} (ID: {contract[0]})")

        conn.close()

        if notifications_sent > 0:
            print(f"📊 تم إرسال {notifications_sent} إشعار عقد جديد")
            return None  # لا نرسل رسالة إضافية لأن كل عقد أرسل إشعار منفصل

        return None

    except Exception as e:
        error_msg = f"❌ خطأ في فحص العقود الجديدة: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

def initialize_last_ids():
    """تهيئة آخر المعرفات من قاعدة بيانات الإشعارات وقاعدة البيانات الأصلية"""
    global last_customer_ids, last_preview_ids, last_meeting_ids, last_design_ids, last_contract_ids

    try:
        print("🔄 تهيئة نظام قاعدة البيانات والمعرفات...")

        # إنشاء جداول قاعدة بيانات الإشعارات إذا لم تكن موجودة
        init_notifications_db()

        # تحميل المعرفات من قاعدة بيانات الإشعارات أولاً
        load_last_ids_from_db()

        print(f"✅ تم تهيئة المعرفات النهائية حسب الفرع:")
        print(f"   🏢 فرع مدينة نصر (2):")
        print(f"      عميل: {last_customer_ids[2]}")
        print(f"      معاينة: {last_preview_ids[2]}")
        print(f"      اجتماع: {last_meeting_ids[2]}")
        print(f"      تصميم: {last_design_ids[2]}")
        print(f"      عقد: {last_contract_ids[2]}")
        print(f"   🏬 فرع التجمع (3):")
        print(f"      عميل: {last_customer_ids[3]}")
        print(f"      معاينة: {last_preview_ids[3]}")
        print(f"      اجتماع: {last_meeting_ids[3]}")
        print(f"      تصميم: {last_design_ids[3]}")
        print(f"      عقد: {last_contract_ids[3]}")

    except Exception as e:
        error_msg = f"❌ خطأ في تهيئة المعرفات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)

async def monitor_all_data():
    """مراقبة جميع البيانات مع التفاصيل الشاملة"""
    start_msg = "🔍 بدء مراقبة شاملة لجميع البيانات..."
    print(start_msg)
    print("📊 سيتم عرض تفاصيل كاملة مع الربط بجميع الجداول")
    print("⚠️ قراءة فقط - لا يتم كتابة أي بيانات")

    if logger:
        logger.info(start_msg)
        logger.info("📊 سيتم عرض تفاصيل كاملة مع الربط بجميع الجداول")

    # تهيئة المعرفات
    initialize_last_ids()

    cycle = 0

    while True:
        try:
            cycle += 1
            current_time = datetime.now().strftime('%H:%M:%S')
            cycle_msg = f"🔄 دورة مراقبة شاملة #{cycle} - {current_time}"
            print(f"\n{cycle_msg}")

            if logger and cycle % 10 == 1:  # كل 10 دورات
                logger.info(cycle_msg)

            # فحص العملاء الجدد
            new_customer = check_new_customers()
            if new_customer:
                discovery_msg = "🔔 تم اكتشاف عميل جديد!"
                print(discovery_msg)
                if logger:
                    logger.info(discovery_msg)
                await send_notification(new_customer)

            # فحص المعاينات الجديدة
            new_preview = check_new_previews()
            if new_preview:
                discovery_msg = "🔔 تم اكتشاف معاينة جديدة!"
                print(discovery_msg)
                if logger:
                    logger.info(discovery_msg)
                await send_notification(new_preview)

            # فحص الاجتماعات الجديدة
            new_meeting = check_new_meetings()
            if new_meeting:
                discovery_msg = "🔔 تم اكتشاف اجتماع جديد!"
                print(discovery_msg)
                if logger:
                    logger.info(discovery_msg)
                await send_notification(new_meeting)

            # فحص التصميمات الجديدة
            new_design = check_new_designs()
            if new_design:
                discovery_msg = "🔔 تم اكتشاف تصميم جديد!"
                print(discovery_msg)
                if logger:
                    logger.info(discovery_msg)
                await send_notification(new_design)

            # فحص العقود الجديدة
            new_contract = check_new_contracts()
            if new_contract:
                discovery_msg = "🔔 تم اكتشاف عقد جديد!"
                print(discovery_msg)
                if logger:
                    logger.info(discovery_msg)
                await send_notification(new_contract)

            if not any([new_customer, new_preview, new_meeting, new_design, new_contract]):
                no_data_msg = "📊 لا توجد بيانات جديدة"
                print(no_data_msg)
                if logger and cycle % 20 == 0:  # كل 20 دورة
                    logger.debug(no_data_msg)

            # انتظار 30 ثانية
            await asyncio.sleep(30)

        except KeyboardInterrupt:
            stop_msg = "\n⏹️ تم إيقاف المراقبة"
            print(stop_msg)
            if logger:
                logger.info(stop_msg)
            break
        except Exception as e:
            error_msg = f"❌ خطأ في المراقبة: {str(e)}"
            print(error_msg)
            if logger:
                logger.error(error_msg)
            await asyncio.sleep(60)

# دوال البوت
async def start(update, context):
    """دالة البداية"""
    if not update.effective_user or not update.message:
        return

    user_id = update.effective_user.id
    user_name = update.effective_user.first_name or "مستخدم"

    welcome_text = f"""🌟 **مرحباً بك في Terra Bot المحسن** 🌟

👋 أهلاً {user_name}!

🔔 **نظام الإشعارات التلقائية نشط**
📊 **مراقبة قاعدة البيانات مستمرة**

💡 **الأوامر المتاحة:**
/start - العودة للقائمة الرئيسية
/test - اختبار قاعدة البيانات
/id - عرض معرف المستخدم/الجروب
/recipients - عرض قائمة مستقبلي الإشعارات

⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

    await update.message.reply_text(
        welcome_text,
        parse_mode='Markdown'
    )

async def get_id(update, context):
    """دالة عرض معرف المستخدم أو الجروب"""
    if not update.effective_user or not update.message:
        return

    try:
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        chat_type = update.effective_chat.type

        # معلومات المستخدم
        user_info = f"👤 **معلومات المستخدم:**\n"
        user_info += f"🆔 **معرف المستخدم:** `{user_id}`\n"
        user_info += f"👤 **الاسم:** {update.effective_user.first_name or 'غير محدد'}"

        if update.effective_user.last_name:
            user_info += f" {update.effective_user.last_name}"

        if update.effective_user.username:
            user_info += f"\n📝 **اسم المستخدم:** @{update.effective_user.username}"

        # معلومات المحادثة
        chat_info = f"\n\n💬 **معلومات المحادثة:**\n"
        chat_info += f"🆔 **معرف المحادثة:** `{chat_id}`\n"

        if chat_type == "private":
            chat_info += f"📱 **نوع المحادثة:** محادثة خاصة"
        elif chat_type == "group":
            chat_info += f"👥 **نوع المحادثة:** جروب"
            if update.effective_chat.title:
                chat_info += f"\n🏷️ **اسم الجروب:** {update.effective_chat.title}"
        elif chat_type == "supergroup":
            chat_info += f"👥 **نوع المحادثة:** سوبر جروب"
            if update.effective_chat.title:
                chat_info += f"\n🏷️ **اسم الجروب:** {update.effective_chat.title}"
        elif chat_type == "channel":
            chat_info += f"📢 **نوع المحادثة:** قناة"
            if update.effective_chat.title:
                chat_info += f"\n🏷️ **اسم القناة:** {update.effective_chat.title}"

        # ملاحظة للإشعارات
        notification_note = f"\n\n🔔 **للإشعارات التلقائية:**\n"
        if chat_type == "private":
            notification_note += f"استخدم هذا المعرف: `{user_id}`"
        else:
            notification_note += f"استخدم هذا المعرف: `{chat_id}`"

        notification_note += f"\n\n💡 **ملاحظة:** أرسل هذا المعرف للمطور لإضافتك لقائمة الإشعارات التلقائية"

        full_message = user_info + chat_info + notification_note

        await update.message.reply_text(
            full_message,
            parse_mode='Markdown'
        )

        print(f"📨 طلب معرف من: {update.effective_user.first_name or 'مستخدم'} - معرف المستخدم: {user_id}, معرف المحادثة: {chat_id}")

    except Exception as e:
        print(f"❌ خطأ في دالة get_id: {str(e)}")
        await update.message.reply_text(
            "❌ حدث خطأ في الحصول على المعرف",
            parse_mode='Markdown'
        )

async def test_db(update, context):
    """اختبار قاعدة البيانات"""
    if not update.effective_user or not update.message:
        return

    await update.message.reply_text("🔄 جاري اختبار قاعدة البيانات...")

    try:
        conn = connect_to_db()
        if not conn:
            await update.message.reply_text("❌ فشل الاتصال بقاعدة البيانات")
            return

        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM Acc_Customers WHERE IsDeleted=0")
        result = cursor.fetchone()
        total_customers = result[0] if result else 0
        conn.close()

        report = f"""🔧 **معلومات قاعدة البيانات:**

🖥️ **الخادم:** `{MAIN_DB_CONFIG['server']}`
🗄️ **قاعدة البيانات الأصلية:** `{MAIN_DB_CONFIG['database']}`
🗄️ **قاعدة بيانات الإشعارات:** `{NOTIFICATIONS_DB_CONFIG['database']}`
👥 **إجمالي العملاء:** {total_customers:,} عميل

✅ **الاتصال سليم**"""

        await update.message.reply_text(report, parse_mode='Markdown')

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في فحص قاعدة البيانات: {str(e)}")

async def manage_recipients(update, context):
    """إدارة قائمة المستقبلين للإشعارات"""
    if not update.effective_user or not update.message:
        return

    try:
        conn = connect_to_notifications_db()
        if not conn:
            await update.message.reply_text("❌ لا يمكن الاتصال بقاعدة بيانات الإشعارات")
            return

        cursor = conn.cursor()
        cursor.execute("""
            SELECT ChatId, RecipientName, RecipientType, IsActive
            FROM Notification_Recipients
            ORDER BY RecipientType, RecipientName
        """)

        results = cursor.fetchall()
        conn.close()

        if results:
            report = "📋 **قائمة مستقبلي الإشعارات:**\n\n"

            active_count = 0
            inactive_count = 0

            for row in results:
                chat_id, name, recipient_type, is_active = row
                status = "✅ نشط" if is_active else "❌ معطل"
                icon = "👤" if recipient_type == 'User' else "👥"

                report += f"{icon} **{name}**\n"
                report += f"🆔 المعرف: `{chat_id}`\n"
                report += f"📊 الحالة: {status}\n\n"

                if is_active:
                    active_count += 1
                else:
                    inactive_count += 1

            report += f"📊 **الإحصائيات:**\n"
            report += f"✅ نشط: {active_count}\n"
            report += f"❌ معطل: {inactive_count}\n"
            report += f"📋 الإجمالي: {len(results)}"

        else:
            report = "⚠️ لا توجد مستقبلين مسجلين في قاعدة البيانات"

        await update.message.reply_text(report, parse_mode='Markdown')

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في عرض المستقبلين: {str(e)}")

async def main():
    """الدالة الرئيسية للبوت"""
    print("🚀 بدء تشغيل Terra Bot المحسن مع الإشعارات")
    print("=" * 50)

    try:
        from telegram.ext import Application, CommandHandler

        # إنشاء التطبيق
        app = Application.builder().token(BOT_TOKEN).build()

        # تهيئة البوت
        await app.initialize()

        # إضافة المعالجات
        app.add_handler(CommandHandler("start", start))
        app.add_handler(CommandHandler("test", test_db))
        app.add_handler(CommandHandler("id", get_id))
        app.add_handler(CommandHandler("recipients", manage_recipients))

        setup_msg = "✅ البوت جاهز!"
        print(setup_msg)
        print("📱 أرسل /start في التليجرام")
        print("🆔 أرسل /id لعرض المعرف")
        print("🧪 أرسل /test لاختبار قاعدة البيانات")
        print("📄 ملف Log: terra_bot.log")
        print("=" * 50)

        if logger:
            logger.info(setup_msg)
            logger.info("🔔 نظام الإشعارات نشط")
            logger.info("📊 مراقبة قاعدة البيانات مستمرة")

        # تشغيل مراقب قاعدة البيانات في مهمة منفصلة
        monitor_task = asyncio.create_task(monitor_all_data())
        print("🔍 تم بدء مراقب قاعدة البيانات")

        # تشغيل البوت
        await app.start()
        await app.updater.start_polling(drop_pending_updates=True)

        # انتظار إيقاف البوت
        await asyncio.Event().wait()

    except ImportError:
        print("❌ خطأ: مكتبات التليجرام غير مثبتة")
        print("💡 شغل: pip install python-telegram-bot")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
        if logger:
            logger.error(f"خطأ في تشغيل البوت: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
