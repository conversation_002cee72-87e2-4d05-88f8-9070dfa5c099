#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إشعارات محسن مع تفاصيل شاملة من جميع الجداول المرتبطة
قراءة فقط - لا يكتب أي بيانات في قاعدة البيانات
"""

import asyncio
import pyodbc
import os
import logging
from datetime import datetime
from telegram import Bot

# إعدادات
BOT_TOKEN = "**********************************************"
# معرفات المستخدمين والجروبات للإشعارات (احتياطية)
DEFAULT_NOTIFICATION_CHAT_IDS = [1107000748, 1206533289, -1002255521584, -1002308493862]

def get_active_recipients(branch_id=None):
    """الحصول على قائمة المستقبلين النشطين من قاعدة بيانات الإشعارات حسب الفرع"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            print("⚠️ لا يمكن الاتصال بقاعدة بيانات الإشعارات - استخدام القائمة الافتراضية")
            if logger:
                logger.warning("لا يمكن الاتصال بقاعدة بيانات الإشعارات - استخدام القائمة الافتراضية")
            return DEFAULT_NOTIFICATION_CHAT_IDS

        cursor = conn.cursor()

        # بناء الاستعلام حسب الفرع
        if branch_id == 2:  # فرع مدينة نصر
            query = """
                SELECT ChatId, RecipientName, RecipientType
                FROM Notification_Recipients
                WHERE IsActive = 1 AND ReceiveNasrBranch = 1
                ORDER BY RecipientType, RecipientName
            """
        elif branch_id == 3:  # فرع التجمع
            query = """
                SELECT ChatId, RecipientName, RecipientType
                FROM Notification_Recipients
                WHERE IsActive = 1 AND ReceiveTajammuBranch = 1
                ORDER BY RecipientType, RecipientName
            """
        else:  # جميع المستقبلين النشطين (للرسائل العامة)
            query = """
                SELECT ChatId, RecipientName, RecipientType
                FROM Notification_Recipients
                WHERE IsActive = 1
                ORDER BY RecipientType, RecipientName
            """

        cursor.execute(query)
        results = cursor.fetchall()
        conn.close()

        if results:
            chat_ids = [row[0] for row in results]
            branch_text = ""
            if branch_id == 2:
                branch_text = " (فرع مدينة نصر)"
            elif branch_id == 3:
                branch_text = " (فرع التجمع)"

            print(f"📋 تم تحميل {len(chat_ids)} مستقبل نشط من قاعدة البيانات{branch_text}:")
            for row in results:
                recipient_type = "👤 مستخدم" if row[2] == 'User' else "👥 جروب"
                print(f"   {recipient_type}: {row[1]} ({row[0]})")

            if logger:
                logger.info(f"📋 تم تحميل {len(chat_ids)} مستقبل نشط من قاعدة البيانات{branch_text}")

            return chat_ids
        else:
            print(f"⚠️ لا توجد مستقبلين نشطين في قاعدة البيانات للفرع {branch_id} - استخدام القائمة الافتراضية")
            if logger:
                logger.warning(f"لا توجد مستقبلين نشطين في قاعدة البيانات للفرع {branch_id} - استخدام القائمة الافتراضية")
            return DEFAULT_NOTIFICATION_CHAT_IDS

    except Exception as e:
        error_msg = f"❌ خطأ في تحميل المستقبلين من قاعدة البيانات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return DEFAULT_NOTIFICATION_CHAT_IDS

# إعدادات قاعدة البيانات الأصلية (Terra) - قراءة فقط
MAIN_DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

# إعدادات قاعدة بيانات الإشعارات (Terra_Notifications_DB1) - قراءة وكتابةانا
NOTIFICATIONS_DB_CONFIG = {
    'server': '.',
    'database': 'Terra_Notifications_DB1',
    'user': 'sa',
    'password': 'Ret_ME@'
}

# متغيرات لتتبع آخر البيانات حسب الفرع
last_customer_ids = {2: 0, 3: 0}  # فرع مدينة نصر: 2، فرع التجمع: 3
last_preview_ids = {2: 0, 3: 0}
last_meeting_ids = {2: 0, 3: 0}
last_design_ids = {2: 0, 3: 0}
last_contract_ids = {2: 0, 3: 0}

# ملف Excel لحفظ سجل الإشعارات
EXCEL_FILE = "terra_notifications_log.xlsx"

# ملف Log لحفظ الأخطاء والأحداث
LOG_FILE = "terra_bot.log"

# إعداد نظام Logging
def setup_logging():
    """إعداد نظام logging لحفظ الأخطاء والأحداث"""
    try:
        # إنشاء logger
        logger = logging.getLogger('TerraBot')
        logger.setLevel(logging.DEBUG)

        # إزالة handlers القديمة إذا وجدت
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # إنشاء formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # إنشاء file handler
        file_handler = logging.FileHandler(LOG_FILE, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)

        # إنشاء console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(console_formatter)

        # إضافة handlers للlogger
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        return logger

    except Exception as e:
        print(f"❌ خطأ في إعداد نظام Logging: {str(e)}")
        return None

# إنشاء logger عام
logger = setup_logging()

def connect_to_main_db():
    """الاتصال بقاعدة البيانات الأصلية (Terra) - قراءة فقط"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={MAIN_DB_CONFIG['server']};DATABASE={MAIN_DB_CONFIG['database']};UID={MAIN_DB_CONFIG['user']};PWD={MAIN_DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        if logger:
            logger.info("✅ تم الاتصال بقاعدة البيانات الأصلية بنجاح")
        return conn
    except Exception as e:
        error_msg = f"❌ خطأ في الاتصال بقاعدة البيانات الأصلية: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

def connect_to_notifications_db():
    """الاتصال بقاعدة بيانات الإشعارات (Terra_Notifications_DB1) - قراءة وكتابة"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={NOTIFICATIONS_DB_CONFIG['server']};DATABASE={NOTIFICATIONS_DB_CONFIG['database']};UID={NOTIFICATIONS_DB_CONFIG['user']};PWD={NOTIFICATIONS_DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        if logger:
            logger.info("✅ تم الاتصال بقاعدة بيانات الإشعارات بنجاح")
        return conn
    except Exception as e:
        error_msg = f"❌ خطأ في الاتصال بقاعدة بيانات الإشعارات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

# للتوافق مع الكود القديم
def connect_to_db():
    """الاتصال بقاعدة البيانات الأصلية - للتوافق مع الكود القديم"""
    return connect_to_main_db()

def init_notifications_db():
    """التحقق من وجود جداول قاعدة بيانات الإشعارات وإنشاؤها إذا لزم الأمر"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return False

        cursor = conn.cursor()

        # فحص وجود جدول سجل الإشعارات
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Notifications_Log' AND xtype='U')
            BEGIN
                CREATE TABLE Notifications_Log (
                    LogId INT IDENTITY(1,1) PRIMARY KEY,
                    DataType NVARCHAR(50) NOT NULL,
                    RecordId INT NOT NULL,
                    RecordName NVARCHAR(255),
                    BranchId INT,
                    BranchName NVARCHAR(100),
                    RecordUniqueId INT,
                    AddDate DATETIME,
                    AddedBy NVARCHAR(100),
                    NotificationDate DATETIME DEFAULT GETDATE(),
                    NotificationStatus NVARCHAR(20) DEFAULT N'تم الإرسال',
                    IsActive BIT DEFAULT 1,
                    Notes NVARCHAR(500),
                    CONSTRAINT UK_Notifications_DataType_RecordId_Branch UNIQUE(DataType, RecordId, BranchId)
                );

                CREATE INDEX IX_Notifications_DataType ON Notifications_Log(DataType);
                CREATE INDEX IX_Notifications_RecordId ON Notifications_Log(RecordId);
                CREATE INDEX IX_Notifications_Date ON Notifications_Log(NotificationDate);
                CREATE INDEX IX_Notifications_Branch ON Notifications_Log(BranchId);
            END

            -- إضافة الأعمدة الجديدة إذا لم تكن موجودة
            IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Notifications_Log' AND COLUMN_NAME = 'BranchId')
            BEGIN
                ALTER TABLE Notifications_Log ADD BranchId INT;
            END

            IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Notifications_Log' AND COLUMN_NAME = 'RecordUniqueId')
            BEGIN
                ALTER TABLE Notifications_Log ADD RecordUniqueId INT;
            END
        """)

        # فحص وجود جدول المستقبلين
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Notification_Recipients' AND xtype='U')
            BEGIN
                CREATE TABLE Notification_Recipients (
                    RecipientId INT IDENTITY(1,1) PRIMARY KEY,
                    ChatId BIGINT NOT NULL UNIQUE,
                    RecipientName NVARCHAR(100) NOT NULL,
                    RecipientType NVARCHAR(20) NOT NULL CHECK (RecipientType IN ('User', 'Group')),
                    IsActive BIT DEFAULT 1,
                    AddDate DATETIME DEFAULT GETDATE(),
                    Notes NVARCHAR(500)
                );

                CREATE INDEX IX_Recipients_ChatId ON Notification_Recipients(ChatId);
                CREATE INDEX IX_Recipients_Active ON Notification_Recipients(IsActive);

                -- إدراج المستقبلين الافتراضيين
                INSERT INTO Notification_Recipients (ChatId, RecipientName, RecipientType, IsActive)
                VALUES
                    (1107000748, N'المستخدم الأول', 'User', 1),
                    (1206533289, N'المستخدم الثاني', 'User', 1),
                    (-1002308493862, N'الجروب الرئيسي', 'Group', 1),
                    (-1002255521584, N'جروب TERRABOT', 'Group', 1);
            END
        """)

        conn.commit()
        conn.close()

        success_msg = "✅ تم التحقق من جداول قاعدة بيانات الإشعارات"
        print(success_msg)
        if logger:
            logger.info(success_msg)
        return True

    except Exception as e:
        error_msg = f"❌ خطأ في إنشاء جداول قاعدة بيانات الإشعارات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return False

def save_notification_to_db(data_type, code, name, branch_id, branch_name, unique_id, add_date, added_by):
    """حفظ بيانات الإشعار في قاعدة بيانات الإشعارات"""
    try:
        print(f"🔄 محاولة حفظ {data_type} - كود {code} - معرف فريد {unique_id}")

        conn = connect_to_notifications_db()
        if not conn:
            print("❌ فشل الاتصال بقاعدة بيانات الإشعارات")
            return False

        cursor = conn.cursor()

        # التحقق أولاً من وجود السجل لتجنب التكرار
        print(f"🔍 فحص وجود السجل: {data_type} - معرف فريد {unique_id}")
        cursor.execute("""
            SELECT COUNT(*) FROM Notifications_Log
            WHERE DataType = ? AND RecordUniqueId = ?
        """, (data_type, unique_id))

        result = cursor.fetchone()
        if result and result[0] > 0:
            # السجل موجود بالفعل
            print(f"⚠️ السجل موجود بالفعل: {data_type} - معرف فريد {unique_id}")
            conn.close()
            return True

        # إدراج السجل الجديد
        print(f"💾 إدراج سجل جديد: {data_type} - كود {code}")
        cursor.execute("""
            INSERT INTO Notifications_Log
            (DataType, RecordId, RecordName, BranchId, BranchName, RecordUniqueId, AddDate, AddedBy, NotificationDate, NotificationStatus)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, GETDATE(), N'تم الإرسال')
        """, (data_type, code, name, branch_id, branch_name, unique_id, add_date, added_by))

        conn.commit()
        conn.close()

        success_msg = f"✅ تم حفظ {data_type} - كود {code} - معرف فريد {unique_id} في قاعدة بيانات الإشعارات"
        print(success_msg)
        if logger:
            logger.info(success_msg)
        return True

    except Exception as e:
        # تجاهل أخطاء UNIQUE KEY constraint لأنها تعني أن السجل موجود بالفعل
        if "UNIQUE KEY constraint" in str(e) or "2627" in str(e):
            print(f"⚠️ السجل موجود بالفعل (UNIQUE constraint): {data_type} - معرف فريد {unique_id}")
            return True

        error_msg = f"❌ خطأ في حفظ البيانات في قاعدة بيانات الإشعارات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return False





def load_last_ids_from_db():
    """تحميل آخر المعرفات من قاعدة بيانات الإشعارات حسب الفرع"""
    global last_customer_ids, last_preview_ids, last_meeting_ids, last_design_ids, last_contract_ids

    try:
        conn = connect_to_notifications_db()
        if not conn:
            print("📊 قاعدة بيانات الإشعارات غير متاحة - سيتم البدء من الصفر")
            return

        cursor = conn.cursor()

        # الحصول على آخر معرف لكل نوع
        data_types = ['عميل', 'معاينة', 'اجتماع', 'تصميم', 'عقد']

        # الحصول على جميع الفروع من قاعدة البيانات تلقائياً
        cursor.execute("SELECT DISTINCT BranchId FROM Acc_Customers WHERE IsDeleted = 0 AND BranchId IS NOT NULL ORDER BY BranchId")
        branches = [row[0] for row in cursor.fetchall()]
        print(f"🏢 الفروع المراقبة: {branches}")

        for data_type in data_types:
            for branch_id in branches:
                cursor.execute("""
                    SELECT TOP 1 RecordUniqueId FROM Notifications_Log
                    WHERE DataType = ? AND BranchId = ? AND IsActive = 1 AND RecordUniqueId IS NOT NULL
                    ORDER BY NotificationDate DESC
                """, (data_type, branch_id))

                result = cursor.fetchone()
                if result:
                    unique_id = result[0]

                    if data_type == 'عميل':
                        last_customer_ids[branch_id] = unique_id
                    elif data_type == 'معاينة':
                        last_preview_ids[branch_id] = unique_id
                    elif data_type == 'اجتماع':
                        last_meeting_ids[branch_id] = unique_id
                    elif data_type == 'تصميم':
                        last_design_ids[branch_id] = unique_id
                    elif data_type == 'عقد':
                        last_contract_ids[branch_id] = unique_id

                    branch_name = "مدينة نصر" if branch_id == 2 else "التجمع"
                    print(f"📊 تم تحميل آخر {data_type} من فرع {branch_name}: {unique_id}")

        conn.close()

        print(f"📊 ملخص المعرفات المحملة من قاعدة البيانات حسب الفرع:")
        print(f"   🏢 فرع مدينة نصر (2): عميل: {last_customer_ids[2]}, معاينة: {last_preview_ids[2]}, اجتماع: {last_meeting_ids[2]}")
        print(f"   🏬 فرع التجمع (3): عميل: {last_customer_ids[3]}, معاينة: {last_preview_ids[3]}, اجتماع: {last_meeting_ids[3]}")

        if logger:
            logger.info(f"📊 تم تحميل المعرفات حسب الفرع - مدينة نصر: عميل:{last_customer_ids[2]}, معاينة:{last_preview_ids[2]} | التجمع: عميل:{last_customer_ids[3]}, معاينة:{last_preview_ids[3]}")

    except Exception as e:
        error_msg = f"❌ خطأ في تحميل البيانات من قاعدة بيانات الإشعارات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)

def get_latest_customer_with_details():
    """الحصول على آخر عميل مع جميع التفاصيل من الجداول المرتبطة"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return None
        
        cursor = conn.cursor()
        
        # استعلام شامل مع جميع الجداول المرتبطة
        query = """
        SELECT TOP 1
            c.CustomerId,                     -- 0 - المعرف الفريد
            c.CustomerCode,                   -- 1
            c.NameAr,                         -- 2
            c.NameEn,                         -- 3
            c.MainPhoneNo,                    -- 4
            c.SubMainPhoneNo,                 -- 5
            c.Email,                          -- 6
            c.Address,                        -- 7
            c.NationalId,                     -- 8
            c.Notes,                          -- 9
            c.AddDate,                        -- 10
            c.UpdateDate,                     -- 11
            c.BranchId,                       -- 12

            -- بيانات الفرع
            ISNULL(branch.NameAr, 'فرع غير محدد') AS BranchName,                -- 13

            -- بيانات المدينة/المنطقة
            ISNULL(city.NameAr, 'غير محدد') AS CityName,     -- 14

            -- بيانات طريقة الدفع
            ISNULL(pay.NameAr, 'غير محدد') AS PayTypeName,   -- 15

            -- بيانات وسائل التواصل
            ISNULL(social.NameAr, 'غير محدد') AS SocialMediaName,  -- 16

            -- بيانات من أنشأ العميل
            ISNULL(addUser.UserName, 'غير محدد') AS AddedByUser,    -- 17
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName, -- 18

            -- بيانات من حدث العميل (إذا وجد)
            ISNULL(updateUser.UserName, '') AS UpdatedByUser,       -- 19
            ISNULL(updateUser.FullName, '') AS UpdatedByFullName    -- 20

        FROM Acc_Customers c
        LEFT JOIN Sys_Branches branch ON c.BranchId = branch.BranchId
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Acc_PayType pay ON c.PayTypeId = pay.PayTypeId
        LEFT JOIN Sys_SocialMedia social ON c.SocialMediaId = social.SocialMediaId
        LEFT JOIN Sys_Users addUser ON c.AddUser = addUser.UserId
        LEFT JOIN Sys_Users updateUser ON c.UpdateUser = updateUser.UserId

        WHERE c.IsDeleted = 0
        ORDER BY c.AddDate DESC
        """
        
        cursor.execute(query)
        result = cursor.fetchone()
        conn.close()
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في الحصول على تفاصيل العميل: {str(e)}")
        return None

def get_latest_preview_with_details():
    """الحصول على آخر معاينة مع جميع التفاصيل"""
    try:
        conn = connect_to_db()
        if not conn:
            return None
        
        cursor = conn.cursor()
        
        query = """
        SELECT TOP 1
            p.PreviewId,              -- 0 - المعرف الفريد
            p.PreviewCode,            -- 1
            p.Date AS PreviewDate,    -- 2
            p.Notes AS PreviewNotes,  -- 3
            p.BranchId,               -- 4
            ISNULL(branch.NameAr, 'فرع غير محدد') AS BranchName,         -- 5

            -- بيانات العميل
            c.CustomerCode,            -- 6
            c.NameAr AS CustomerName,  -- 7
            c.MainPhoneNo,             -- 8
            c.Address,                 -- 9

            -- بيانات المدينة
            ISNULL(city.NameAr, 'غير محدد') AS CityName,   -- 10

            -- بيانات من أنشأ المعاينة
            addUser.UserName AS AddedByUser,     -- 11
            addUser.FullName AS AddedByFullName, -- 12

            p.AddDate                  -- 13
            
        FROM Sys_Previews p
        INNER JOIN Acc_Customers c ON p.CustomerId = c.CustomerId
        LEFT JOIN Sys_Branches branch ON p.BranchId = branch.BranchId
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON p.AddUser = addUser.UserId
        
        WHERE p.IsDeleted = 0 AND c.IsDeleted = 0
        ORDER BY p.AddDate DESC
        """
        
        cursor.execute(query)
        result = cursor.fetchone()
        conn.close()
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في الحصول على تفاصيل المعاينة: {str(e)}")
        return None

def get_latest_meeting_with_details():
    """الحصول على آخر اجتماع مع جميع التفاصيل"""
    try:
        conn = connect_to_db()
        if not conn:
            return None
        
        cursor = conn.cursor()
        
        query = """
        SELECT TOP 1
            m.MeetingId,              -- 0 - المعرف الفريد
            m.MeetingCode,            -- 1
            m.Date AS MeetingDate,    -- 2
            m.Notes AS MeetingNotes,  -- 3
            m.BranchId,               -- 4
            ISNULL(branch.NameAr, 'فرع غير محدد') AS BranchName,         -- 5

            -- بيانات العميل
            c.CustomerCode,            -- 6
            c.NameAr AS CustomerName,  -- 7
            c.MainPhoneNo,             -- 8
            c.Address,                 -- 9

            -- بيانات المدينة
            ISNULL(city.NameAr, 'غير محدد') AS CityName,   -- 10

            -- بيانات من أنشأ الاجتماع
            addUser.UserName AS AddedByUser,     -- 11
            addUser.FullName AS AddedByFullName, -- 12

            m.AddDate                  -- 13
            
        FROM Sys_Meetings m
        INNER JOIN Acc_Customers c ON m.CustomerId = c.CustomerId
        LEFT JOIN Sys_Branches branch ON m.BranchId = branch.BranchId
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON m.AddUser = addUser.UserId
        
        WHERE m.IsDeleted = 0 AND c.IsDeleted = 0
        ORDER BY m.AddDate DESC
        """
        
        cursor.execute(query)
        result = cursor.fetchone()
        conn.close()
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في الحصول على تفاصيل الاجتماع: {str(e)}")
        return None

def get_latest_design_with_details():
    """الحصول على آخر تصميم مع جميع التفاصيل"""
    try:
        conn = connect_to_db()
        if not conn:
            return None
        
        cursor = conn.cursor()
        
        query = """
        SELECT TOP 1
            d.DesignId,               -- 0 - المعرف الفريد
            d.DesignCode,             -- 1
            d.Date AS DesignDate,     -- 2
            d.Notes AS DesignNotes,   -- 3
            d.BranchId,               -- 4
            ISNULL(branch.NameAr, 'فرع غير محدد') AS BranchName,         -- 5

            -- بيانات العميل
            c.CustomerCode,            -- 6
            c.NameAr AS CustomerName,  -- 7
            c.MainPhoneNo,             -- 8
            c.Address,                 -- 9

            -- بيانات المدينة
            ISNULL(city.NameAr, 'غير محدد') AS CityName,   -- 10

            -- بيانات من أنشأ التصميم
            addUser.UserName AS AddedByUser,     -- 11
            addUser.FullName AS AddedByFullName, -- 12

            d.AddDate                  -- 13
            
        FROM Sys_Designs d
        INNER JOIN Acc_Customers c ON d.CustomerId = c.CustomerId
        LEFT JOIN Sys_Branches branch ON d.BranchId = branch.BranchId
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON d.AddUser = addUser.UserId
        
        WHERE d.IsDeleted = 0 AND c.IsDeleted = 0
        ORDER BY d.AddDate DESC
        """
        
        cursor.execute(query)
        result = cursor.fetchone()
        conn.close()
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في الحصول على تفاصيل التصميم: {str(e)}")
        return None

def get_latest_contract_with_details():
    """الحصول على آخر عقد مع جميع التفاصيل"""
    try:
        conn = connect_to_db()
        if not conn:
            return None
        
        cursor = conn.cursor()
        
        query = """
        SELECT TOP 1
            ct.ContractId,            -- 0 - المعرف الفريد
            ct.ContractCode,          -- 1
            ct.Date AS ContractDate,  -- 2
            ct.Notes AS ContractNotes,-- 3
            ct.BranchId,              -- 4
            ISNULL(branch.NameAr, 'فرع غير محدد') AS BranchName,         -- 5

            -- بيانات العميل
            c.CustomerCode,            -- 6
            c.NameAr AS CustomerName,  -- 7
            c.MainPhoneNo,             -- 8
            c.Address,                 -- 9

            -- بيانات المدينة
            ISNULL(city.NameAr, 'غير محدد') AS CityName,   -- 10

            -- بيانات من أنشأ العقد
            addUser.UserName AS AddedByUser,     -- 11
            addUser.FullName AS AddedByFullName, -- 12

            ct.AddDate                 -- 13
            
        FROM Acc_Contracts ct
        INNER JOIN Acc_Customers c ON ct.CustomerId = c.CustomerId
        LEFT JOIN Sys_Branches branch ON ct.BranchId = branch.BranchId
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON ct.AddUser = addUser.UserId
        
        WHERE ct.IsDeleted = 0 AND c.IsDeleted = 0
        ORDER BY ct.AddDate DESC
        """
        
        cursor.execute(query)
        result = cursor.fetchone()
        conn.close()
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في الحصول على تفاصيل العقد: {str(e)}")
        return None

def create_branch_settings_table():
    """إنشاء جدول إعدادات الفروع في قاعدة بيانات الإشعارات"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return False

        cursor = conn.cursor()

        # إنشاء جدول إعدادات الفروع
        create_table_query = """
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Branch_Settings' AND xtype='U')
        CREATE TABLE Branch_Settings (
            BranchId INT PRIMARY KEY,
            BranchName NVARCHAR(100) NOT NULL,
            IsActive BIT DEFAULT 1,
            IncludeInReports BIT DEFAULT 1,
            NotificationEnabled BIT DEFAULT 1,
            CreatedDate DATETIME DEFAULT GETDATE(),
            UpdatedDate DATETIME DEFAULT GETDATE()
        )
        """

        cursor.execute(create_table_query)
        conn.commit()

        # التحقق من وجود بيانات الفروع
        check_query = "SELECT COUNT(*) FROM Branch_Settings"
        cursor.execute(check_query)
        count = cursor.fetchone()[0]

        if count == 0:
            # الحصول على الفروع من قاعدة البيانات الأساسية وإدراجها
            terra_conn = connect_to_main_db()
            if terra_conn:
                terra_cursor = terra_conn.cursor()
                terra_cursor.execute("SELECT BranchId, NameAr FROM Sys_Branches WHERE IsDeleted = 0")
                branches = terra_cursor.fetchall()

                # إدراج الفروع في جدول الإعدادات
                insert_query = """
                INSERT INTO Branch_Settings (BranchId, BranchName, IsActive, IncludeInReports, NotificationEnabled)
                VALUES (?, ?, 1, 1, 1)
                """

                for branch in branches:
                    cursor.execute(insert_query, (branch[0], branch[1]))

                conn.commit()
                terra_cursor.close()
                terra_conn.close()
                print(f"✅ تم إدراج {len(branches)} فرع في جدول إعدادات الفروع")

        cursor.close()
        conn.close()
        return True

    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول إعدادات الفروع: {str(e)}")
        return False

def create_scheduled_reports_table():
    """إنشاء جدول التقارير المجدولة في قاعدة بيانات الإشعارات"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return False

        cursor = conn.cursor()

        # إنشاء جدول التقارير المجدولة
        create_table_query = """
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Scheduled_Reports' AND xtype='U')
        CREATE TABLE Scheduled_Reports (
            ReportId INT IDENTITY(1,1) PRIMARY KEY,
            ReportName NVARCHAR(100) NOT NULL,
            IsEnabled BIT DEFAULT 1,
            DaysCount INT DEFAULT 3,
            PeriodDescription NVARCHAR(50) DEFAULT 'آخر 3 أيام', -- وصف الفترة
            ScheduleType NVARCHAR(20) DEFAULT 'daily', -- daily, weekly, monthly, custom
            ScheduleHour INT DEFAULT 9,
            ScheduleMinute INT DEFAULT 0,
            ScheduleWeekday INT DEFAULT NULL, -- 0=Monday, 6=Sunday (for weekly)
            ScheduleDay INT DEFAULT NULL, -- day of month (for monthly)
            BranchIds NVARCHAR(500) DEFAULT NULL, -- comma separated branch IDs, NULL = all branches
            LastSentDate DATETIME DEFAULT NULL,
            NextSendDate DATETIME DEFAULT NULL, -- موعد الإرسال التالي
            CreatedDate DATETIME DEFAULT GETDATE(),
            UpdatedDate DATETIME DEFAULT GETDATE()
        )
        """

        cursor.execute(create_table_query)
        conn.commit()

        # إدراج التقارير الافتراضية
        default_reports = [
            ("التقرير اليومي", 1, 1, "daily", 9, 0, None, None, None),
            ("التقرير الأسبوعي", 1, 7, "weekly", 10, 0, 6, None, None),  # الأحد
            ("التقرير الشهري", 1, 30, "monthly", 11, 0, None, 1, None),  # أول يوم في الشهر
        ]

        # التحقق من وجود التقارير الافتراضية
        cursor.execute("SELECT COUNT(*) FROM Scheduled_Reports")
        count = cursor.fetchone()[0]

        if count == 0:
            insert_query = """
            INSERT INTO Scheduled_Reports
            (ReportName, IsEnabled, DaysCount, ScheduleType, ScheduleHour, ScheduleMinute, ScheduleWeekday, ScheduleDay, BranchIds)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            for report in default_reports:
                cursor.execute(insert_query, report)

            conn.commit()
            print("✅ تم إنشاء التقارير الافتراضية")

        conn.close()
        return True

    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول التقارير المجدولة: {str(e)}")
        return False

async def check_and_send_scheduled_reports():
    """فحص وإرسال التقارير المجدولة"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return

        cursor = conn.cursor()

        # الحصول على التقارير المفعلة التي حان وقت إرسالها
        current_time = datetime.now()

        query = """
        SELECT ReportId, ReportName, DaysCount, PeriodDescription, BranchIds, ScheduleType, ScheduleHour, ScheduleMinute
        FROM Scheduled_Reports
        WHERE IsEnabled = 1
        AND (
            (ScheduleType = 'daily' AND DATEPART(hour, GETDATE()) = ScheduleHour AND DATEPART(minute, GETDATE()) = ScheduleMinute)
            OR (LastSentDate IS NULL)
            OR (LastSentDate < DATEADD(day, -1, GETDATE()) AND ScheduleType = 'daily')
        )
        """

        cursor.execute(query)
        reports_to_send = cursor.fetchall()

        for report in reports_to_send:
            report_id = report[0]
            report_name = report[1]
            days_count = report[2]
            period_desc = report[3]
            branch_ids = report[4]
            schedule_type = report[5]
            schedule_hour = report[6]
            schedule_minute = report[7]

            # التحقق من الوقت المحدد
            if current_time.hour == schedule_hour and current_time.minute == schedule_minute:
                await send_scheduled_report(report_id, report_name, days_count, period_desc, branch_ids)

        cursor.close()
        conn.close()

    except Exception as e:
        print(f"❌ خطأ في فحص التقارير المجدولة: {str(e)}")

async def send_scheduled_report(report_id, report_name, days_count, period_desc, branch_ids_str):
    """إرسال تقرير مجدول"""
    try:
        # تحديد الفروع المطلوبة
        if branch_ids_str:
            # فروع محددة
            branch_ids = [int(x.strip()) for x in branch_ids_str.split(',') if x.strip().isdigit()]
        else:
            # جميع الفروع النشطة
            branch_ids = get_active_branch_ids()

        # الحصول على بيانات التقرير
        report_data = get_statistics_report(days_count, branch_ids)

        if not report_data:
            print(f"⚠️ لا توجد بيانات للتقرير: {report_name}")
            return

        # تنسيق التقرير
        report_text = format_statistics_report(report_data, days_count, report_name, branch_ids)

        # إضافة معلومات الجدولة
        report_text += f"\n\n📅 **تقرير مجدول:** {report_name}"
        report_text += f"\n⏰ **الفترة:** {period_desc}"
        report_text += f"\n🕙 **وقت الإرسال:** {datetime.now().strftime('%Y-%m-%d %H:%M')}"

        # الحصول على قائمة المستقبلين المفعلين
        recipients = get_enabled_recipients(branch_ids)

        # إرسال التقرير لكل مستقبل
        for recipient in recipients:
            chat_id = recipient[1]
            try:
                await app.bot.send_message(
                    chat_id=chat_id,
                    text=report_text,
                    parse_mode='Markdown'
                )
                print(f"✅ تم إرسال التقرير المجدول إلى: {chat_id}")
            except Exception as e:
                print(f"❌ فشل إرسال التقرير إلى {chat_id}: {str(e)}")

        # تحديث تاريخ آخر إرسال
        update_last_sent_date(report_id)

    except Exception as e:
        print(f"❌ خطأ في إرسال التقرير المجدول: {str(e)}")

def get_active_branch_ids():
    """الحصول على معرفات الفروع النشطة"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return []

        cursor = conn.cursor()
        cursor.execute("SELECT BranchId FROM Branch_Settings WHERE IsActive = 1 AND IncludeInReports = 1")
        branches = cursor.fetchall()

        cursor.close()
        conn.close()

        return [branch[0] for branch in branches]

    except Exception as e:
        print(f"❌ خطأ في الحصول على الفروع النشطة: {str(e)}")
        return []

def get_enabled_recipients(branch_ids=None):
    """الحصول على المستقبلين المفعلين للفروع المحددة"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return []

        cursor = conn.cursor()

        if branch_ids:
            # مستقبلين لفروع محددة
            placeholders = ','.join(['?' for _ in branch_ids])
            query = f"""
            SELECT DISTINCT nr.RecipientId, nr.ChatId, nr.RecipientName
            FROM Notifications_Recipients nr
            WHERE nr.IsEnabled = 1
            AND (nr.BranchId IS NULL OR nr.BranchId IN ({placeholders}))
            """
            cursor.execute(query, branch_ids)
        else:
            # جميع المستقبلين المفعلين
            query = """
            SELECT RecipientId, ChatId, RecipientName
            FROM Notifications_Recipients
            WHERE IsEnabled = 1
            """
            cursor.execute(query)

        recipients = cursor.fetchall()
        cursor.close()
        conn.close()

        return recipients

    except Exception as e:
        print(f"❌ خطأ في الحصول على المستقبلين: {str(e)}")
        return []

def update_last_sent_date(report_id):
    """تحديث تاريخ آخر إرسال للتقرير"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return False

        cursor = conn.cursor()
        cursor.execute("""
            UPDATE Scheduled_Reports
            SET LastSentDate = GETDATE(), UpdatedDate = GETDATE()
            WHERE ReportId = ?
        """, (report_id,))

        conn.commit()
        cursor.close()
        conn.close()
        return True

    except Exception as e:
        print(f"❌ خطأ في تحديث تاريخ الإرسال: {str(e)}")
        return False

def calculate_next_send_date(schedule_type, schedule_hour, schedule_minute, schedule_weekday=None, schedule_day=None):
    """حساب موعد الإرسال التالي للتقرير"""
    from datetime import datetime, timedelta

    now = datetime.now()

    if schedule_type == "daily":
        # يومي - نفس اليوم إذا لم يحن الوقت بعد، وإلا اليوم التالي
        next_date = now.replace(hour=schedule_hour, minute=schedule_minute, second=0, microsecond=0)
        if next_date <= now:
            next_date += timedelta(days=1)

    elif schedule_type == "weekly":
        # أسبوعي - يوم معين من الأسبوع
        days_ahead = schedule_weekday - now.weekday()
        if days_ahead <= 0:  # إذا فات اليوم هذا الأسبوع
            days_ahead += 7
        next_date = now + timedelta(days=days_ahead)
        next_date = next_date.replace(hour=schedule_hour, minute=schedule_minute, second=0, microsecond=0)

    elif schedule_type == "monthly":
        # شهري - يوم معين من الشهر
        if schedule_day:
            try:
                next_date = now.replace(day=schedule_day, hour=schedule_hour, minute=schedule_minute, second=0, microsecond=0)
                if next_date <= now:
                    # الشهر التالي
                    if now.month == 12:
                        next_date = next_date.replace(year=now.year + 1, month=1)
                    else:
                        next_date = next_date.replace(month=now.month + 1)
            except ValueError:
                # إذا كان اليوم غير صالح للشهر (مثل 31 في فبراير)
                next_date = now + timedelta(days=30)
                next_date = next_date.replace(hour=schedule_hour, minute=schedule_minute, second=0, microsecond=0)
        else:
            next_date = now + timedelta(days=30)
            next_date = next_date.replace(hour=schedule_hour, minute=schedule_minute, second=0, microsecond=0)

    else:
        # افتراضي - يومي
        next_date = now + timedelta(days=1)
        next_date = next_date.replace(hour=schedule_hour, minute=schedule_minute, second=0, microsecond=0)

    return next_date

def update_report_next_send_date(report_id):
    """تحديث موعد الإرسال التالي للتقرير"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return False

        cursor = conn.cursor()

        # الحصول على بيانات التقرير
        cursor.execute("""
            SELECT ScheduleType, ScheduleHour, ScheduleMinute, ScheduleWeekday, ScheduleDay
            FROM Scheduled_Reports
            WHERE ReportId = ?
        """, (report_id,))

        report_data = cursor.fetchone()
        if not report_data:
            return False

        # حساب الموعد التالي
        next_date = calculate_next_send_date(
            report_data[0], report_data[1], report_data[2],
            report_data[3], report_data[4]
        )

        # تحديث الموعد في قاعدة البيانات
        cursor.execute("""
            UPDATE Scheduled_Reports
            SET NextSendDate = ?, UpdatedDate = GETDATE()
            WHERE ReportId = ?
        """, (next_date, report_id))

        conn.commit()
        cursor.close()
        conn.close()
        return True

    except Exception as e:
        print(f"❌ خطأ في تحديث موعد الإرسال التالي: {str(e)}")
        return False

def get_scheduled_reports():
    """الحصول على قائمة التقارير المجدولة المفعلة"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return []

        cursor = conn.cursor()

        query = """
        SELECT ReportId, ReportName, DaysCount, ScheduleType, ScheduleHour, ScheduleMinute,
               ScheduleWeekday, ScheduleDay, BranchIds, LastSentDate
        FROM Scheduled_Reports
        WHERE IsEnabled = 1
        ORDER BY ReportName
        """

        cursor.execute(query)
        results = cursor.fetchall()
        conn.close()

        return results

    except Exception as e:
        print(f"❌ خطأ في الحصول على التقارير المجدولة: {str(e)}")
        return []

def update_report_last_sent(report_id):
    """تحديث تاريخ آخر إرسال للتقرير"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return False

        cursor = conn.cursor()

        query = """
        UPDATE Scheduled_Reports
        SET LastSentDate = GETDATE(), UpdatedDate = GETDATE()
        WHERE ReportId = ?
        """

        cursor.execute(query, (report_id,))
        conn.commit()
        conn.close()

        return True

    except Exception as e:
        print(f"❌ خطأ في تحديث تاريخ الإرسال: {str(e)}")
        return False

def get_statistics_report(days=3, branch_ids=None):
    """الحصول على تقرير إحصائي شامل للفترة المحددة"""
    try:
        conn = connect_to_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # بناء شرط الفروع
        branch_filter = ""
        params = [days]

        if branch_ids:
            if isinstance(branch_ids, str):
                branch_ids = [int(x.strip()) for x in branch_ids.split(',') if x.strip()]

            if branch_ids:
                placeholders = ','.join(['?' for _ in branch_ids])
                branch_filter = f" AND BranchId IN ({placeholders})"
                params.extend(branch_ids)

        # استعلام التقرير الإحصائي الشامل
        query = f"""
        DECLARE @Days INT = ?;

        -- العملاء
        WITH CustomersSummary AS (
            SELECT BranchId, COUNT(*) AS CustomersCount
            FROM Acc_Customers
            WHERE AddDate >= DATEADD(DAY, -@Days, CAST(GETDATE() AS DATE))
              AND AddDate < DATEADD(DAY, 1, CAST(GETDATE() AS DATE))
              AND IsDeleted = 0{branch_filter}
            GROUP BY BranchId
        ),
        -- المعاينات
        PreviewsSummary AS (
            SELECT
                BranchId,
                COUNT(*) AS PreviewsCount,
                SUM(ISNULL(TotalValue, 0)) AS PreviewsTotalValue,
                COUNT(CASE WHEN ISNULL(TotalValue, 0) > 0 THEN 1 END) AS PreviewsPaidCount,
                COUNT(CASE WHEN ISNULL(TotalValue, 0) = 0 THEN 1 END) AS PreviewsFreeCount
            FROM Sys_Previews
            WHERE AddDate >= DATEADD(DAY, -@Days, CAST(GETDATE() AS DATE))
              AND AddDate < DATEADD(DAY, 1, CAST(GETDATE() AS DATE))
              AND IsDeleted = 0{branch_filter}
            GROUP BY BranchId
        ),
        -- الاجتماعات
        MeetingsSummary AS (
            SELECT BranchId, COUNT(*) AS MeetingsCount
            FROM Sys_Meetings
            WHERE AddDate >= DATEADD(DAY, -@Days, CAST(GETDATE() AS DATE))
              AND AddDate < DATEADD(DAY, 1, CAST(GETDATE() AS DATE))
              AND IsDeleted = 0{branch_filter}
            GROUP BY BranchId
        ),
        -- التصميمات
        DesignsSummary AS (
            SELECT BranchId, COUNT(*) AS DesignsCount
            FROM Sys_Designs
            WHERE AddDate >= DATEADD(DAY, -@Days, CAST(GETDATE() AS DATE))
              AND AddDate < DATEADD(DAY, 1, CAST(GETDATE() AS DATE))
              AND IsDeleted = 0{branch_filter}
            GROUP BY BranchId
        ),
        -- العقود
        ContractsSummary AS (
            SELECT
                BranchId,
                COUNT(*) AS ContractsCount,
                SUM(ISNULL(TotalValue, 0)) AS ContractsTotalValue,
                SUM(ISNULL(DiscountValue, 0)) AS ContractsTotalDiscount,
                SUM(ISNULL(TotalValue, 0) - ISNULL(DiscountValue, 0)) AS ContractsNetTotal
            FROM Acc_Contracts
            WHERE AddDate >= DATEADD(DAY, -@Days, CAST(GETDATE() AS DATE))
              AND AddDate < DATEADD(DAY, 1, CAST(GETDATE() AS DATE))
              AND IsDeleted = 0{branch_filter}
            GROUP BY BranchId
        ),
        -- العروض
        OffersSummary AS (
            SELECT
                BranchId,
                COUNT(*) AS OffersCount,
                SUM(ISNULL(TotalValue, 0)) AS OffersTotalValue,
                SUM(ISNULL(DiscountValue, 0)) AS OffersTotalDiscount,
                SUM(ISNULL(TotalValue, 0) - ISNULL(DiscountValue, 0)) AS OffersNetTotal
            FROM Acc_Offers
            WHERE AddDate >= DATEADD(DAY, -@Days, CAST(GETDATE() AS DATE))
              AND AddDate < DATEADD(DAY, 1, CAST(GETDATE() AS DATE))
              AND IsDeleted = 0{branch_filter}
            GROUP BY BranchId
        )

        -- التجميع النهائي
        SELECT
            B.BranchId,
            B.NameAr AS BranchName,
            ISNULL(CS.CustomersCount, 0) AS CustomersCount,
            ISNULL(PS.PreviewsCount, 0) AS PreviewsCount,
            ISNULL(PS.PreviewsTotalValue, 0) AS PreviewsTotalValue,
            ISNULL(PS.PreviewsPaidCount, 0) AS PreviewsPaidCount,
            ISNULL(PS.PreviewsFreeCount, 0) AS PreviewsFreeCount,
            ISNULL(MS.MeetingsCount, 0) AS MeetingsCount,
            ISNULL(DS.DesignsCount, 0) AS DesignsCount,
            ISNULL(CTS.ContractsCount, 0) AS ContractsCount,
            ISNULL(CTS.ContractsTotalValue, 0) AS ContractsTotalValue,
            ISNULL(CTS.ContractsTotalDiscount, 0) AS ContractsTotalDiscount,
            ISNULL(CTS.ContractsNetTotal, 0) AS ContractsNetTotal,
            ISNULL(OS.OffersCount, 0) AS OffersCount,
            ISNULL(OS.OffersTotalValue, 0) AS OffersTotalValue,
            ISNULL(OS.OffersTotalDiscount, 0) AS OffersTotalDiscount,
            ISNULL(OS.OffersNetTotal, 0) AS OffersNetTotal

        FROM Sys_Branches B
        LEFT JOIN CustomersSummary CS ON B.BranchId = CS.BranchId
        LEFT JOIN PreviewsSummary PS ON B.BranchId = PS.BranchId
        LEFT JOIN MeetingsSummary MS ON B.BranchId = MS.BranchId
        LEFT JOIN DesignsSummary DS ON B.BranchId = DS.BranchId
        LEFT JOIN ContractsSummary CTS ON B.BranchId = CTS.BranchId
        LEFT JOIN OffersSummary OS ON B.BranchId = OS.BranchId

        WHERE B.IsDeleted = 0{branch_filter.replace('BranchId', 'B.BranchId') if branch_filter else ''}

        ORDER BY B.NameAr
        """

        cursor.execute(query, params)
        results = cursor.fetchall()
        conn.close()

        return results

    except Exception as e:
        print(f"❌ خطأ في الحصول على التقرير الإحصائي: {str(e)}")
        return None

def format_statistics_report(data, days, report_name=None, branch_ids=None):
    """تنسيق التقرير الإحصائي للإرسال"""
    if not data:
        return "❌ لا توجد بيانات للعرض"

    # حساب الإجماليات
    total_customers = sum(row[2] for row in data)
    total_previews = sum(row[3] for row in data)
    total_previews_value = sum(row[4] for row in data)
    total_meetings = sum(row[7] for row in data)
    total_designs = sum(row[8] for row in data)
    total_contracts = sum(row[9] for row in data)
    total_contracts_value = sum(row[10] for row in data)
    total_offers = sum(row[13] for row in data)
    total_offers_value = sum(row[14] for row in data)

    # عنوان التقرير
    title = report_name or f"تقرير إحصائي شامل - آخر {days} أيام"

    # معلومات الفروع المحددة
    branch_info = ""
    if branch_ids:
        if isinstance(branch_ids, str):
            branch_list = [x.strip() for x in branch_ids.split(',') if x.strip()]
            branch_info = f"\n🏢 الفروع المحددة: {', '.join(branch_list)}"
        elif isinstance(branch_ids, list):
            branch_info = f"\n🏢 الفروع المحددة: {', '.join(map(str, branch_ids))}"
    else:
        branch_info = "\n🏢 جميع الفروع"

    # تنسيق التقرير
    report = f"""📊 **{title}**
📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}{branch_info}

═══════════════════════════════════

📈 **الإجماليات العامة:**
👥 العملاء الجدد: {total_customers:,}
👁️ المعاينات: {total_previews:,} (قيمة: {total_previews_value:,.0f} جنيه)
🤝 الاجتماعات: {total_meetings:,}
🎨 التصميمات: {total_designs:,}
📋 العقود: {total_contracts:,} (قيمة: {total_contracts_value:,.0f} جنيه)
💼 العروض: {total_offers:,} (قيمة: {total_offers_value:,.0f} جنيه)

═══════════════════════════════════

🏢 **تفاصيل الفروع:**

"""

    for row in data:
        branch_name = row[1]
        customers = row[2]
        previews = row[3]
        previews_value = row[4]
        previews_paid = row[5]
        previews_free = row[6]
        meetings = row[7]
        designs = row[8]
        contracts = row[9]
        contracts_value = row[10]
        contracts_discount = row[11]
        contracts_net = row[12]
        offers = row[13]
        offers_value = row[14]
        offers_discount = row[15]
        offers_net = row[16]

        # تخطي الفروع التي لا تحتوي على أي بيانات
        if (customers + previews + meetings + designs + contracts + offers) == 0:
            continue

        report += f"""🏪 **{branch_name}**
├─ 👥 عملاء جدد: {customers:,}
├─ 👁 معاينات: {previews:,} (مدفوعة: {previews_paid}, مجانية: {previews_free})
│   💰 قيمة المعاينات: {previews_value:,.0f} جنيه
├─ 🤝 اجتماعات: {meetings:,}
├─ 🎨 تصميمات: {designs:,}
├─ 📋 عقود: {contracts:,}
│   💰 اجمالى العقود: {contracts_value:,.0f} جنيه
│   🏷 اجمالي خصم العقود: {contracts_discount:,.0f} جنيه
│   💵 صافي العقود: {contracts_net:,.0f} جنيه
└─ 💼 عدد طلب التسعير: {offers:,}
    💰 اجمالي طلب التسعير: {offers_value:,.0f} جنيه
    🏷 اجمالي خصومات طلب التسعير: {offers_discount:,.0f} جنيه
    💵 صافي طلبات التسعير: {offers_net:,.0f} جنيه

"""

    report += f"""═══════════════════════════════════
🤖 **Terra Bot Enhanced** - {datetime.now().strftime('%H:%M')}"""

    return report

def format_statistics_table(data, days, report_name=None, branch_ids=None):
    """تنسيق التقرير الإحصائي في شكل جدول"""
    if not data:
        return "❌ لا توجد بيانات للعرض"

    # عنوان التقرير
    title = report_name or f"تقرير إحصائي شامل - آخر {days} أيام"

    # معلومات الفروع المحددة
    branch_info = ""
    if branch_ids:
        if isinstance(branch_ids, str):
            branch_list = [x.strip() for x in branch_ids.split(',') if x.strip()]
            branch_info = f"\n🏢 الفروع المحددة: {', '.join(branch_list)}"
        elif isinstance(branch_ids, list):
            branch_info = f"\n🏢 الفروع المحددة: {', '.join(map(str, branch_ids))}"
    else:
        branch_info = "\n🏢 جميع الفروع"

    # بناء الجدول
    table = f"""📊 **{title}**
📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}{branch_info}

```
اسم الفرع | عملاء جدد | معاينات | قيمة المعاينات | معاينات مدفوعة | معاينات مجانية | اجتماعات | تصميمات | عقود | اجمالى العقود | اجمالي خصم العقود | صافي العقود | عدد طلب التسعير | اجمالي طلب التسعير | اجمالي خصومات طلب التسعير | صافي طلبات التسعير
"""

    # إضافة البيانات
    for row in data:
        branch_name = row[1] or "فرع غير محدد"
        customers = row[2] or 0
        previews = row[3] or 0
        previews_value = row[4] or 0
        previews_paid = row[5] or 0
        previews_free = row[6] or 0
        meetings = row[7] or 0
        designs = row[8] or 0
        contracts = row[9] or 0
        contracts_value = row[10] or 0
        contracts_discount = row[11] or 0
        contracts_net = row[12] or 0
        offers = row[13] or 0
        offers_value = row[14] or 0
        offers_discount = row[15] or 0
        offers_net = row[16] or 0

        # تخطي الفروع التي لا تحتوي على أي بيانات
        if (customers + previews + meetings + designs + contracts + offers) == 0:
            continue

        table += f"{branch_name} | {customers} | {previews} | {previews_value:,.0f} | {previews_paid} | {previews_free} | {meetings} | {designs} | {contracts} | {contracts_value:,.0f} | {contracts_discount:,.0f} | {contracts_net:,.0f} | {offers} | {offers_value:,.0f} | {offers_discount:,.0f} | {offers_net:,.0f}\n"

    table += "```"

    return table

async def send_statistics_report(days=3, branch_ids=None, report_name=None):
    """إرسال التقرير الإحصائي لجميع المستقبلين"""
    try:
        report_title = report_name or f"التقرير الإحصائي لآخر {days} أيام"
        print(f"📊 بدء إرسال {report_title}...")

        # الحصول على البيانات
        data = get_statistics_report(days, branch_ids)
        if not data:
            print("❌ لا توجد بيانات للتقرير")
            return

        # تنسيق التقرير
        report_text = format_statistics_report(data, days, report_name, branch_ids)

        # الحصول على قائمة المستقبلين من قاعدة البيانات
        recipients = get_active_recipients()
        if not recipients:
            print("❌ لا توجد قائمة مستقبلين للتقرير")
            return

        # إرسال التقرير لكل مستقبل
        sent_count = 0
        for recipient in recipients:
            chat_id = recipient[1]
            try:
                # استخدام Bot مباشرة
                bot_instance = Bot(token=BOT_TOKEN)
                await bot_instance.send_message(
                    chat_id=chat_id,
                    text=report_text,
                    parse_mode='Markdown'
                )
                sent_count += 1
                print(f"✅ تم إرسال التقرير إلى: {chat_id}")

                # تأخير قصير لتجنب flood control
                await asyncio.sleep(1)

            except Exception as e:
                print(f"❌ خطأ في إرسال التقرير إلى {chat_id}: {str(e)}")

        print(f"📊 تم إرسال التقرير الإحصائي إلى {sent_count} مستقبل")

    except Exception as e:
        print(f"❌ خطأ في إرسال التقرير الإحصائي: {str(e)}")

async def scheduled_statistics_report():
    """إرسال التقرير الإحصائي في الأوقات المجدولة حسب قاعدة البيانات"""
    while True:
        try:
            now = datetime.now()

            # الحصول على التقارير المجدولة
            scheduled_reports = get_scheduled_reports()

            for report in scheduled_reports:
                report_id = report[0]
                report_name = report[1]
                days_count = report[2]
                schedule_type = report[3]
                schedule_hour = report[4]
                schedule_minute = report[5]
                schedule_weekday = report[6]
                schedule_day = report[7]
                branch_ids = report[8]
                last_sent_date = report[9]

                should_send = False

                # التحقق من التوقيت حسب نوع الجدولة
                if schedule_type == "daily":
                    # يومي - فحص الساعة والدقيقة
                    if now.hour == schedule_hour and now.minute == schedule_minute:
                        # التحقق من عدم الإرسال اليوم
                        if not last_sent_date or last_sent_date.date() < now.date():
                            should_send = True

                elif schedule_type == "weekly":
                    # أسبوعي - فحص اليوم والساعة والدقيقة
                    if (now.weekday() == schedule_weekday and
                        now.hour == schedule_hour and
                        now.minute == schedule_minute):
                        # التحقق من عدم الإرسال هذا الأسبوع
                        if not last_sent_date or (now - last_sent_date).days >= 7:
                            should_send = True

                elif schedule_type == "monthly":
                    # شهري - فحص اليوم والساعة والدقيقة
                    if (now.day == schedule_day and
                        now.hour == schedule_hour and
                        now.minute == schedule_minute):
                        # التحقق من عدم الإرسال هذا الشهر
                        if not last_sent_date or (now.month != last_sent_date.month or now.year != last_sent_date.year):
                            should_send = True

                # إرسال التقرير إذا حان وقته
                if should_send:
                    print(f"📊 وقت إرسال {report_name}...")
                    await send_statistics_report(days=days_count, branch_ids=branch_ids, report_name=report_name)

                    # تحديث تاريخ آخر إرسال
                    update_report_last_sent(report_id)

                    # انتظار دقيقة لتجنب الإرسال المتكرر
                    await asyncio.sleep(60)

            # فحص كل 30 ثانية
            await asyncio.sleep(30)

        except Exception as e:
            print(f"❌ خطأ في جدولة التقرير الإحصائي: {str(e)}")
            await asyncio.sleep(60)  # انتظار دقيقة عند حدوث خطأ

def send_notification_sync(message):
    """دالة مساعدة لإرسال الإشعارات من الدوال العادية"""
    import threading

    def run_async():
        try:
            asyncio.run(send_notification(message))
        except Exception as e:
            print(f"❌ خطأ في إرسال الإشعار: {str(e)}")
            if logger:
                logger.error(f"خطأ في إرسال الإشعار: {str(e)}")

    # تشغيل الدالة في thread منفصل لتجنب تضارب حلقات الأحداث
    thread = threading.Thread(target=run_async)
    thread.start()
    thread.join()  # انتظار انتهاء الإرسال

async def send_notification(message):
    """إرسال إشعار للمستخدمين المخولين"""
    try:
        if not message:
            warning_msg = "⚠️ محاولة إرسال رسالة فارغة"
            print(warning_msg)
            if logger:
                logger.warning(warning_msg)
            return

        bot = Bot(token=BOT_TOKEN)
        notification_text = f"🔔 **إشعار تلقائي من Terra Bot**\n\n{message}\n\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # الحصول على قائمة المستقبلين النشطين
        active_recipients = get_active_recipients()

        start_msg = f"📤 بدء إرسال إشعار إلى {len(active_recipients)} مستقبل"
        print(start_msg)
        if logger:
            logger.info(start_msg)

        for chat_id in active_recipients:
            try:
                await bot.send_message(
                    chat_id=chat_id,
                    text=notification_text,
                    parse_mode='Markdown'
                )
                success_msg = f"✅ تم إرسال إشعار إلى {chat_id}"
                print(success_msg)
                if logger:
                    logger.info(success_msg)

            except Exception as e:
                error_msg = f"❌ فشل إرسال إشعار إلى {chat_id}: {str(e)}"
                print(error_msg)
                if logger:
                    logger.error(error_msg)

    except Exception as e:
        error_msg = f"❌ خطأ في إرسال الإشعارات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)

def check_new_customers():
    """فحص العملاء الجدد مع التفاصيل الشاملة والتحقق من قاعدة البيانات حسب الفرع"""

    try:
        # الحصول على كل العملاء الجدد بعد آخر معرف
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # الحصول على آخر معرف تم إرساله لهذا الفرع
        def get_last_sent_customer_id(branch_id):
            try:
                notifications_conn = connect_to_notifications_db()
                if not notifications_conn:
                    return 0

                notifications_cursor = notifications_conn.cursor()
                # جلب آخر معرف عميل (CustomerId) من نوع العملاء فقط عالمياً
                notifications_cursor.execute("""
                    SELECT TOP 1 RecordUniqueId FROM Notifications_Log
                    WHERE DataType = 'عميل' AND IsActive = 1 AND RecordUniqueId IS NOT NULL
                    ORDER BY RecordUniqueId DESC
                """)

                result = notifications_cursor.fetchone()
                notifications_conn.close()

                last_id = result[0] if result else 0
                print(f"📊 آخر معرف عميل (CustomerId): {last_id}")
                return last_id
            except Exception as e:
                print(f"❌ خطأ في الحصول على آخر معرف عميل: {str(e)}")
                return 0

        # استعلام للحصول على العملاء الجدد بعد آخر معرف تم إرساله
        query = """
        SELECT
            c.CustomerId, c.CustomerCode, c.NameAr, c.NameEn, c.MainPhoneNo,
            c.SubMainPhoneNo, c.Email, c.Address, c.NationalId, c.Notes,
            c.AddDate, c.UpdateDate, c.BranchId,
            ISNULL(branch.NameAr, 'فرع غير محدد') AS BranchName,
            ISNULL(city.NameAr, 'غير محدد') AS CityName,
            ISNULL(pay.NameAr, 'غير محدد') AS PayTypeName,
            ISNULL(social.NameAr, 'غير محدد') AS SocialMediaName,
            ISNULL(addUser.UserName, 'غير محدد') AS AddedByUser,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            ISNULL(updateUser.UserName, '') AS UpdatedByUser,
            ISNULL(updateUser.FullName, '') AS UpdatedByFullName
        FROM Acc_Customers c
        LEFT JOIN Sys_Branches branch ON c.BranchId = branch.BranchId
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Acc_PayType pay ON c.PayTypeId = pay.PayTypeId
        LEFT JOIN Sys_SocialMedia social ON c.SocialMediaId = social.SocialMediaId
        LEFT JOIN Sys_Users addUser ON c.AddUser = addUser.UserId
        LEFT JOIN Sys_Users updateUser ON c.UpdateUser = updateUser.UserId
        WHERE c.IsDeleted = 0 AND c.BranchId = ? AND c.CustomerId > ?
        ORDER BY c.CustomerId ASC
        """

        # فحص كل فرع منفصل
        notifications_sent = 0
        # الحصول على جميع الفروع من قاعدة البيانات تلقائياً
        cursor.execute("SELECT DISTINCT BranchId FROM Acc_Customers WHERE IsDeleted = 0 AND BranchId IS NOT NULL ORDER BY BranchId")
        branches = [row[0] for row in cursor.fetchall()]
        print(f"🏢 فحص العملاء في الفروع: {branches}")

        for branch_id in branches:
            # الحصول على آخر معرف تم إرساله لهذا الفرع
            last_sent_id = get_last_sent_customer_id(branch_id)
            print(f"📊 آخر معرف عميل تم إرساله للفرع {branch_id}: {last_sent_id}")

            # تنفيذ الاستعلام للحصول على العملاء الجدد بعد آخر معرف
            cursor.execute(query, (branch_id, last_sent_id))
            new_customers = cursor.fetchall()

            # معالجة كل عميل جديد في هذا الفرع
            for customer in new_customers:

                # تحديد أيقونة الفرع
                branch_icon = "🏢" if customer[12] == 2 else "🏬" if customer[12] == 3 else "🏪"

                message = f"""👤 **عميل جديد تم إضافته**

{branch_icon} **الفرع:** {customer[13]}

🔢 **الكود:** {customer[1]}
👤 **الاسم العربي:** {customer[2] or 'غير محدد'}
🔤 **الاسم الإنجليزي:** {customer[3] or 'غير محدد'}
📱 **الهاتف الأساسي:** {customer[4] or 'غير محدد'}
📞 **الهاتف الفرعي:** {customer[5] or 'غير محدد'}
📧 **الإيميل:** {customer[6] or 'غير محدد'}
🏠 **العنوان:** {customer[7] or 'غير محدد'}
🆔 **الرقم القومي:** {customer[8] or 'غير محدد'}
📝 **ملاحظات:** {customer[9] or 'لا توجد'}

🌍 **المدينة/المنطقة:** {customer[14]}
💳 **طريقة الدفع:** {customer[15]}
📱 **وسيلة التواصل:** {customer[16]}

👨‍💼 **تم الإنشاء بواسطة:** {customer[18]}
📅 **تاريخ الإضافة:** {customer[10].strftime('%Y-%m-%d %H:%M') if customer[10] else 'غير محدد'}"""

                if customer[11]:  # إذا كان هناك تاريخ تحديث
                    message += f"\n🔄 **آخر تحديث:** {customer[11].strftime('%Y-%m-%d %H:%M')}"
                    if customer[19]:  # إذا كان هناك من حدث
                        message += f"\n👨‍🔧 **تم التحديث بواسطة:** {customer[20]}"

                # حفظ في قاعدة البيانات
                save_notification_to_db(
                    'عميل',
                    customer[1],  # CustomerCode
                    customer[2] or 'غير محدد',  # NameAr
                    customer[12],  # BranchId
                    customer[13],  # BranchName
                    customer[0],   # CustomerId (المعرف الفريد)
                    customer[10],  # AddDate
                    customer[18]   # AddedByFullName
                )

                # إرسال الإشعار
                send_notification_sync(message)
                notifications_sent += 1
                print(f"✅ تم إرسال إشعار للعميل {customer[1]} (ID: {customer[0]})")

        conn.close()

        if notifications_sent > 0:
            print(f"📊 تم إرسال {notifications_sent} إشعار عميل جديد")
            return None  # لا نرسل رسالة إضافية لأن كل عميل أرسل إشعار منفصل

        return None

    except Exception as e:
        print(f"❌ خطأ في فحص العملاء الجدد: {str(e)}")
        return None

def check_new_previews():
    """فحص المعاينات الجديدة مع التفاصيل الشاملة والتحقق من قاعدة البيانات حسب الفرع"""

    try:
        # الحصول على كل المعاينات الجديدة بعد آخر معرف لكل فرع
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # الحصول على آخر معرف تم إرساله لهذا الفرع
        def get_last_sent_preview_id(branch_id):
            try:
                notifications_conn = connect_to_notifications_db()
                if not notifications_conn:
                    return 0

                notifications_cursor = notifications_conn.cursor()

                # جلب آخر معرف معاينة (PreviewId) من نوع المعاينات فقط عالمياً
                notifications_cursor.execute("""
                    SELECT TOP 1 RecordUniqueId FROM Notifications_Log
                    WHERE DataType = 'معاينة' AND IsActive = 1 AND RecordUniqueId IS NOT NULL
                    ORDER BY RecordUniqueId DESC
                """)

                result = notifications_cursor.fetchone()
                notifications_conn.close()

                last_id = result[0] if result else 0
                print(f"📊 آخر معرف معاينة (PreviewId): {last_id}")
                return last_id
            except Exception as e:
                print(f"❌ خطأ في الحصول على آخر معرف معاينة: {str(e)}")
                return 0

        # استعلام للحصول على المعاينات الجديدة بعد آخر معرف تم إرساله
        query = """
        SELECT
            p.PreviewId, p.PreviewCode, p.Date AS PreviewDate, p.Notes, p.BranchId,
            ISNULL(branch.NameAr, 'فرع غير محدد') AS BranchName,
            c.CustomerCode, c.NameAr AS CustomerName, c.MainPhoneNo, c.Address,
            ISNULL(city.NameAr, 'غير محدد') AS CityName,
            ISNULL(addUser.UserName, 'غير محدد') AS AddedByUser,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            p.AddDate
        FROM Sys_Previews p
        INNER JOIN Acc_Customers c ON p.CustomerId = c.CustomerId
        LEFT JOIN Sys_Branches branch ON p.BranchId = branch.BranchId
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON p.AddUser = addUser.UserId
        WHERE p.IsDeleted = 0 AND c.IsDeleted = 0 AND c.BranchId = ? AND p.PreviewId > ?
        ORDER BY p.PreviewId ASC
        """

        # فحص كل فرع منفصل
        notifications_sent = 0
        # الحصول على جميع الفروع من قاعدة البيانات تلقائياً
        cursor.execute("SELECT DISTINCT BranchId FROM Acc_Customers WHERE IsDeleted = 0 AND BranchId IS NOT NULL ORDER BY BranchId")
        branches = [row[0] for row in cursor.fetchall()]
        print(f"🏢 فحص المعاينات في الفروع: {branches}")

        for branch_id in branches:
            # الحصول على آخر معرف تم إرساله لهذا الفرع
            last_sent_id = get_last_sent_preview_id(branch_id)
            print(f"📊 آخر معرف معاينة تم إرساله للفرع {branch_id}: {last_sent_id}")

            # تنفيذ الاستعلام للحصول على المعاينات الجديدة بعد آخر معرف
            cursor.execute(query, (branch_id, last_sent_id))
            new_previews = cursor.fetchall()

            # معالجة كل معاينة جديدة في هذا الفرع
            for preview in new_previews:

                # تحديد أيقونة الفرع
                branch_icon = "🏢" if preview[4] == 2 else "🏬" if preview[4] == 3 else "🏪"

                message = f"""👁️ **معاينة جديدة تم إضافتها**

{branch_icon} **الفرع:** {preview[5]}

🔢 **كود المعاينة:** {preview[1]}
📅 **تاريخ المعاينة:** {preview[2].strftime('%Y-%m-%d %H:%M') if preview[2] else 'غير محدد'}
📝 **ملاحظات المعاينة:** {preview[3] or 'لا توجد'}

👤 **بيانات العميل:**
🔢 **كود العميل:** {preview[6]}
👤 **اسم العميل:** {preview[7] or 'غير محدد'}
📱 **هاتف العميل:** {preview[8] or 'غير محدد'}
🏠 **عنوان العميل:** {preview[9] or 'غير محدد'}
🌍 **المدينة/المنطقة:** {preview[10] or 'غير محدد'}

👨‍💼 **تم الإنشاء بواسطة:** {preview[12] or 'غير محدد'}
📅 **تاريخ الإضافة:** {preview[13].strftime('%Y-%m-%d %H:%M') if preview[13] else 'غير محدد'}"""

                # حفظ في قاعدة البيانات
                save_notification_to_db(
                    'معاينة',
                    preview[1],  # PreviewCode
                    f"معاينة للعميل {preview[7] or 'غير محدد'}",
                    preview[4],  # BranchId
                    preview[5],  # BranchName
                    preview[0],  # PreviewId (المعرف الفريد)
                    preview[13], # AddDate
                    preview[12] or 'غير محدد'  # AddedByFullName
                )

                # إرسال الإشعار
                send_notification_sync(message)
                notifications_sent += 1
                print(f"✅ تم إرسال إشعار للمعاينة {preview[1]} (ID: {preview[0]})")

        conn.close()

        if notifications_sent > 0:
            print(f"📊 تم إرسال {notifications_sent} إشعار معاينة جديدة")
            return None  # لا نرسل رسالة إضافية لأن كل معاينة أرسلت إشعار منفصل

        return None

    except Exception as e:
        print(f"❌ خطأ في فحص المعاينات الجديدة: {str(e)}")
        return None

def check_new_meetings():
    """فحص الاجتماعات الجديدة مع التفاصيل الشاملة والتحقق من قاعدة البيانات حسب الفرع"""

    try:
        # الحصول على كل الاجتماعات الجديدة بعد آخر معرف لكل فرع
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # الحصول على آخر معرف تم إرساله لهذا الفرع
        def get_last_sent_meeting_id(branch_id):
            try:
                notifications_conn = connect_to_notifications_db()
                if not notifications_conn:
                    return 0

                notifications_cursor = notifications_conn.cursor()
                # جلب آخر معرف اجتماع (MeetingId) من نوع الاجتماعات فقط عالمياً
                notifications_cursor.execute("""
                    SELECT TOP 1 RecordUniqueId FROM Notifications_Log
                    WHERE DataType = 'اجتماع' AND IsActive = 1 AND RecordUniqueId IS NOT NULL
                    ORDER BY RecordUniqueId DESC
                """)

                result = notifications_cursor.fetchone()
                notifications_conn.close()

                last_id = result[0] if result else 0
                print(f"📊 آخر معرف اجتماع (MeetingId): {last_id}")
                return last_id
            except Exception as e:
                print(f"❌ خطأ في الحصول على آخر معرف اجتماع: {str(e)}")
                return 0

        # استعلام للحصول على الاجتماعات الجديدة بعد آخر معرف تم إرساله
        query = """
        SELECT
            m.MeetingId, m.MeetingCode, m.Date AS MeetingDate, m.Notes, m.BranchId,
            ISNULL(branch.NameAr, 'فرع غير محدد') AS BranchName,
            c.CustomerCode, c.NameAr AS CustomerName, c.MainPhoneNo, c.Address,
            ISNULL(city.NameAr, 'غير محدد') AS CityName,
            ISNULL(addUser.UserName, 'غير محدد') AS AddedByUser,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            m.AddDate
        FROM Sys_Meetings m
        INNER JOIN Acc_Customers c ON m.CustomerId = c.CustomerId
        LEFT JOIN Sys_Branches branch ON m.BranchId = branch.BranchId
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON m.AddUser = addUser.UserId
        WHERE m.IsDeleted = 0 AND c.IsDeleted = 0 AND c.BranchId = ? AND m.MeetingId > ?
        ORDER BY m.MeetingId ASC
        """

        # فحص كل فرع منفصل
        notifications_sent = 0
        # الحصول على جميع الفروع من قاعدة البيانات تلقائياً
        cursor.execute("SELECT DISTINCT BranchId FROM Acc_Customers WHERE IsDeleted = 0 AND BranchId IS NOT NULL ORDER BY BranchId")
        branches = [row[0] for row in cursor.fetchall()]
        print(f"🏢 فحص الاجتماعات في الفروع: {branches}")

        for branch_id in branches:
            # الحصول على آخر معرف تم إرساله لهذا الفرع
            last_sent_id = get_last_sent_meeting_id(branch_id)
            print(f"📊 آخر معرف اجتماع تم إرساله للفرع {branch_id}: {last_sent_id}")

            # تنفيذ الاستعلام للحصول على الاجتماعات الجديدة بعد آخر معرف
            cursor.execute(query, (branch_id, last_sent_id))
            new_meetings = cursor.fetchall()

            # معالجة كل اجتماع جديد في هذا الفرع
            for meeting in new_meetings:

                # تحديد أيقونة الفرع
                branch_icon = "🏢" if meeting[4] == 2 else "🏬" if meeting[4] == 3 else "🏪"

                message = f"""🤝 **اجتماع جديد تم إضافته**

{branch_icon} **الفرع:** {meeting[5]}

🔢 **كود الاجتماع:** {meeting[1]}
📅 **تاريخ الاجتماع:** {meeting[2].strftime('%Y-%m-%d %H:%M') if meeting[2] else 'غير محدد'}
📝 **ملاحظات الاجتماع:** {meeting[3] or 'لا توجد'}

👤 **بيانات العميل:**
🔢 **كود العميل:** {meeting[6]}
👤 **اسم العميل:** {meeting[7] or 'غير محدد'}
📱 **هاتف العميل:** {meeting[8] or 'غير محدد'}
🏠 **عنوان العميل:** {meeting[9] or 'غير محدد'}
🌍 **المدينة/المنطقة:** {meeting[10] or 'غير محدد'}

👨‍💼 **تم الإنشاء بواسطة:** {meeting[12] or 'غير محدد'}
📅 **تاريخ الإضافة:** {meeting[13].strftime('%Y-%m-%d %H:%M') if meeting[13] else 'غير محدد'}"""

                # حفظ في قاعدة البيانات
                save_notification_to_db(
                    'اجتماع',
                    meeting[1],  # MeetingCode
                    f"اجتماع مع العميل {meeting[7] or 'غير محدد'}",
                    meeting[4],  # BranchId
                    meeting[5],  # BranchName
                    meeting[0],  # MeetingId (المعرف الفريد)
                    meeting[13], # AddDate
                    meeting[12] or 'غير محدد'  # AddedByFullName
                )

                # إرسال الإشعار
                send_notification_sync(message)
                notifications_sent += 1
                print(f"✅ تم إرسال إشعار للاجتماع {meeting[1]} (ID: {meeting[0]})")

        conn.close()

        if notifications_sent > 0:
            print(f"📊 تم إرسال {notifications_sent} إشعار اجتماع جديد")
            return None  # لا نرسل رسالة إضافية لأن كل اجتماع أرسل إشعار منفصل

        return None

    except Exception as e:
        error_msg = f"❌ خطأ في فحص الاجتماعات الجديدة: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

def check_new_designs():
    """فحص التصميمات الجديدة مع التفاصيل الشاملة والتحقق من قاعدة البيانات حسب الفرع"""

    try:
        # الحصول على كل التصميمات الجديدة بعد آخر معرف لكل فرع
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # الحصول على آخر معرف تم إرساله لهذا الفرع
        def get_last_sent_design_id(branch_id):
            try:
                notifications_conn = connect_to_notifications_db()
                if not notifications_conn:
                    return 0

                notifications_cursor = notifications_conn.cursor()
                # جلب آخر معرف تصميم (DesignId) من نوع التصميمات فقط عالمياً
                notifications_cursor.execute("""
                    SELECT TOP 1 RecordUniqueId FROM Notifications_Log
                    WHERE DataType = 'تصميم' AND IsActive = 1 AND RecordUniqueId IS NOT NULL
                    ORDER BY RecordUniqueId DESC
                """)

                result = notifications_cursor.fetchone()
                notifications_conn.close()

                last_id = result[0] if result else 0
                print(f"📊 آخر معرف تصميم (DesignId): {last_id}")
                return last_id
            except Exception as e:
                print(f"❌ خطأ في الحصول على آخر معرف تصميم: {str(e)}")
                return 0

        # استعلام للحصول على التصميمات الجديدة بعد آخر معرف تم إرساله
        query = """
        SELECT
            d.DesignId, d.DesignCode, d.Date AS DesignDate, d.Notes, d.BranchId,
            ISNULL(branch.NameAr, 'فرع غير محدد') AS BranchName,
            c.CustomerCode, c.NameAr AS CustomerName, c.MainPhoneNo, c.Address,
            ISNULL(city.NameAr, 'غير محدد') AS CityName,
            ISNULL(addUser.UserName, 'غير محدد') AS AddedByUser,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            d.AddDate
        FROM Sys_Designs d
        INNER JOIN Acc_Customers c ON d.CustomerId = c.CustomerId
        LEFT JOIN Sys_Branches branch ON d.BranchId = branch.BranchId
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON d.AddUser = addUser.UserId
        WHERE d.IsDeleted = 0 AND c.IsDeleted = 0 AND c.BranchId = ? AND d.DesignId > ?
        ORDER BY d.DesignId ASC
        """

        # فحص كل فرع منفصل
        notifications_sent = 0
        # الحصول على جميع الفروع من قاعدة البيانات تلقائياً
        cursor.execute("SELECT DISTINCT BranchId FROM Acc_Customers WHERE IsDeleted = 0 AND BranchId IS NOT NULL ORDER BY BranchId")
        branches = [row[0] for row in cursor.fetchall()]
        print(f"🏢 فحص التصميمات في الفروع: {branches}")

        for branch_id in branches:
            # الحصول على آخر معرف تم إرساله لهذا الفرع
            last_sent_id = get_last_sent_design_id(branch_id)
            print(f"📊 آخر معرف تصميم تم إرساله للفرع {branch_id}: {last_sent_id}")

            # تنفيذ الاستعلام للحصول على التصميمات الجديدة بعد آخر معرف
            cursor.execute(query, (branch_id, last_sent_id))
            new_designs = cursor.fetchall()

            # معالجة كل تصميم جديد في هذا الفرع
            for design in new_designs:

                # تحديد أيقونة الفرع
                branch_icon = "🏢" if design[4] == 2 else "🏬" if design[4] == 3 else "🏪"

                message = f"""🎨 **تصميم جديد تم إضافته**

{branch_icon} **الفرع:** {design[5]}

🔢 **كود التصميم:** {design[1]}
📅 **تاريخ التصميم:** {design[2].strftime('%Y-%m-%d %H:%M') if design[2] else 'غير محدد'}
📝 **ملاحظات التصميم:** {design[3] or 'لا توجد'}

👤 **بيانات العميل:**
🔢 **كود العميل:** {design[6]}
👤 **اسم العميل:** {design[7] or 'غير محدد'}
📱 **هاتف العميل:** {design[8] or 'غير محدد'}
🏠 **عنوان العميل:** {design[9] or 'غير محدد'}
🌍 **المدينة/المنطقة:** {design[10] or 'غير محدد'}

👨‍💼 **تم الإنشاء بواسطة:** {design[12] or 'غير محدد'}
📅 **تاريخ الإضافة:** {design[13].strftime('%Y-%m-%d %H:%M') if design[13] else 'غير محدد'}"""

                # حفظ في قاعدة البيانات
                save_notification_to_db(
                    'تصميم',
                    design[1],  # DesignCode
                    f"تصميم للعميل {design[7] or 'غير محدد'}",
                    design[4],  # BranchId
                    design[5],  # BranchName
                    design[0],  # DesignId (المعرف الفريد)
                    design[13], # AddDate
                    design[12] or 'غير محدد'  # AddedByFullName
                )

                # إرسال الإشعار
                send_notification_sync(message)
                notifications_sent += 1
                print(f"✅ تم إرسال إشعار للتصميم {design[1]} (ID: {design[0]})")

        conn.close()

        if notifications_sent > 0:
            print(f"📊 تم إرسال {notifications_sent} إشعار تصميم جديد")
            return None  # لا نرسل رسالة إضافية لأن كل تصميم أرسل إشعار منفصل

        return None

    except Exception as e:
        error_msg = f"❌ خطأ في فحص التصميمات الجديدة: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

def check_new_contracts():
    """فحص العقود الجديدة مع التفاصيل الشاملة والتحقق من قاعدة البيانات حسب الفرع"""

    try:
        # الحصول على كل العقود الجديدة بعد آخر معرف لكل فرع
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # الحصول على آخر معرف تم إرساله لهذا الفرع
        def get_last_sent_contract_id(branch_id):
            try:
                notifications_conn = connect_to_notifications_db()
                if not notifications_conn:
                    return 0

                notifications_cursor = notifications_conn.cursor()
                # جلب آخر معرف عقد (ContractId) من نوع العقود فقط عالمياً
                notifications_cursor.execute("""
                    SELECT TOP 1 RecordUniqueId FROM Notifications_Log
                    WHERE DataType = 'عقد' AND IsActive = 1 AND RecordUniqueId IS NOT NULL
                    ORDER BY RecordUniqueId DESC
                """)

                result = notifications_cursor.fetchone()
                notifications_conn.close()

                last_id = result[0] if result else 0
                print(f"📊 آخر معرف عقد (ContractId): {last_id}")
                return last_id
            except Exception as e:
                print(f"❌ خطأ في الحصول على آخر معرف عقد: {str(e)}")
                return 0

        # استعلام للحصول على العقود الجديدة بعد آخر معرف تم إرساله
        query = """
        SELECT
            ct.ContractId, ct.ContractCode, ct.Date AS ContractDate, ct.Notes, ct.BranchId,
            ISNULL(branch.NameAr, 'فرع غير محدد') AS BranchName,
            c.CustomerCode, c.NameAr AS CustomerName, c.MainPhoneNo, c.Address,
            ISNULL(city.NameAr, 'غير محدد') AS CityName,
            ISNULL(addUser.UserName, 'غير محدد') AS AddedByUser,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            ct.AddDate
        FROM Acc_Contracts ct
        INNER JOIN Acc_Customers c ON ct.CustomerId = c.CustomerId
        LEFT JOIN Sys_Branches branch ON ct.BranchId = branch.BranchId
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON ct.AddUser = addUser.UserId
        WHERE ct.IsDeleted = 0 AND c.IsDeleted = 0 AND c.BranchId = ? AND ct.ContractId > ?
        ORDER BY ct.ContractId ASC
        """

        # فحص كل فرع منفصل
        notifications_sent = 0
        # الحصول على جميع الفروع من قاعدة البيانات تلقائياً
        cursor.execute("SELECT DISTINCT BranchId FROM Acc_Customers WHERE IsDeleted = 0 AND BranchId IS NOT NULL ORDER BY BranchId")
        branches = [row[0] for row in cursor.fetchall()]
        print(f"🏢 فحص العقود في الفروع: {branches}")

        for branch_id in branches:
            # الحصول على آخر معرف تم إرساله لهذا الفرع
            last_sent_id = get_last_sent_contract_id(branch_id)
            print(f"📊 آخر معرف عقد تم إرساله للفرع {branch_id}: {last_sent_id}")

            # تنفيذ الاستعلام للحصول على العقود الجديدة بعد آخر معرف
            cursor.execute(query, (branch_id, last_sent_id))
            new_contracts = cursor.fetchall()

            # معالجة كل عقد جديد في هذا الفرع
            for contract in new_contracts:

                # تحديد أيقونة الفرع
                branch_icon = "🏢" if contract[4] == 2 else "🏬" if contract[4] == 3 else "🏪"

                message = f"""📄 **عقد جديد تم إضافته**

{branch_icon} **الفرع:** {contract[5]}

🔢 **كود العقد:** {contract[1]}
📅 **تاريخ العقد:** {contract[2].strftime('%Y-%m-%d %H:%M') if contract[2] else 'غير محدد'}
📝 **ملاحظات العقد:** {contract[3] or 'لا توجد'}

👤 **بيانات العميل:**
🔢 **كود العميل:** {contract[6]}
👤 **اسم العميل:** {contract[7] or 'غير محدد'}
📱 **هاتف العميل:** {contract[8] or 'غير محدد'}
🏠 **عنوان العميل:** {contract[9] or 'غير محدد'}
🌍 **المدينة/المنطقة:** {contract[10] or 'غير محدد'}

👨‍💼 **تم الإنشاء بواسطة:** {contract[12] or 'غير محدد'}
📅 **تاريخ الإضافة:** {contract[13].strftime('%Y-%m-%d %H:%M') if contract[13] else 'غير محدد'}"""

                # حفظ في قاعدة البيانات
                save_notification_to_db(
                    'عقد',
                    contract[1],  # ContractCode
                    f"عقد للعميل {contract[7] or 'غير محدد'}",
                    contract[4],  # BranchId
                    contract[5],  # BranchName
                    contract[0],  # ContractId (المعرف الفريد)
                    contract[13], # AddDate
                    contract[12] or 'غير محدد'  # AddedByFullName
                )

                # إرسال الإشعار
                send_notification_sync(message)
                notifications_sent += 1
                print(f"✅ تم إرسال إشعار للعقد {contract[1]} (ID: {contract[0]})")

        conn.close()

        if notifications_sent > 0:
            print(f"📊 تم إرسال {notifications_sent} إشعار عقد جديد")
            return None  # لا نرسل رسالة إضافية لأن كل عقد أرسل إشعار منفصل

        return None

    except Exception as e:
        error_msg = f"❌ خطأ في فحص العقود الجديدة: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

def initialize_last_ids():
    """تهيئة آخر المعرفات من قاعدة بيانات الإشعارات وقاعدة البيانات الأصلية"""
    global last_customer_ids, last_preview_ids, last_meeting_ids, last_design_ids, last_contract_ids

    try:
        print("🔄 تهيئة نظام قاعدة البيانات والمعرفات...")

        # إنشاء جداول قاعدة بيانات الإشعارات إذا لم تكن موجودة
        init_notifications_db()

        # تحميل المعرفات من قاعدة بيانات الإشعارات أولاً
        load_last_ids_from_db()

        print(f"✅ تم تهيئة المعرفات النهائية حسب الفرع:")
        print(f"   🏢 فرع مدينة نصر (2):")
        print(f"      عميل: {last_customer_ids[2]}")
        print(f"      معاينة: {last_preview_ids[2]}")
        print(f"      اجتماع: {last_meeting_ids[2]}")
        print(f"      تصميم: {last_design_ids[2]}")
        print(f"      عقد: {last_contract_ids[2]}")
        print(f"   🏬 فرع التجمع (3):")
        print(f"      عميل: {last_customer_ids[3]}")
        print(f"      معاينة: {last_preview_ids[3]}")
        print(f"      اجتماع: {last_meeting_ids[3]}")
        print(f"      تصميم: {last_design_ids[3]}")
        print(f"      عقد: {last_contract_ids[3]}")

    except Exception as e:
        error_msg = f"❌ خطأ في تهيئة المعرفات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)

async def monitor_all_data():
    """مراقبة جميع البيانات مع التفاصيل الشاملة"""
    start_msg = "🔍 بدء مراقبة شاملة لجميع البيانات..."
    print(start_msg)
    print("📊 سيتم عرض تفاصيل كاملة مع الربط بجميع الجداول")
    print("⚠️ قراءة فقط - لا يتم كتابة أي بيانات")

    if logger:
        logger.info(start_msg)
        logger.info("📊 سيتم عرض تفاصيل كاملة مع الربط بجميع الجداول")

    # تهيئة المعرفات
    initialize_last_ids()

    cycle = 0

    while True:
        try:
            cycle += 1
            current_time = datetime.now().strftime('%H:%M:%S')
            cycle_msg = f"🔄 دورة مراقبة شاملة #{cycle} - {current_time}"
            print(f"\n{cycle_msg}")

            if logger and cycle % 10 == 1:  # كل 10 دورات
                logger.info(cycle_msg)

            # فحص العملاء الجدد
            new_customer = check_new_customers()
            if new_customer:
                discovery_msg = "🔔 تم اكتشاف عميل جديد!"
                print(discovery_msg)
                if logger:
                    logger.info(discovery_msg)
                await send_notification(new_customer)

            # فحص المعاينات الجديدة
            new_preview = check_new_previews()
            if new_preview:
                discovery_msg = "🔔 تم اكتشاف معاينة جديدة!"
                print(discovery_msg)
                if logger:
                    logger.info(discovery_msg)
                await send_notification(new_preview)

            # فحص الاجتماعات الجديدة
            new_meeting = check_new_meetings()
            if new_meeting:
                discovery_msg = "🔔 تم اكتشاف اجتماع جديد!"
                print(discovery_msg)
                if logger:
                    logger.info(discovery_msg)
                await send_notification(new_meeting)

            # فحص التصميمات الجديدة
            new_design = check_new_designs()
            if new_design:
                discovery_msg = "🔔 تم اكتشاف تصميم جديد!"
                print(discovery_msg)
                if logger:
                    logger.info(discovery_msg)
                await send_notification(new_design)

            # فحص العقود الجديدة
            new_contract = check_new_contracts()
            if new_contract:
                discovery_msg = "🔔 تم اكتشاف عقد جديد!"
                print(discovery_msg)
                if logger:
                    logger.info(discovery_msg)
                await send_notification(new_contract)

            if not any([new_customer, new_preview, new_meeting, new_design, new_contract]):
                no_data_msg = "📊 لا توجد بيانات جديدة"
                print(no_data_msg)
                if logger and cycle % 20 == 0:  # كل 20 دورة
                    logger.debug(no_data_msg)

            # انتظار 30 ثانية
            await asyncio.sleep(30)

        except KeyboardInterrupt:
            stop_msg = "\n⏹️ تم إيقاف المراقبة"
            print(stop_msg)
            if logger:
                logger.info(stop_msg)
            break
        except Exception as e:
            error_msg = f"❌ خطأ في المراقبة: {str(e)}"
            print(error_msg)
            if logger:
                logger.error(error_msg)
            await asyncio.sleep(60)

# دوال البوت
async def start(update, context):
    """دالة البداية"""
    if not update.effective_user or not update.message:
        return

    user_id = update.effective_user.id
    user_name = update.effective_user.first_name or "مستخدم"

    welcome_text = f"""🌟 **مرحباً بك في Terra Bot المحسن** 🌟

👋 أهلاً {user_name}!

🔔 **نظام الإشعارات التلقائية نشط**
📊 **مراقبة قاعدة البيانات مستمرة**

💡 **الأوامر المتاحة:**
/start - العودة للقائمة الرئيسية
/test - اختبار قاعدة البيانات
/id - عرض معرف المستخدم/الجروب
/e - عرض التقرير الإحصائي الشامل
/table - عرض التقرير في شكل جدول
/reports - عرض وإدارة التقارير المجدولة
/recipients - عرض قائمة مستقبلي الإشعارات

📊 **التقارير التلقائية:**
⏰ يومي: 9:00 صباحاً (آخر يوم)
⏰ أسبوعي: الأحد 10:00 صباحاً (آخر أسبوع)
⏰ شهري: أول يوم في الشهر 11:00 صباحاً (آخر شهر)

⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

    await update.message.reply_text(
        welcome_text,
        parse_mode='Markdown'
    )

async def get_id(update, context):
    """دالة عرض معرف المستخدم أو الجروب"""
    if not update.effective_user or not update.message:
        return

    try:
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        chat_type = update.effective_chat.type

        # معلومات المستخدم
        user_info = f"👤 **معلومات المستخدم:**\n"
        user_info += f"🆔 **معرف المستخدم:** `{user_id}`\n"
        user_info += f"👤 **الاسم:** {update.effective_user.first_name or 'غير محدد'}"

        if update.effective_user.last_name:
            user_info += f" {update.effective_user.last_name}"

        if update.effective_user.username:
            user_info += f"\n📝 **اسم المستخدم:** @{update.effective_user.username}"

        # معلومات المحادثة
        chat_info = f"\n\n💬 **معلومات المحادثة:**\n"
        chat_info += f"🆔 **معرف المحادثة:** `{chat_id}`\n"

        if chat_type == "private":
            chat_info += f"📱 **نوع المحادثة:** محادثة خاصة"
        elif chat_type == "group":
            chat_info += f"👥 **نوع المحادثة:** جروب"
            if update.effective_chat.title:
                chat_info += f"\n🏷️ **اسم الجروب:** {update.effective_chat.title}"
        elif chat_type == "supergroup":
            chat_info += f"👥 **نوع المحادثة:** سوبر جروب"
            if update.effective_chat.title:
                chat_info += f"\n🏷️ **اسم الجروب:** {update.effective_chat.title}"
        elif chat_type == "channel":
            chat_info += f"📢 **نوع المحادثة:** قناة"
            if update.effective_chat.title:
                chat_info += f"\n🏷️ **اسم القناة:** {update.effective_chat.title}"

        # ملاحظة للإشعارات
        notification_note = f"\n\n🔔 **للإشعارات التلقائية:**\n"
        if chat_type == "private":
            notification_note += f"استخدم هذا المعرف: `{user_id}`"
        else:
            notification_note += f"استخدم هذا المعرف: `{chat_id}`"

        notification_note += f"\n\n💡 **ملاحظة:** أرسل هذا المعرف للمطور لإضافتك لقائمة الإشعارات التلقائية"

        full_message = user_info + chat_info + notification_note

        await update.message.reply_text(
            full_message,
            parse_mode='Markdown'
        )

        print(f"📨 طلب معرف من: {update.effective_user.first_name or 'مستخدم'} - معرف المستخدم: {user_id}, معرف المحادثة: {chat_id}")

    except Exception as e:
        print(f"❌ خطأ في دالة get_id: {str(e)}")
        await update.message.reply_text(
            "❌ حدث خطأ في الحصول على المعرف",
            parse_mode='Markdown'
        )

async def test_db(update, context):
    """اختبار قاعدة البيانات"""
    if not update.effective_user or not update.message:
        return

    await update.message.reply_text("🔄 جاري اختبار قاعدة البيانات...")

    try:
        conn = connect_to_db()
        if not conn:
            await update.message.reply_text("❌ فشل الاتصال بقاعدة البيانات")
            return

        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM Acc_Customers WHERE IsDeleted=0")
        result = cursor.fetchone()
        total_customers = result[0] if result else 0
        conn.close()

        report = f"""🔧 **معلومات قاعدة البيانات:**

🖥️ **الخادم:** `{MAIN_DB_CONFIG['server']}`
🗄️ **قاعدة البيانات الأصلية:** `{MAIN_DB_CONFIG['database']}`
🗄️ **قاعدة بيانات الإشعارات:** `{NOTIFICATIONS_DB_CONFIG['database']}`
👥 **إجمالي العملاء:** {total_customers:,} عميل

✅ **الاتصال سليم**"""

        await update.message.reply_text(report, parse_mode='Markdown')

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في فحص قاعدة البيانات: {str(e)}")

async def statistics_report(update, context):
    """عرض التقرير الإحصائي الشامل"""
    if not update.effective_user or not update.message:
        return

    try:
        # تحديد عدد الأيام (افتراضي 3 أيام)
        days = 3

        # التحقق من وجود معامل لعدد الأيام
        if context.args and len(context.args) > 0:
            try:
                days = int(context.args[0])
                if days < 1 or days > 30:
                    await update.message.reply_text("❌ عدد الأيام يجب أن يكون بين 1 و 30")
                    return
            except ValueError:
                await update.message.reply_text("❌ يرجى إدخال رقم صحيح لعدد الأيام")
                return

        await update.message.reply_text(f"📊 جاري إعداد التقرير الإحصائي لآخر {days} أيام...")

        # الحصول على البيانات
        data = get_statistics_report(days)
        if not data:
            await update.message.reply_text("❌ لا توجد بيانات للعرض")
            return

        # تنسيق التقرير
        report_text = format_statistics_report(data, days)

        # إرسال التقرير
        await update.message.reply_text(report_text, parse_mode='Markdown')

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في إنشاء التقرير الإحصائي: {str(e)}")

async def statistics_table(update, context):
    """عرض التقرير الإحصائي في شكل جدول"""
    if not update.effective_user or not update.message:
        return

    try:
        # تحديد عدد الأيام (افتراضي 3)
        days = 3
        if context.args and len(context.args) > 0:
            try:
                days = int(context.args[0])
                if days < 1 or days > 30:
                    await update.message.reply_text("❌ عدد الأيام يجب أن يكون بين 1 و 30")
                    return
            except ValueError:
                await update.message.reply_text("❌ يرجى إدخال رقم صحيح لعدد الأيام")
                return

        # الحصول على البيانات
        data = get_statistics_report(days)
        if not data:
            await update.message.reply_text("❌ لا توجد بيانات للتقرير")
            return

        # تنسيق التقرير في شكل جدول
        report_text = format_statistics_table(data, days)

        # إرسال التقرير
        await update.message.reply_text(
            report_text,
            parse_mode='Markdown'
        )

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في إنشاء التقرير الإحصائي: {str(e)}")

async def manage_scheduled_reports(update, context):
    """إدارة التقارير المجدولة"""
    if not update.effective_user or not update.message:
        return

    try:
        # الحصول على التقارير المجدولة
        reports = get_scheduled_reports()

        if not reports:
            await update.message.reply_text("❌ لا توجد تقارير مجدولة")
            return

        # تنسيق قائمة التقارير
        report_text = "📊 **التقارير المجدولة:**\n\n"

        for report in reports:
            report_id = report[0]
            report_name = report[1]
            days_count = report[2]
            schedule_type = report[3]
            schedule_hour = report[4]
            schedule_minute = report[5]
            schedule_weekday = report[6]
            schedule_day = report[7]
            branch_ids = report[8]
            last_sent_date = report[9]

            # تحديد نوع الجدولة
            schedule_info = ""
            if schedule_type == "daily":
                schedule_info = f"يومي - {schedule_hour:02d}:{schedule_minute:02d}"
            elif schedule_type == "weekly":
                weekdays = ["الاثنين", "الثلاثاء", "الأربعاء", "الخميس", "الجمعة", "السبت", "الأحد"]
                day_name = weekdays[schedule_weekday] if schedule_weekday is not None else "غير محدد"
                schedule_info = f"أسبوعي - {day_name} {schedule_hour:02d}:{schedule_minute:02d}"
            elif schedule_type == "monthly":
                schedule_info = f"شهري - يوم {schedule_day} {schedule_hour:02d}:{schedule_minute:02d}"

            # معلومات الفروع
            branch_info = "جميع الفروع" if not branch_ids else f"فروع: {branch_ids}"

            # آخر إرسال
            last_sent = "لم يتم الإرسال بعد"
            if last_sent_date:
                last_sent = last_sent_date.strftime('%Y-%m-%d %H:%M')

            report_text += f"""🔹 **{report_name}** (ID: {report_id})
📅 المدة: {days_count} أيام
⏰ الجدولة: {schedule_info}
🏢 {branch_info}
📤 آخر إرسال: {last_sent}

"""

        report_text += "\n💡 استخدم /reports_config لتعديل الإعدادات"

        await update.message.reply_text(
            report_text,
            parse_mode='Markdown'
        )

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في عرض التقارير المجدولة: {str(e)}")

async def manage_recipients(update, context):
    """إدارة قائمة المستقبلين للإشعارات"""
    if not update.effective_user or not update.message:
        return

    try:
        conn = connect_to_notifications_db()
        if not conn:
            await update.message.reply_text("❌ لا يمكن الاتصال بقاعدة بيانات الإشعارات")
            return

        cursor = conn.cursor()
        cursor.execute("""
            SELECT ChatId, RecipientName, RecipientType, IsActive
            FROM Notification_Recipients
            ORDER BY RecipientType, RecipientName
        """)

        results = cursor.fetchall()
        conn.close()

        if results:
            report = "📋 **قائمة مستقبلي الإشعارات:**\n\n"

            active_count = 0
            inactive_count = 0

            for row in results:
                chat_id, name, recipient_type, is_active = row
                status = "✅ نشط" if is_active else "❌ معطل"
                icon = "👤" if recipient_type == 'User' else "👥"

                report += f"{icon} **{name}**\n"
                report += f"🆔 المعرف: `{chat_id}`\n"
                report += f"📊 الحالة: {status}\n\n"

                if is_active:
                    active_count += 1
                else:
                    inactive_count += 1

            report += f"📊 **الإحصائيات:**\n"
            report += f"✅ نشط: {active_count}\n"
            report += f"❌ معطل: {inactive_count}\n"
            report += f"📋 الإجمالي: {len(results)}"

        else:
            report = "⚠️ لا توجد مستقبلين مسجلين في قاعدة البيانات"

        await update.message.reply_text(report, parse_mode='Markdown')

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في عرض المستقبلين: {str(e)}")

def get_branch_settings():
    """الحصول على إعدادات الفروع من قاعدة البيانات"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return []

        cursor = conn.cursor()
        query = """
        SELECT BranchId, BranchName, IsActive, IncludeInReports, NotificationEnabled
        FROM Branch_Settings
        ORDER BY BranchName
        """
        cursor.execute(query)
        branches = cursor.fetchall()

        cursor.close()
        conn.close()
        return branches

    except Exception as e:
        print(f"❌ خطأ في الحصول على إعدادات الفروع: {str(e)}")
        return []

async def manage_branches(update, context):
    """إدارة إعدادات الفروع"""
    if not update.effective_user or not update.message:
        return

    try:
        # الحصول على إعدادات الفروع
        branches = get_branch_settings()

        if not branches:
            await update.message.reply_text("❌ لا توجد فروع في النظام")
            return

        # تنسيق قائمة الفروع
        branch_text = "🏢 **إعدادات الفروع:**\n\n"

        for branch in branches:
            branch_id = branch[0]
            branch_name = branch[1]
            is_active = branch[2]
            include_in_reports = branch[3]
            notification_enabled = branch[4]

            # رموز الحالة
            active_status = "🟢 نشط" if is_active else "🔴 غير نشط"
            report_status = "📊 في التقارير" if include_in_reports else "❌ مستبعد من التقارير"
            notification_status = "🔔 إشعارات مفعلة" if notification_enabled else "🔕 إشعارات معطلة"

            branch_text += f"""🏪 **{branch_name}** (ID: {branch_id})
├─ {active_status}
├─ {report_status}
└─ {notification_status}

"""

        branch_text += "\n💡 لتعديل إعدادات فرع معين، استخدم SQL في قاعدة البيانات Terra_Notifications_DB1"
        branch_text += "\n📋 جدول: Branch_Settings"

        await update.message.reply_text(
            branch_text,
            parse_mode='Markdown'
        )

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في عرض إعدادات الفروع: {str(e)}")

async def main():
    """الدالة الرئيسية للبوت"""
    print("🚀 بدء تشغيل Terra Bot المحسن مع الإشعارات")
    print("=" * 50)

    try:
        from telegram.ext import Application, CommandHandler

        # إنشاء التطبيق
        app = Application.builder().token(BOT_TOKEN).build()

        # إنشاء جدول التقارير المجدولة إذا لم يكن موجوداً
        create_scheduled_reports_table()

        # تهيئة البوت
        await app.initialize()

        # إضافة المعالجات
        app.add_handler(CommandHandler("start", start))
        app.add_handler(CommandHandler("test", test_db))
        app.add_handler(CommandHandler("id", get_id))
        app.add_handler(CommandHandler("e", statistics_report))
        app.add_handler(CommandHandler("table", statistics_table))
        app.add_handler(CommandHandler("reports", manage_scheduled_reports))
        app.add_handler(CommandHandler("recipients", manage_recipients))

        setup_msg = "✅ البوت جاهز!"
        print(setup_msg)
        print("📱 أرسل /start في التليجرام")
        print("🆔 أرسل /id لعرض المعرف")
        print("🧪 أرسل /test لاختبار قاعدة البيانات")
        print("📄 ملف Log: terra_bot.log")
        print("=" * 50)

        if logger:
            logger.info(setup_msg)
            logger.info("🔔 نظام الإشعارات نشط")
            logger.info("📊 مراقبة قاعدة البيانات مستمرة")

        # تشغيل مراقب قاعدة البيانات في مهمة منفصلة
        monitor_task = asyncio.create_task(monitor_all_data())
        print("🔍 تم بدء مراقب قاعدة البيانات")

        # تشغيل مجدول التقارير الإحصائية في مهمة منفصلة
        scheduler_task = asyncio.create_task(scheduled_statistics_report())
        print("📊 تم بدء مجدول التقارير الإحصائية")
        print("⏰ التقرير اليومي: 9:00 صباحاً")
        print("⏰ التقرير الأسبوعي: الأحد 10:00 صباحاً")
        print("⏰ التقرير الشهري: أول يوم في الشهر 11:00 صباحاً")
        print("💡 استخدم /e لعرض التقرير فوراً")
        print("💡 استخدم /e 7 لعرض تقرير آخر 7 أيام")

        # تشغيل البوت
        await app.start()
        await app.updater.start_polling(drop_pending_updates=True)

        # انتظار إيقاف البوت
        await asyncio.Event().wait()

    except ImportError:
        print("❌ خطأ: مكتبات التليجرام غير مثبتة")
        print("💡 شغل: pip install python-telegram-bot")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
        if logger:
            logger.error(f"خطأ في تشغيل البوت: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
