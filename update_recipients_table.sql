-- تحديث جدول Notification_Recipients لإضافة أعمدة الفروع
-- Terra Notifications Database Update Script

USE Terra_Notifications_DB1;
GO

-- التحقق من وجود الأعمدة الجديدة وإضافتها إذا لم تكن موجودة
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Notification_Recipients') AND name = 'ReceiveNasrBranch')
BEGIN
    ALTER TABLE Notification_Recipients 
    ADD ReceiveNasrBranch BIT DEFAULT 1;
    
    PRINT 'تم إضافة عمود ReceiveNasrBranch (فرع مدينة نصر)';
END
ELSE
BEGIN
    PRINT 'عمود ReceiveNasrBranch موجود بالفعل';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Notification_Recipients') AND name = 'ReceiveTajammuBranch')
BEGIN
    ALTER TABLE Notification_Recipients 
    ADD ReceiveTajammuBranch BIT DEFAULT 1;
    
    PRINT 'تم إضافة عمود ReceiveTajammuBranch (فرع التجمع)';
END
ELSE
BEGIN
    PRINT 'عمود ReceiveTajammuBranch موجود بالفعل';
END

-- تحديث البيانات الموجودة لتفعيل استقبال إشعارات كلا الفرعين افتراضياً
UPDATE Notification_Recipients 
SET ReceiveNasrBranch = 1, ReceiveTajammuBranch = 1
WHERE ReceiveNasrBranch IS NULL OR ReceiveTajammuBranch IS NULL;

-- إنشاء فهارس للأعمدة الجديدة لتحسين الأداء
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Recipients_NasrBranch')
BEGIN
    CREATE INDEX IX_Recipients_NasrBranch ON Notification_Recipients(ReceiveNasrBranch);
    PRINT 'تم إنشاء فهرس IX_Recipients_NasrBranch';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_Recipients_TajammuBranch')
BEGIN
    CREATE INDEX IX_Recipients_TajammuBranch ON Notification_Recipients(ReceiveTajammuBranch);
    PRINT 'تم إنشاء فهرس IX_Recipients_TajammuBranch';
END

-- عرض هيكل الجدول المحدث
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_NAME = 'Notification_Recipients'
ORDER BY ORDINAL_POSITION;

-- عرض البيانات الحالية مع الأعمدة الجديدة
SELECT 
    RecipientId,
    ChatId,
    RecipientName,
    RecipientType,
    IsActive,
    ReceiveNasrBranch,
    ReceiveTajammuBranch,
    AddDate
FROM Notification_Recipients
ORDER BY RecipientType, RecipientName;

PRINT 'تم تحديث جدول Notification_Recipients بنجاح!';
PRINT 'الأعمدة الجديدة:';
PRINT '- ReceiveNasrBranch: لتحديد استقبال إشعارات فرع مدينة نصر (BranchId = 2)';
PRINT '- ReceiveTajammuBranch: لتحديد استقبال إشعارات فرع التجمع (BranchId = 3)';
PRINT 'القيم: 1 = يستقبل الإشعارات، 0 = لا يستقبل الإشعارات';
