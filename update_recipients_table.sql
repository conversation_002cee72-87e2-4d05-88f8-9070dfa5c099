-- تحديث جدول Notifications_Log لإضافة أعمدة الفروع
-- Terra Notifications Database Update Script

USE Terra_Notifications_DB1;
GO

-- التحقق من وجود الأعمدة الجديدة وإضافتها إذا لم تكن موجودة
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Notifications_Log') AND name = 'SendToNasrBranch')
BEGIN
    ALTER TABLE Notifications_Log
    ADD SendToNasrBranch BIT DEFAULT 1;

    PRINT 'تم إضافة عمود SendToNasrBranch (إرسال لفرع مدينة نصر)';
END
ELSE
BEGIN
    PRINT 'عمود SendToNasrBranch موجود بالفعل';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('Notifications_Log') AND name = 'SendToTajammuBranch')
BEGIN
    ALTER TABLE Notifications_Log
    ADD SendToTajammuBranch BIT DEFAULT 1;

    PRINT 'تم إضافة عمود SendToTajammuBranch (إرسال لفرع التجمع)';
END
ELSE
BEGIN
    PRINT 'عمود SendToTajammuBranch موجود بالفعل';
END

-- تحديث البيانات الموجودة حسب الفرع المصدر
-- إذا كان الإشعار من فرع مدينة نصر، يرسل لمستقبلي فرع مدينة نصر
-- إذا كان الإشعار من فرع التجمع، يرسل لمستقبلي فرع التجمع
UPDATE Notifications_Log
SET
    SendToNasrBranch = CASE WHEN BranchId = 2 THEN 1 ELSE 0 END,
    SendToTajammuBranch = CASE WHEN BranchId = 3 THEN 1 ELSE 0 END
WHERE SendToNasrBranch IS NULL OR SendToTajammuBranch IS NULL;

-- إنشاء فهارس للأعمدة الجديدة لتحسين الأداء
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_NotificationsLog_NasrBranch')
BEGIN
    CREATE INDEX IX_NotificationsLog_NasrBranch ON Notifications_Log(SendToNasrBranch);
    PRINT 'تم إنشاء فهرس IX_NotificationsLog_NasrBranch';
END

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_NotificationsLog_TajammuBranch')
BEGIN
    CREATE INDEX IX_NotificationsLog_TajammuBranch ON Notifications_Log(SendToTajammuBranch);
    PRINT 'تم إنشاء فهرس IX_NotificationsLog_TajammuBranch';
END

-- عرض هيكل الجدول المحدث
SELECT
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_NAME = 'Notifications_Log'
ORDER BY ORDINAL_POSITION;

-- عرض البيانات الحالية مع الأعمدة الجديدة (آخر 10 سجلات)
SELECT TOP 10
    LogId,
    DataType,
    RecordId,
    RecordName,
    BranchId,
    BranchName,
    SendToNasrBranch,
    SendToTajammuBranch,
    NotificationDate,
    NotificationStatus
FROM Notifications_Log
ORDER BY LogId DESC;

PRINT 'تم تحديث جدول Notifications_Log بنجاح!';
PRINT 'الأعمدة الجديدة:';
PRINT '- SendToNasrBranch: لتحديد إرسال الإشعار لمستقبلي فرع مدينة نصر';
PRINT '- SendToTajammuBranch: لتحديد إرسال الإشعار لمستقبلي فرع التجمع';
PRINT 'القيم: 1 = يرسل الإشعار، 0 = لا يرسل الإشعار';
PRINT 'ملاحظة: تم تحديث البيانات الموجودة حسب فرع المصدر (BranchId)';
