#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار سريع للنظام المحسن
"""

import asyncio
import pyodbc
from datetime import datetime
from telegram import Bot

# إعدادات
BOT_TOKEN = "**********************************************"
NOTIFICATION_CHAT_IDS = ["1107000748", "1206533289"]

DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

def connect_to_db():
    """الاتصال بقاعدة البيانات"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['user']};PWD={DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def get_latest_customer_with_details():
    """الحصول على آخر عميل مع جميع التفاصيل"""
    try:
        conn = connect_to_db()
        if not conn:
            return None
        
        cursor = conn.cursor()
        
        query = """
        SELECT TOP 1 
            c.CustomerCode,
            c.NameAr,
            c.NameEn,
            c.MainPhoneNo,
            c.SubMainPhoneNo,
            c.Email,
            c.Address,
            c.NationalId,
            c.Notes,
            c.AddDate,
            c.UpdateDate,
            
            -- بيانات الفرع
            c.BranchId,
            CASE 
                WHEN c.BranchId = 2 THEN 'فرع مدينة نصر'
                WHEN c.BranchId = 3 THEN 'فرع التجمع'
                ELSE 'غير محدد'
            END AS BranchName,
            
            -- بيانات المدينة/المنطقة
            city.NameAr AS CityName,
            
            -- بيانات طريقة الدفع
            pay.NameAr AS PayTypeName,
            
            -- بيانات وسائل التواصل
            social.NameAr AS SocialMediaName,
            
            -- بيانات من أنشأ العميل
            addUser.UserName AS AddedByUser,
            addUser.FullName AS AddedByFullName
            
        FROM Acc_Customers c
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Acc_PayType pay ON c.PayTypeId = pay.PayTypeId  
        LEFT JOIN Sys_SocialMedia social ON c.SocialMediaId = social.SocialMediaId
        LEFT JOIN Sys_Users addUser ON c.AddUser = addUser.UserId
        
        WHERE c.IsDeleted = 0
        ORDER BY c.AddDate DESC
        """
        
        cursor.execute(query)
        result = cursor.fetchone()
        conn.close()
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في الحصول على تفاصيل العميل: {str(e)}")
        return None

async def send_test_notification():
    """إرسال إشعار اختبار"""
    try:
        customer = get_latest_customer_with_details()
        
        if not customer:
            print("❌ لم يتم العثور على عميل")
            return
        
        message = f"""👤 **اختبار النظام المحسن**

🔢 **الكود:** {customer[0]}
👤 **الاسم العربي:** {customer[1] or 'غير محدد'}
🔤 **الاسم الإنجليزي:** {customer[2] or 'غير محدد'}
📱 **الهاتف الأساسي:** {customer[3] or 'غير محدد'}
📞 **الهاتف الفرعي:** {customer[4] or 'غير محدد'}
📧 **الإيميل:** {customer[5] or 'غير محدد'}
🏠 **العنوان:** {customer[6] or 'غير محدد'}
🆔 **الرقم القومي:** {customer[7] or 'غير محدد'}
📝 **ملاحظات:** {customer[8] or 'لا توجد'}

🏢 **الفرع:** {customer[12]}
🌍 **المدينة/المنطقة:** {customer[13] or 'غير محدد'}
💳 **طريقة الدفع:** {customer[14] or 'غير محدد'}
📱 **وسيلة التواصل:** {customer[15] or 'غير محدد'}

👨‍💼 **تم الإنشاء بواسطة:** {customer[17] or 'غير محدد'} ({customer[16] or 'غير محدد'})
📅 **تاريخ الإضافة:** {customer[9].strftime('%Y-%m-%d %H:%M') if customer[9] else 'غير محدد'}"""

        if customer[10]:  # إذا كان هناك تاريخ تحديث
            message += f"\n🔄 **آخر تحديث:** {customer[10].strftime('%Y-%m-%d %H:%M')}"
        
        bot = Bot(token=BOT_TOKEN)
        
        notification_text = f"🔔 **اختبار النظام المحسن - Terra Bot**\n\n{message}\n\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        for chat_id in NOTIFICATION_CHAT_IDS:
            try:
                await bot.send_message(
                    chat_id=chat_id,
                    text=notification_text,
                    parse_mode='Markdown'
                )
                print(f"✅ تم إرسال اختبار النظام المحسن إلى {chat_id}")
                
            except Exception as e:
                print(f"❌ فشل إرسال اختبار إلى {chat_id}: {str(e)}")
        
    except Exception as e:
        print(f"❌ خطأ في إرسال الاختبار: {str(e)}")

async def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار النظام المحسن")
    print("=" * 50)
    
    await send_test_notification()
    
    print("\n" + "=" * 50)
    print("✅ انتهى الاختبار")

if __name__ == "__main__":
    asyncio.run(main())
