#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إرسال فوري لإشعارات العملاء الجدد
"""

import asyncio
import pyodbc
from datetime import datetime
from telegram import Bot

# إعدادات
BOT_TOKEN = "**********************************************"
CHAT_IDS = ["1107000748", "1206533289"]

DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

async def send_latest_customers():
    """إرسال آخر العملاء فوراً"""
    print("🔍 جاري البحث عن آخر العملاء...")
    
    try:
        # الاتصال بقاعدة البيانات
        conn_str = f"DRIVER={{SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['user']};PWD={DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        # الحصول على آخر 3 عملاء
        cursor.execute("""
            SELECT TOP 3 CustomerCode, NameAr, MainPhoneNo, BranchId, AddDate 
            FROM Acc_Customers 
            WHERE IsDeleted = 0 
            ORDER BY AddDate DESC
        """)
        
        customers = cursor.fetchall()
        conn.close()
        
        if not customers:
            print("❌ لم يتم العثور على عملاء")
            return
        
        print(f"✅ تم العثور على {len(customers)} عملاء")
        
        # إنشاء البوت
        bot = Bot(token=BOT_TOKEN)
        
        # إرسال إشعار لكل عميل
        for i, customer in enumerate(customers, 1):
            branch_name = "فرع التجمع" if customer[3] == 3 else "فرع مدينة نصر" if customer[3] == 2 else "غير محدد"
            
            message = f"""🔔 **إشعار من Terra Bot**

👤 **عميل #{i}**

🔢 **الكود:** {customer[0]}
👤 **الاسم:** {customer[1] or 'غير محدد'}
📱 **الهاتف:** {customer[2] or 'غير محدد'}
🏢 **الفرع:** {branch_name}
📅 **التاريخ:** {customer[4].strftime('%Y-%m-%d %H:%M') if customer[4] else 'غير محدد'}

⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
            
            # إرسال للمستخدمين
            for chat_id in CHAT_IDS:
                try:
                    await bot.send_message(
                        chat_id=chat_id,
                        text=message,
                        parse_mode='Markdown'
                    )
                    print(f"✅ تم إرسال العميل {customer[0]} إلى {chat_id}")
                    
                except Exception as e:
                    print(f"❌ فشل إرسال إلى {chat_id}: {str(e)}")
            
            # انتظار قصير
            await asyncio.sleep(0.5)
        
        print("\n🎉 تم إرسال جميع الإشعارات!")
        
    except Exception as e:
        print(f"❌ خطأ: {str(e)}")

if __name__ == "__main__":
    print("🚀 إرسال فوري لآخر العملاء")
    print("=" * 40)
    asyncio.run(send_latest_customers())
