#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف إعداد لتحويل Terra Bot إلى ملف تنفيذي .exe مع إعداد التشغيل التلقائي
"""

from cx_Freeze import setup, Executable
import sys
import os
import winreg
import shutil

# إعدادات البناء
build_exe_options = {
    "packages": [
        "asyncio",
        "telegram",
        "telegram.ext", 
        "pyodbc",
        "datetime",
        "logging",
        "threading",
        "json",
        "time"
    ],
    "excludes": [
        "tkinter",
        "unittest",
        "email",
        "html",
        "http",
        "urllib",
        "xml"
    ],
    "include_files": [
        # يمكن إضافة ملفات إضافية هنا إذا لزم الأمر
    ],
    "optimize": 2,
    "include_msvcrt": True
}

# إعدادات الملف التنفيذي
base = None
if sys.platform == "win32":
    base = "Console"  # استخدم "Win32GUI" لإخفاء نافذة الكونسول

def add_to_startup(exe_path, app_name="TerraBot Enhanced"):
    """إضافة التطبيق للتشغيل التلقائي عند بداية النظام"""
    try:
        # فتح مفتاح التسجيل للتشغيل التلقائي
        key = winreg.OpenKey(
            winreg.HKEY_CURRENT_USER,
            r"Software\Microsoft\Windows\CurrentVersion\Run",
            0,
            winreg.KEY_SET_VALUE
        )

        # إضافة التطبيق
        winreg.SetValueEx(key, app_name, 0, winreg.REG_SZ, exe_path)
        winreg.CloseKey(key)

        print(f"✅ تم إضافة {app_name} للتشغيل التلقائي")
        return True

    except Exception as e:
        print(f"❌ خطأ في إضافة التشغيل التلقائي: {e}")
        return False

def remove_from_startup(app_name="TerraBot Enhanced"):
    """إزالة التطبيق من التشغيل التلقائي"""
    try:
        key = winreg.OpenKey(
            winreg.HKEY_CURRENT_USER,
            r"Software\Microsoft\Windows\CurrentVersion\Run",
            0,
            winreg.KEY_SET_VALUE
        )

        winreg.DeleteValue(key, app_name)
        winreg.CloseKey(key)

        print(f"✅ تم إزالة {app_name} من التشغيل التلقائي")
        return True

    except FileNotFoundError:
        print(f"⚠️ {app_name} غير موجود في التشغيل التلقائي")
        return True
    except Exception as e:
        print(f"❌ خطأ في إزالة التشغيل التلقائي: {e}")
        return False

def create_startup_script():
    """إنشاء سكريبت للتحكم في التشغيل التلقائي"""
    script_content = '''@echo off
title Terra Bot - إعداد التشغيل التلقائي
echo ========================================
echo     Terra Bot Enhanced - إعداد التشغيل التلقائي
echo ========================================
echo.
echo اختر أحد الخيارات:
echo 1. إضافة للتشغيل التلقائي عند بداية النظام
echo 2. إزالة من التشغيل التلقائي
echo 3. إلغاء
echo.
set /p choice="اختيارك (1-3): "

if "%choice%"=="1" (
    echo.
    echo جاري إضافة Terra Bot للتشغيل التلقائي...
    reg add "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run" /v "TerraBot Enhanced" /t REG_SZ /d "%~dp0TerraBot_Enhanced.exe" /f
    if %errorlevel%==0 (
        echo ✅ تم إضافة Terra Bot للتشغيل التلقائي بنجاح
        echo سيتم تشغيل البوت تلقائياً عند بداية تشغيل النظام
    ) else (
        echo ❌ فشل في إضافة التشغيل التلقائي
    )
) else if "%choice%"=="2" (
    echo.
    echo جاري إزالة Terra Bot من التشغيل التلقائي...
    reg delete "HKCU\\Software\\Microsoft\\Windows\\CurrentVersion\\Run" /v "TerraBot Enhanced" /f
    if %errorlevel%==0 (
        echo ✅ تم إزالة Terra Bot من التشغيل التلقائي بنجاح
    ) else (
        echo ⚠️ Terra Bot غير موجود في التشغيل التلقائي أو حدث خطأ
    )
) else (
    echo تم الإلغاء
)

echo.
pause
'''

    try:
        with open("startup_manager.bat", "w", encoding="utf-8") as f:
            f.write(script_content)
        print("✅ تم إنشاء ملف إدارة التشغيل التلقائي: startup_manager.bat")
    except Exception as e:
        print(f"❌ خطأ في إنشاء ملف إدارة التشغيل التلقائي: {e}")

# معلومات التطبيق
setup(
    name="Terra Bot Enhanced",
    version="2.0",
    description="Terra Bot المحسن مع نظام إشعارات تلقائية شامل",
    author="Terra Company",
    options={"build_exe": build_exe_options},
    executables=[
        Executable(
            "enhanced_notifications.py",
            base=base,
            target_name="TerraBot_Enhanced.exe",
            icon=None  # يمكن إضافة أيقونة هنا
        )
    ]
)

# إنشاء ملف إدارة التشغيل التلقائي
print("\n" + "="*50)
print("إنشاء أدوات إضافية...")
create_startup_script()

# سؤال المستخدم عن التشغيل التلقائي
print("\n" + "="*50)
print("هل تريد إضافة Terra Bot للتشغيل التلقائي عند بداية النظام؟")
print("1. نعم - إضافة للتشغيل التلقائي")
print("2. لا - عدم إضافة")
print("3. لاحقاً - يمكنك استخدام ملف startup_manager.bat لاحقاً")

try:
    choice = input("اختيارك (1-3): ").strip()

    if choice == "1":
        # البحث عن الملف التنفيذي المُنشأ
        build_dirs = [d for d in os.listdir(".") if d.startswith("build")]
        if build_dirs:
            build_dir = build_dirs[0]
            exe_dirs = [d for d in os.listdir(build_dir) if d.startswith("exe.")]
            if exe_dirs:
                exe_path = os.path.join(os.getcwd(), build_dir, exe_dirs[0], "TerraBot_Enhanced.exe")
                if os.path.exists(exe_path):
                    add_to_startup(exe_path)
                else:
                    print("⚠️ لم يتم العثور على الملف التنفيذي. استخدم startup_manager.bat لاحقاً")
            else:
                print("⚠️ لم يتم العثور على مجلد exe. استخدم startup_manager.bat لاحقاً")
        else:
            print("⚠️ لم يتم العثور على مجلد build. استخدم startup_manager.bat لاحقاً")

    elif choice == "2":
        print("✅ لن يتم إضافة التشغيل التلقائي")

    else:
        print("✅ يمكنك استخدام ملف startup_manager.bat لاحقاً لإدارة التشغيل التلقائي")

except:
    print("✅ يمكنك استخدام ملف startup_manager.bat لاحقاً لإدارة التشغيل التلقائي")

print("\n" + "="*50)
print("🎉 تم الانتهاء من الإعداد!")
print("📁 الملفات المُنشأة:")
print("   - TerraBot_Enhanced.exe (في مجلد build)")
print("   - startup_manager.bat (لإدارة التشغيل التلقائي)")
print("="*50)
