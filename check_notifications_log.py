#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pyodbc
from datetime import datetime

# إعدادات قاعدة بيانات الإشعارات
NOTIFICATIONS_DB_CONFIG = {
    'server': '.',
    'database': 'Terra_Notifications_DB1',
    'user': 'sa',
    'password': 'Ret_ME@'
}

def connect_to_notifications_db():
    """الاتصال بقاعدة بيانات الإشعارات"""
    try:
        conn_str = (
            f"DRIVER={{ODBC Driver 17 for SQL Server}};"
            f"SERVER={NOTIFICATIONS_DB_CONFIG['server']};"
            f"DATABASE={NOTIFICATIONS_DB_CONFIG['database']};"
            f"UID={NOTIFICATIONS_DB_CONFIG['user']};"
            f"PWD={NOTIFICATIONS_DB_CONFIG['password']};"
        )
        return pyodbc.connect(conn_str)
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة بيانات الإشعارات: {str(e)}")
        return None

def check_latest_notifications():
    """فحص آخر الإشعارات في قاعدة البيانات"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return
        
        cursor = conn.cursor()
        
        print("🔍 آخر 10 إشعارات في قاعدة البيانات:")
        print("=" * 80)
        
        # جلب آخر 10 إشعارات
        cursor.execute("""
            SELECT TOP 10 
                DataType, RecordId, RecordName, BranchName, RecordUniqueId, 
                AddDate, AddedBy, NotificationDate, NotificationStatus
            FROM Notifications_Log 
            WHERE IsActive = 1
            ORDER BY NotificationDate DESC
        """)
        
        results = cursor.fetchall()
        
        if results:
            for i, row in enumerate(results, 1):
                print(f"{i}. {row[0]} - كود: {row[1]} - اسم: {row[2]}")
                print(f"   فرع: {row[3]} - معرف فريد: {row[4]}")
                print(f"   تاريخ الإضافة: {row[5]} - أضيف بواسطة: {row[6]}")
                print(f"   تاريخ الإشعار: {row[7]} - حالة: {row[8]}")
                print("-" * 60)
        else:
            print("📊 لا توجد إشعارات في قاعدة البيانات")
        
        # فحص آخر معرف لكل نوع
        print("\n📊 آخر معرف فريد لكل نوع بيانات:")
        print("=" * 50)
        
        data_types = ['عميل', 'معاينة', 'اجتماع', 'تصميم', 'عقد']
        
        for data_type in data_types:
            cursor.execute("""
                SELECT TOP 1 RecordUniqueId, RecordId, NotificationDate
                FROM Notifications_Log 
                WHERE DataType = ? AND IsActive = 1 AND RecordUniqueId IS NOT NULL
                ORDER BY RecordUniqueId DESC
            """, (data_type,))
            
            result = cursor.fetchone()
            if result:
                print(f"{data_type}: آخر معرف فريد = {result[0]} (كود: {result[1]}) - {result[2]}")
            else:
                print(f"{data_type}: لا توجد بيانات")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في فحص الإشعارات: {str(e)}")

def main():
    print("🚀 فحص سجل الإشعارات...")
    check_latest_notifications()
    print("\n🎯 انتهى الفحص")

if __name__ == "__main__":
    main()
