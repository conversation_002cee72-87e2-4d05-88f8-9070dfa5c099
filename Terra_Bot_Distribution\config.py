#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ملف الإعدادات لنظام Terra Bot
يحتوي على جميع البيانات الحساسة والإعدادات القابلة للتخصيص
"""

# =============================================================================
# إعدادات بوت التليجرام
# =============================================================================

# توكن البوت (احصل عليه من @BotFather في التليجرام)
BOT_TOKEN = "7664606990:AAFbBWGShtg00af-qkbCm9VRpvqq7M--bU0"

# معرفات المستخدمين والجروبات للإشعارات (احتياطية)
# يتم استخدامها في حالة عدم توفر قاعدة بيانات الإشعارات
DEFAULT_NOTIFICATION_CHAT_IDS = [
    1107000748,      # مستخدم 1
    1206533289,      # مستخدم 2
    -1002255521584,  # جروب TERRABOT
    -1002308493862   # الجروب الرئيسي
]

# =============================================================================
# إعدادات قواعد البيانات
# =============================================================================

# إعدادات قاعدة البيانات الأصلية (Terra) - قراءة فقط
MAIN_DB_CONFIG = {
    'server': '.',                    # عنوان الخادم (. للخادم المحلي)
    'database': 'Terra',              # اسم قاعدة البيانات الأصلية
    'user': 'sa',                     # اسم المستخدم
    'password': 'Ret_ME@'             # كلمة المرور
}

# إعدادات قاعدة بيانات الإشعارات (Terra_Notifications_DB1) - قراءة وكتابة
NOTIFICATIONS_DB_CONFIG = {
    'server': '.',                           # عنوان الخادم (. للخادم المحلي)
    'database': 'Terra_Notifications_DB1',  # اسم قاعدة بيانات الإشعارات
    'user': 'sa',                            # اسم المستخدم
    'password': 'Ret_ME@'                    # كلمة المرور
}

# =============================================================================
# إعدادات المراقبة والإشعارات
# =============================================================================

# فترة الانتظار بين دورات المراقبة (بالثواني)
MONITORING_INTERVAL = 30

# فترة الانتظار في حالة حدوث خطأ (بالثواني)
ERROR_RETRY_INTERVAL = 60

# عدد المحاولات لإعادة الاتصال بقاعدة البيانات
MAX_RETRY_ATTEMPTS = 3

# =============================================================================
# إعدادات الملفات
# =============================================================================

# ملف Excel لحفظ سجل الإشعارات
EXCEL_FILE = "terra_notifications_log.xlsx"

# ملف Log لحفظ الأخطاء والأحداث
LOG_FILE = "terra_bot.log"

# مستوى التسجيل (DEBUG, INFO, WARNING, ERROR, CRITICAL)
LOG_LEVEL = "INFO"

# =============================================================================
# إعدادات الفروع
# =============================================================================

# معرفات الفروع وأسماؤها
BRANCHES = {
    2: {
        'name': 'مدينة نصر',
        'icon': '🏢',
        'enabled': True
    },
    3: {
        'name': 'التجمع',
        'icon': '🏬',
        'enabled': True
    }
}

# =============================================================================
# إعدادات أنواع البيانات المراقبة
# =============================================================================

# أنواع البيانات التي يتم مراقبتها
MONITORED_DATA_TYPES = {
    'customers': {
        'enabled': True,
        'arabic_name': 'عميل',
        'icon': '👤',
        'table': 'Acc_Customers',
        'id_field': 'CustomerId',
        'code_field': 'CustomerCode'
    },
    'previews': {
        'enabled': True,
        'arabic_name': 'معاينة',
        'icon': '👁️',
        'table': 'Sys_Previews',
        'id_field': 'PreviewId',
        'code_field': 'PreviewCode'
    },
    'meetings': {
        'enabled': True,
        'arabic_name': 'اجتماع',
        'icon': '🤝',
        'table': 'Sys_Meetings',
        'id_field': 'MeetingId',
        'code_field': 'MeetingCode'
    },
    'designs': {
        'enabled': True,
        'arabic_name': 'تصميم',
        'icon': '🎨',
        'table': 'Sys_Designs',
        'id_field': 'DesignId',
        'code_field': 'DesignCode'
    },
    'contracts': {
        'enabled': True,
        'arabic_name': 'عقد',
        'icon': '📄',
        'table': 'Acc_Contracts',
        'id_field': 'ContractId',
        'code_field': 'ContractCode'
    }
}

# =============================================================================
# إعدادات الرسائل والتنسيق
# =============================================================================

# تنسيق الرسائل
MESSAGE_FORMAT = {
    'use_markdown': True,
    'include_timestamp': True,
    'include_branch_icon': True,
    'max_message_length': 4000  # الحد الأقصى لطول الرسالة في التليجرام
}

# =============================================================================
# إعدادات الأمان
# =============================================================================

# قائمة المستخدمين المخولين لاستخدام أوامر الإدارة
ADMIN_USER_IDS = [
    1107000748,  # المدير الأول
    1206533289   # المدير الثاني
]

# تفعيل وضع الصيانة (إيقاف الإشعارات مؤقتاً)
MAINTENANCE_MODE = False

# =============================================================================
# ملاحظات مهمة
# =============================================================================

"""
ملاحظات للمطور:

1. أمان البيانات:
   - لا تشارك هذا الملف مع أي شخص
   - احتفظ بنسخة احتياطية آمنة من هذا الملف
   - فكر في استخدام متغيرات البيئة للبيانات الحساسة

2. تخصيص الإعدادات:
   - يمكنك تعديل أي إعداد حسب احتياجاتك
   - تأكد من إعادة تشغيل البوت بعد أي تغيير
   - اختبر الإعدادات الجديدة قبل التطبيق في الإنتاج

3. إضافة فروع جديدة:
   - أضف الفرع الجديد في قاموس BRANCHES
   - تأكد من وجود البيانات في قاعدة البيانات
   - اختبر الإشعارات للفرع الجديد

4. إضافة أنواع بيانات جديدة:
   - أضف النوع الجديد في قاموس MONITORED_DATA_TYPES
   - تأكد من وجود الجدول والحقول المطلوبة
   - قم بإنشاء دالة مراقبة منفصلة إذا لزم الأمر

5. استكشاف الأخطاء:
   - راجع ملف terra_bot.log للأخطاء
   - تأكد من صحة إعدادات قاعدة البيانات
   - تحقق من صحة توكن البوت
"""
