#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pyodbc
from datetime import datetime

def check_unique_constraint():
    """فحص الـ UNIQUE constraint الحالي"""
    try:
        # الاتصال بقاعدة بيانات الإشعارات
        conn_str = "DRIVER={SQL Server};SERVER=.;DATABASE=Terra_Notifications_DB1;UID=sa;PWD=*******"
        conn = pyodbc.connect(conn_str)
        
        cursor = conn.cursor()
        
        print("🔍 فحص الـ UNIQUE constraints على جدول Notifications_Log...")
        
        # جلب معلومات الـ constraints
        cursor.execute("""
            SELECT 
                tc.CONSTRAINT_NAME,
                tc.CONSTRAINT_TYPE,
                kcu.COLUMN_NAME
            FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
            JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu 
                ON tc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
            WHERE tc.TABLE_NAME = 'Notifications_Log'
                AND tc.CONSTRAINT_TYPE IN ('UNIQUE', 'PRIMARY KEY')
            ORDER BY tc.CONSTRAINT_NAME, kcu.ORDINAL_POSITION
        """)
        
        constraints = cursor.fetchall()
        
        if constraints:
            print("📊 الـ constraints الموجودة:")
            current_constraint = None
            columns = []
            
            for row in constraints:
                constraint_name = row[0]
                constraint_type = row[1]
                column_name = row[2]
                
                if constraint_name != current_constraint:
                    if current_constraint:
                        print(f"   {current_constraint} ({constraint_type}): {', '.join(columns)}")
                    current_constraint = constraint_name
                    columns = [column_name]
                else:
                    columns.append(column_name)
            
            # طباعة آخر constraint
            if current_constraint:
                print(f"   {current_constraint} ({constraint_type}): {', '.join(columns)}")
        else:
            print("❌ لم يتم العثور على constraints")
        
        print("\n🔍 فحص البيانات المتضاربة...")

        # فحص معاينة 273
        cursor.execute("""
            SELECT DataType, RecordId, RecordName, RecordUniqueId, AddDate, IsActive
            FROM Notifications_Log
            WHERE DataType = 'معاينة' AND RecordId = '273'
            ORDER BY AddDate DESC
        """)

        results_273 = cursor.fetchall()
        if results_273:
            print(f"\n📊 معاينة 273:")
            for row in results_273:
                print(f"   {row[0]} - كود: {row[1]} - معرف: {row[3]} - نشط: {row[5]} - تاريخ: {row[4]}")
        else:
            print("\n❌ معاينة 273 غير موجودة")

        # فحص اجتماع 65
        cursor.execute("""
            SELECT DataType, RecordId, RecordName, RecordUniqueId, AddDate, IsActive
            FROM Notifications_Log
            WHERE DataType = 'اجتماع' AND RecordId = '65'
            ORDER BY AddDate DESC
        """)

        results_65 = cursor.fetchall()
        if results_65:
            print(f"\n📊 اجتماع 65:")
            for row in results_65:
                print(f"   {row[0]} - كود: {row[1]} - معرف: {row[3]} - نشط: {row[5]} - تاريخ: {row[4]}")
        else:
            print("\n❌ اجتماع 65 غير موجود")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    check_unique_constraint()
