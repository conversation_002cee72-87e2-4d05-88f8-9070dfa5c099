#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Terra Bot - Simple Fixed Version
بوت تليجرام مصحح وبسيط مع نظام إشعارات تلقائية
"""

import logging
import asyncio
import threading
from datetime import datetime

# إعداد الـ logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

# توكن البوت
BOT_TOKEN = "7664606990:AAFbBWGShtg00af-qkbCm9VRpvqq7M--bU0"

# إعدادات الإشعارات التلقائية
NOTIFICATION_CHAT_IDS = [
    "1107000748",      # مستخدم 1
    "1206533289",      # مستخدم 2
    # "-1002255521584"   # جروب TERRABOT (سيتم تفعيله بعد إضافة البوت للجروب)
]
ENABLE_NOTIFICATIONS = True

# متغيرات لتتبع آخر البيانات
last_customer_id = None

def connect_to_db():
    """الاتصال بقاعدة البيانات"""
    try:
        import pyodbc
        conn_str = f"DRIVER={{SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['user']};PWD={DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        return conn
    except Exception as e:
        logger.error(f"خطأ في الاتصال بقاعدة البيانات: {e}")
        return None

async def send_notification(bot, message):
    """إرسال إشعار تلقائي"""
    if not ENABLE_NOTIFICATIONS or not NOTIFICATION_CHAT_IDS:
        return
    
    notification_text = f"🔔 **إشعار تلقائي من Terra Bot**\n\n{message}\n\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    
    for chat_id in NOTIFICATION_CHAT_IDS:
        try:
            await bot.send_message(
                chat_id=chat_id,
                text=notification_text,
                parse_mode='Markdown'
            )
            print(f"✅ تم إرسال إشعار إلى {chat_id}")
        except Exception as e:
            print(f"❌ فشل إرسال إشعار إلى {chat_id}: {str(e)}")

def get_latest_customer():
    """الحصول على آخر عميل"""
    try:
        conn = connect_to_db()
        if not conn:
            return None
        
        cursor = conn.cursor()
        query = """
        SELECT TOP 1 
            c.CustomerCode,
            c.NameAr,
            c.MainPhoneNo,
            c.BranchId,
            CASE 
                WHEN c.BranchId = 2 THEN 'فرع مدينة نصر'
                WHEN c.BranchId = 3 THEN 'فرع التجمع'
                ELSE 'غير محدد'
            END AS BranchName,
            c.AddDate
        FROM Acc_Customers c
        WHERE c.IsDeleted = 0
        ORDER BY c.AddDate DESC
        """
        
        cursor.execute(query)
        result = cursor.fetchone()
        conn.close()
        return result
        
    except Exception as e:
        print(f"❌ خطأ في الحصول على العميل: {str(e)}")
        return None

def check_new_customers():
    """فحص العملاء الجدد"""
    global last_customer_id
    
    try:
        customer = get_latest_customer()
        
        if customer and customer[0] != last_customer_id:
            last_customer_id = customer[0]
            
            message = f"""👤 **عميل جديد تم إضافته**

🔢 **الكود:** {customer[0]}
👤 **الاسم:** {customer[1] or 'غير محدد'}
📱 **الهاتف:** {customer[2] or 'غير محدد'}
🏢 **الفرع:** {customer[4]}
📅 **تاريخ الإضافة:** {customer[5].strftime('%Y-%m-%d %H:%M') if customer[5] else 'غير محدد'}"""
            
            return message
        
        return None
        
    except Exception as e:
        print(f"❌ خطأ في فحص العملاء الجدد: {str(e)}")
        return None

def initialize_last_customer():
    """تهيئة آخر عميل"""
    global last_customer_id
    
    try:
        customer = get_latest_customer()
        last_customer_id = customer[0] if customer else None
        print(f"✅ تم تهيئة آخر عميل: {last_customer_id}")
    except Exception as e:
        print(f"❌ خطأ في تهيئة آخر عميل: {str(e)}")

async def monitor_database(bot):
    """مراقبة قاعدة البيانات"""
    print("🔍 بدء مراقبة قاعدة البيانات...")
    
    initialize_last_customer()
    cycle = 0
    
    while True:
        try:
            cycle += 1
            
            if cycle % 10 == 1:
                print(f"🔄 مراقبة - دورة #{cycle} - {datetime.now().strftime('%H:%M:%S')}")
            
            new_customer = check_new_customers()
            if new_customer:
                print("🔔 عميل جديد!")
                await send_notification(bot, new_customer)
            
            await asyncio.sleep(30)
            
        except Exception as e:
            print(f"❌ خطأ في المراقبة: {str(e)}")
            await asyncio.sleep(60)

def test_database():
    """اختبار قاعدة البيانات"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ فشل الاتصال بقاعدة البيانات"
        
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM Acc_Customers WHERE IsDeleted=0")
        result = cursor.fetchone()
        total_customers = result[0] if result else 0
        conn.close()
        
        return f"""🔧 **معلومات قاعدة البيانات:**

🖥️ **الخادم:** `{DB_CONFIG['server']}`
🗄️ **قاعدة البيانات:** `{DB_CONFIG['database']}`
👥 **إجمالي العملاء:** {total_customers:,} عميل"""
        
    except Exception as e:
        return f"❌ خطأ في فحص قاعدة البيانات: {str(e)}"

def main():
    """الدالة الرئيسية"""
    print("🚀 بدء تشغيل Terra Bot المصحح")
    print(f"📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    try:
        from telegram import Update, ReplyKeyboardMarkup, KeyboardButton, Bot
        from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes
        print("✅ تم استيراد مكتبات التليجرام")
    except ImportError as e:
        print(f"❌ خطأ في استيراد المكتبات: {e}")
        return
    
    # اختبار قاعدة البيانات
    test_result = test_database()
    if "❌" in test_result:
        print(test_result)
        return
    else:
        print("✅ قاعدة البيانات متصلة")
    
    # دوال البوت
    async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """دالة البداية"""
        if not update.effective_user or not update.message:
            return

        user_name = update.effective_user.first_name or "مستخدم"
        
        keyboard = [
            [KeyboardButton("🧪 اختبار قاعدة البيانات"), KeyboardButton("🆔 عرض المعرف")],
        ]
        
        reply_markup = ReplyKeyboardMarkup(keyboard, resize_keyboard=True)
        
        welcome_msg = f"""🌟 **Terra Bot المصحح** 🌟

👤 المستخدم: {user_name}
📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}

🔔 **الإشعارات التلقائية:** {'مفعلة' if ENABLE_NOTIFICATIONS else 'معطلة'}

اختر أحد الخيارات:"""
        
        await update.message.reply_text(welcome_msg, reply_markup=reply_markup)

    async def get_id(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """عرض المعرف"""
        if not update.effective_user or not update.message:
            return

        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        chat_type = update.effective_chat.type

        message = f"""👤 **معلومات المستخدم:**
🆔 **معرف المستخدم:** `{user_id}`
👤 **الاسم:** {update.effective_user.first_name or 'غير محدد'}

💬 **معلومات المحادثة:**
🆔 **معرف المحادثة:** `{chat_id}`
📱 **نوع المحادثة:** {chat_type}"""

        if update.effective_chat.title:
            message += f"\n🏷️ **اسم الجروب:** {update.effective_chat.title}"

        message += f"\n\n🔔 **للإشعارات:** استخدم المعرف `{chat_id if chat_type != 'private' else user_id}`"

        await update.message.reply_text(message, parse_mode='Markdown')

    async def test_db(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """اختبار قاعدة البيانات"""
        if not update.effective_user or not update.message:
            return

        await update.message.reply_text("🔄 جاري اختبار قاعدة البيانات...")
        result = test_database()
        await update.message.reply_text(result, parse_mode='Markdown')

    async def handle_text(update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة النصوص"""
        if not update.effective_user or not update.message or not update.message.text:
            return

        user_input = update.message.text.strip()

        if user_input == "🧪 اختبار قاعدة البيانات":
            await test_db(update, context)
        elif user_input == "🆔 عرض المعرف":
            await get_id(update, context)
        else:
            await update.message.reply_text("❓ أمر غير مفهوم. استخدم الأزرار أو /start")

    # إنشاء التطبيق
    app = Application.builder().token(BOT_TOKEN).build()
    
    # إضافة المعالجات
    app.add_handler(CommandHandler("start", start))
    app.add_handler(CommandHandler("test", test_db))
    app.add_handler(CommandHandler("id", get_id))
    app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_text))
    
    print("✅ البوت جاهز!")
    print("📱 أرسل /start في التليجرام")
    print("🆔 أرسل /id لعرض المعرف")
    print("🧪 أرسل /test لاختبار قاعدة البيانات")
    print("=" * 50)

    # تشغيل مراقب قاعدة البيانات
    if ENABLE_NOTIFICATIONS:
        def run_monitor():
            bot = Bot(token=BOT_TOKEN)
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(monitor_database(bot))

        monitor_thread = threading.Thread(target=run_monitor, daemon=True)
        monitor_thread.start()
        print("🔍 تم بدء مراقب قاعدة البيانات")

    # تشغيل البوت
    try:
        print("🔄 بدء تشغيل البوت...")
        app.run_polling(drop_pending_updates=True)
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البوت")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")

if __name__ == "__main__":
    main()
