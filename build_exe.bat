@echo off
chcp 65001 >nul
title Terra Bot Enhanced - Build EXE
color 0A

echo ========================================
echo    Terra Bot Enhanced - Build EXE
echo    مع إعداد التشغيل التلقائي
echo ========================================
echo.

echo [1/5] تحقق من Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ خطأ: Python غير مثبت أو غير موجود في PATH
    echo 💡 قم بتثبيت Python من: https://python.org
    pause
    exit /b 1
)
echo ✅ Python موجود

echo.
echo [2/5] تثبيت cx_Freeze...
pip install cx_Freeze
if %errorlevel% neq 0 (
    echo ❌ خطأ: فشل في تثبيت cx_Freeze
    pause
    exit /b 1
)
echo ✅ تم تثبيت cx_Freeze

echo.
echo [3/5] تثبيت المكتبات المطلوبة...
pip install python-telegram-bot pyodbc
if %errorlevel% neq 0 (
    echo ❌ خطأ: فشل في تثبيت المكتبات
    pause
    exit /b 1
)
echo ✅ تم تثبيت المكتبات

echo.
echo [4/5] بناء الملف التنفيذي...
python setup_exe.py build
if %errorlevel% neq 0 (
    echo ❌ خطأ: فشل في بناء الملف التنفيذي
    pause
    exit /b 1
)
echo ✅ تم بناء الملف التنفيذي

echo.
echo [5/5] نسخ الملفات الإضافية...

REM البحث عن مجلد build
for /d %%i in (build\exe.*) do (
    set BUILD_DIR=%%i
    goto :found_build
)
echo ❌ لم يتم العثور على مجلد build
pause
exit /b 1

:found_build
echo ✅ تم العثور على مجلد: %BUILD_DIR%

REM نسخ ملف إدارة التشغيل التلقائي
if exist startup_manager.bat (
    copy startup_manager.bat "%BUILD_DIR%\" >nul
    echo ✅ تم نسخ ملف إدارة التشغيل التلقائي
)

echo.
echo ========================================
echo 🎉 تم إنشاء الملف التنفيذي بنجاح!
echo ========================================
echo 📁 المجلد: %BUILD_DIR%
echo 📄 الملفات:
echo    - TerraBot_Enhanced.exe
echo    - startup_manager.bat
echo ========================================
echo.

echo 💡 الخيارات المتاحة:
echo 1. فتح مجلد الملف التنفيذي
echo 2. تشغيل البوت الآن
echo 3. إعداد التشغيل التلقائي
echo 4. إنهاء
echo.
set /p choice="اختيارك (1-4): "

if "%choice%"=="1" (
    echo فتح المجلد...
    explorer "%BUILD_DIR%"
) else if "%choice%"=="2" (
    echo تشغيل البوت...
    cd "%BUILD_DIR%"
    start TerraBot_Enhanced.exe
) else if "%choice%"=="3" (
    echo إعداد التشغيل التلقائي...
    cd "%BUILD_DIR%"
    start startup_manager.bat
) else (
    echo تم الإنهاء
)

echo.
echo شكراً لاستخدام Terra Bot Enhanced!
pause
