#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام الإشعارات التلقائية
"""

import asyncio
import pyodbc
from datetime import datetime
from telegram import Bot

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

# إعدادات البوت
BOT_TOKEN = "7556046481:AAEMttgLYzrMu1q2ogRlgsV8mgscqZKbbcY"
NOTIFICATION_CHAT_IDS = [
    "1107000748",
    "1206533289", 
    "-1002266485376"
]

def connect_to_db():
    """الاتصال بقاعدة البيانات"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['user']};PWD={DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def test_customer_query():
    """اختبار استعلام العملاء"""
    print("🔍 اختبار استعلام العملاء...")
    
    try:
        conn = connect_to_db()
        if not conn:
            return False
        
        cursor = conn.cursor()
        
        # اختبار الاستعلام
        query = "SELECT TOP 1 CustomerCode, NameAr, MainPhoneNo, BranchId, AddDate FROM Acc_Customers WHERE IsDeleted = 0 ORDER BY AddDate DESC"
        cursor.execute(query)
        latest_customer = cursor.fetchone()
        
        if latest_customer:
            print("✅ تم العثور على آخر عميل:")
            print(f"   الكود: {latest_customer[0]}")
            print(f"   الاسم: {latest_customer[1] or 'غير محدد'}")
            print(f"   الهاتف: {latest_customer[2] or 'غير محدد'}")
            print(f"   الفرع: {latest_customer[3]}")
            print(f"   التاريخ: {latest_customer[4]}")
            
            conn.close()
            return latest_customer
        else:
            print("❌ لم يتم العثور على أي عملاء")
            conn.close()
            return None
            
    except Exception as e:
        print(f"❌ خطأ في الاستعلام: {str(e)}")
        return None

async def test_send_notification():
    """اختبار إرسال الإشعارات"""
    print("\n📱 اختبار إرسال الإشعارات...")
    
    try:
        bot = Bot(token=BOT_TOKEN)
        
        # رسالة اختبار
        test_message = """🧪 **رسالة اختبار من Terra Bot**

هذه رسالة اختبار للتأكد من عمل نظام الإشعارات التلقائية.

⏰ الوقت: """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        
        success_count = 0
        for chat_id in NOTIFICATION_CHAT_IDS:
            try:
                await bot.send_message(
                    chat_id=chat_id,
                    text=test_message,
                    parse_mode='Markdown'
                )
                print(f"✅ تم إرسال رسالة اختبار إلى: {chat_id}")
                success_count += 1
                
            except Exception as e:
                print(f"❌ فشل إرسال رسالة إلى {chat_id}: {str(e)}")
        
        print(f"\n📊 النتيجة: تم إرسال {success_count}/{len(NOTIFICATION_CHAT_IDS)} رسائل بنجاح")
        return success_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في اختبار الإرسال: {str(e)}")
        return False

async def test_customer_notification():
    """اختبار إشعار العميل الأخير"""
    print("\n🔔 اختبار إشعار العميل الأخير...")
    
    # الحصول على آخر عميل
    customer = test_customer_query()
    if not customer:
        print("❌ لا يمكن اختبار الإشعار بدون عميل")
        return False
    
    # تحضير رسالة الإشعار
    branch_name = "فرع التجمع" if customer[3] == 3 else "فرع مدينة نصر" if customer[3] == 2 else "غير محدد"
    
    notification_message = f"""👤 **عميل جديد تم إضافته**

🔢 **الكود:** {customer[0]}
👤 **الاسم:** {customer[1] or 'غير محدد'}
📱 **الهاتف:** {customer[2] or 'غير محدد'}
🏢 **الفرع:** {branch_name}
📅 **تاريخ الإضافة:** {customer[4].strftime('%Y-%m-%d %H:%M') if customer[4] else 'غير محدد'}"""
    
    # إرسال الإشعار
    try:
        bot = Bot(token=BOT_TOKEN)
        
        notification_text = f"🔔 **إشعار تلقائي من Terra Bot**\n\n{notification_message}\n\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        success_count = 0
        for chat_id in NOTIFICATION_CHAT_IDS:
            try:
                await bot.send_message(
                    chat_id=chat_id,
                    text=notification_text,
                    parse_mode='Markdown'
                )
                print(f"✅ تم إرسال إشعار العميل إلى: {chat_id}")
                success_count += 1
                
            except Exception as e:
                print(f"❌ فشل إرسال إشعار إلى {chat_id}: {str(e)}")
        
        print(f"\n📊 النتيجة: تم إرسال {success_count}/{len(NOTIFICATION_CHAT_IDS)} إشعارات بنجاح")
        return success_count > 0
        
    except Exception as e:
        print(f"❌ خطأ في إرسال إشعار العميل: {str(e)}")
        return False

def check_table_structure():
    """فحص بنية جدول العملاء"""
    print("\n🔍 فحص بنية جدول العملاء...")
    
    try:
        conn = connect_to_db()
        if not conn:
            return False
        
        cursor = conn.cursor()
        
        # فحص أعمدة الجدول
        cursor.execute("""
            SELECT COLUMN_NAME, DATA_TYPE
            FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_NAME = 'Acc_Customers'
            ORDER BY ORDINAL_POSITION
        """)
        
        columns = cursor.fetchall()
        
        if columns:
            print("✅ أعمدة جدول Acc_Customers:")
            for col in columns:
                print(f"   • {col[0]} ({col[1]})")
        else:
            print("❌ لم يتم العثور على جدول Acc_Customers")
        
        conn.close()
        return len(columns) > 0
        
    except Exception as e:
        print(f"❌ خطأ في فحص بنية الجدول: {str(e)}")
        return False

async def main():
    """الدالة الرئيسية"""
    print("🧪 اختبار نظام الإشعارات التلقائية")
    print("=" * 50)
    
    # 1. فحص بنية الجدول
    check_table_structure()
    
    # 2. اختبار استعلام العملاء
    test_customer_query()
    
    # 3. اختبار إرسال رسالة عامة
    await test_send_notification()
    
    # 4. اختبار إشعار العميل
    await test_customer_notification()
    
    print("\n" + "=" * 50)
    print("✅ انتهى الاختبار")

if __name__ == "__main__":
    asyncio.run(main())
