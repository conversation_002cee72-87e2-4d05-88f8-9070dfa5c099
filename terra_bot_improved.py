#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Terra Bot - نسخة محسنة
بوت تليجرام لإدارة قاعدة بيانات Terra
"""

import pyodbc
import logging
import asyncio
from datetime import datetime
from telegram import Update, ReplyKeyboardMarkup, KeyboardButton
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes

# إعداد التسجيل
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO,
    handlers=[
        logging.FileHandler('terra_bot.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# إعدادات البوت
BOT_TOKEN = "7664606990:AAFbBWGShtg00af-qkbCm9VRpvqq7M--bU0"

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

# متغيرات عامة
user_states = {}

# قائمة المستخدمين المخولين لتلقي الإشعارات
NOTIFICATION_USERS = []  # سيتم ملؤها تلقائياً عند استخدام البوت

def connect_to_db():
    """الاتصال بقاعدة البيانات"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['user']};PWD={DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        return conn
    except Exception as e:
        logger.error(f"خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def test_database():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات"
        
        cursor = conn.cursor()
        cursor.execute("SELECT @@VERSION")
        version = cursor.fetchone()
        
        cursor.execute("SELECT DB_NAME()")
        db_name = cursor.fetchone()
        
        conn.close()
        
        report = f"""✅ **اختبار قاعدة البيانات نجح**

🔗 **معلومات الاتصال:**
• الخادم: {DB_CONFIG['server']}
• قاعدة البيانات: {db_name[0] if db_name else 'غير محدد'}
• المستخدم: {DB_CONFIG['user']}

💻 **إصدار SQL Server:**
{version[0][:100] if version else 'غير محدد'}...

⏰ **وقت الاختبار:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🔙 للعودة للقائمة الرئيسية اكتب: /start"""
        
        return report
        
    except Exception as e:
        return f"❌ خطأ في فحص قاعدة البيانات: {str(e)}\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

async def send_notification(application, message):
    """إرسال إشعار لجميع المستخدمين المخولين"""
    if not NOTIFICATION_USERS:
        return
    
    notification_text = f"🔔 **إشعار تلقائي من Terra Bot**\n\n{message}\n\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    
    for user_id in NOTIFICATION_USERS:
        try:
            await application.bot.send_message(
                chat_id=user_id,
                text=notification_text,
                parse_mode='Markdown'
            )
            logger.info(f"تم إرسال إشعار للمستخدم: {user_id}")
        except Exception as e:
            logger.error(f"فشل إرسال إشعار للمستخدم {user_id}: {str(e)}")

def search_customer_in_db(search_term):
    """البحث عن عميل في قاعدة البيانات"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"
        
        cursor = conn.cursor()
        
        # البحث في جدول العملاء
        query = """
        SELECT TOP 1
            CustomerCode, CustomerName, PrimaryPhone, SecondaryPhone,
            CustomerAddress, BranchID, CreatedDate
        FROM Acc_Customers
        WHERE CustomerCode = ? OR PrimaryPhone = ? OR SecondaryPhone = ?
        """
        
        cursor.execute(query, (search_term, search_term, search_term))
        customer = cursor.fetchone()
        
        if not customer:
            conn.close()
            return f"❌ لم يتم العثور على عميل بالبيانات: {search_term}\n\n🔍 للبحث مرة أخرى أدخل كود العميل أو رقم الهاتف\n🔙 للعودة للقائمة الرئيسية اكتب: /start"
        
        # تحضير بيانات العميل
        branch_name = "فرع التجمع" if customer[5] == 1 else "فرع مدينة نصر" if customer[5] == 2 else "غير محدد"
        
        result = f"""📋 **بيانات العميل**

👤 **الاسم:** {customer[1] or 'غير محدد'}
🔢 **الكود:** {customer[0] or 'غير محدد'}
📱 **الهاتف الأساسي:** {customer[2] or 'غير محدد'}
📞 **الهاتف الفرعي:** {customer[3] or 'غير محدد'}
🏠 **العنوان:** {customer[4] or 'غير محدد'}
🏢 **الفرع:** {branch_name}
📅 **تاريخ الإضافة:** {customer[6].strftime('%Y-%m-%d') if customer[6] else 'غير محدد'}

🔍 للبحث عن عميل آخر أدخل كود العميل أو رقم الهاتف
🔙 للعودة للقائمة الرئيسية اكتب: /start"""
        
        conn.close()
        return result
        
    except Exception as e:
        logger.error(f"خطأ في البحث عن العميل: {str(e)}")
        return f"❌ حدث خطأ أثناء البحث: {str(e)}\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

def get_customers_list(branch_id, days):
    """الحصول على قائمة العملاء"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"
        
        cursor = conn.cursor()
        
        # بناء الاستعلام
        if days is None:
            # للكل
            if branch_id == '3':
                query = "SELECT CustomerCode, CustomerName, PrimaryPhone, BranchID, CreatedDate FROM Acc_Customers ORDER BY CreatedDate DESC"
                cursor.execute(query)
            else:
                query = "SELECT CustomerCode, CustomerName, PrimaryPhone, BranchID, CreatedDate FROM Acc_Customers WHERE BranchID = ? ORDER BY CreatedDate DESC"
                cursor.execute(query, (int(branch_id),))
        else:
            # لفترة محددة
            if branch_id == '3':
                query = "SELECT CustomerCode, CustomerName, PrimaryPhone, BranchID, CreatedDate FROM Acc_Customers WHERE CreatedDate >= DATEADD(day, -?, GETDATE()) ORDER BY CreatedDate DESC"
                cursor.execute(query, (days,))
            else:
                query = "SELECT CustomerCode, CustomerName, PrimaryPhone, BranchID, CreatedDate FROM Acc_Customers WHERE BranchID = ? AND CreatedDate >= DATEADD(day, -?, GETDATE()) ORDER BY CreatedDate DESC"
                cursor.execute(query, (int(branch_id), days))
        
        customers = cursor.fetchall()
        conn.close()
        
        if not customers:
            return "❌ لا توجد بيانات للفترة المحددة\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"
        
        # تحضير التقرير
        branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'جميع الفروع'}
        period_text = f"آخر {days} أيام" if days else "جميع الفترات"
        
        result = f"""📊 **تقرير العملاء**

🏢 **الفرع:** {branch_names.get(branch_id, 'غير محدد')}
📅 **الفترة:** {period_text}
👥 **العدد الإجمالي:** {len(customers)}

📋 **قائمة العملاء:**
"""
        
        for i, customer in enumerate(customers[:50], 1):  # أول 50 عميل
            branch_name = "التجمع" if customer[3] == 1 else "مدينة نصر" if customer[3] == 2 else "غير محدد"
            date_str = customer[4].strftime('%Y-%m-%d') if customer[4] else 'غير محدد'
            
            result += f"{i}. {customer[1] or 'غير محدد'} | {customer[0] or 'غير محدد'} | {customer[2] or 'غير محدد'} | {branch_name} | {date_str}\n"
        
        if len(customers) > 50:
            result += f"\n... و {len(customers) - 50} عميل آخر"
        
        result += "\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"
        
        return result
        
    except Exception as e:
        logger.error(f"خطأ في الحصول على قائمة العملاء: {str(e)}")
        return f"❌ حدث خطأ أثناء تحضير التقرير: {str(e)}\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

def get_previews_count_by_regions(branch_id, days):
    """الحصول على أعداد المعاينات حسب المناطق"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

        cursor = conn.cursor()

        # بناء الاستعلام
        if days is None:
            if branch_id == '3':
                query = """
                SELECT CustomerRegion, COUNT(*) as Count
                FROM Sys_Previews p
                INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
                GROUP BY CustomerRegion
                ORDER BY Count DESC
                """
                cursor.execute(query)
            else:
                query = """
                SELECT CustomerRegion, COUNT(*) as Count
                FROM Sys_Previews p
                INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
                WHERE c.BranchID = ?
                GROUP BY CustomerRegion
                ORDER BY Count DESC
                """
                cursor.execute(query, (int(branch_id),))
        else:
            if branch_id == '3':
                query = """
                SELECT CustomerRegion, COUNT(*) as Count
                FROM Sys_Previews p
                INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
                WHERE p.PreviewDate >= DATEADD(day, -?, GETDATE())
                GROUP BY CustomerRegion
                ORDER BY Count DESC
                """
                cursor.execute(query, (days,))
            else:
                query = """
                SELECT CustomerRegion, COUNT(*) as Count
                FROM Sys_Previews p
                INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
                WHERE c.BranchID = ? AND p.PreviewDate >= DATEADD(day, -?, GETDATE())
                GROUP BY CustomerRegion
                ORDER BY Count DESC
                """
                cursor.execute(query, (int(branch_id), days))

        regions = cursor.fetchall()
        conn.close()

        if not regions:
            return "❌ لا توجد بيانات للفترة المحددة\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

        # تحضير التقرير
        branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'جميع الفروع'}
        period_text = f"آخر {days} أيام" if days else "جميع الفترات"

        result = f"""🌍 **تقرير المعاينات حسب المناطق**

🏢 **الفرع:** {branch_names.get(branch_id, 'غير محدد')}
📅 **الفترة:** {period_text}

📊 **الإحصائيات:**
"""

        total_count = sum(region[1] for region in regions)

        for region in regions:
            region_name = region[0] or 'غير محدد'
            count = region[1]
            percentage = (count / total_count * 100) if total_count > 0 else 0
            result += f"• {region_name}: {count} معاينة ({percentage:.1f}%)\n"

        result += f"\n📈 **المجموع الكلي:** {total_count} معاينة"
        result += "\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

        return result

    except Exception as e:
        logger.error(f"خطأ في الحصول على إحصائيات المناطق: {str(e)}")
        return f"❌ حدث خطأ أثناء تحضير التقرير: {str(e)}\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

def get_previews_count_by_social_media(branch_id, days):
    """الحصول على أعداد المعاينات حسب وسائل التواصل"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

        cursor = conn.cursor()

        # بناء الاستعلام
        if days is None:
            if branch_id == '3':
                query = """
                SELECT SocialMediaSource, COUNT(*) as Count
                FROM Sys_Previews p
                INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
                GROUP BY SocialMediaSource
                ORDER BY Count DESC
                """
                cursor.execute(query)
            else:
                query = """
                SELECT SocialMediaSource, COUNT(*) as Count
                FROM Sys_Previews p
                INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
                WHERE c.BranchID = ?
                GROUP BY SocialMediaSource
                ORDER BY Count DESC
                """
                cursor.execute(query, (int(branch_id),))
        else:
            if branch_id == '3':
                query = """
                SELECT SocialMediaSource, COUNT(*) as Count
                FROM Sys_Previews p
                INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
                WHERE p.PreviewDate >= DATEADD(day, -?, GETDATE())
                GROUP BY SocialMediaSource
                ORDER BY Count DESC
                """
                cursor.execute(query, (days,))
            else:
                query = """
                SELECT SocialMediaSource, COUNT(*) as Count
                FROM Sys_Previews p
                INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
                WHERE c.BranchID = ? AND p.PreviewDate >= DATEADD(day, -?, GETDATE())
                GROUP BY SocialMediaSource
                ORDER BY Count DESC
                """
                cursor.execute(query, (int(branch_id), days))

        social_media = cursor.fetchall()
        conn.close()

        if not social_media:
            return "❌ لا توجد بيانات للفترة المحددة\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

        # تحضير التقرير
        branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'جميع الفروع'}
        period_text = f"آخر {days} أيام" if days else "جميع الفترات"

        result = f"""📱 **تقرير المعاينات حسب وسائل التواصل**

🏢 **الفرع:** {branch_names.get(branch_id, 'غير محدد')}
📅 **الفترة:** {period_text}

📊 **الإحصائيات:**
"""

        total_count = sum(media[1] for media in social_media)

        for media in social_media:
            media_name = media[0] or 'غير محدد'
            count = media[1]
            percentage = (count / total_count * 100) if total_count > 0 else 0
            result += f"• {media_name}: {count} معاينة ({percentage:.1f}%)\n"

        result += f"\n📈 **المجموع الكلي:** {total_count} معاينة"
        result += "\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

        return result

    except Exception as e:
        logger.error(f"خطأ في الحصول على إحصائيات وسائل التواصل: {str(e)}")
        return f"❌ حدث خطأ أثناء تحضير التقرير: {str(e)}\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

def get_customers_without_previews(branch_id, days):
    """الحصول على العملاء الذين لا يوجد لهم معاينات"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

        cursor = conn.cursor()

        # بناء الاستعلام
        if days is None:
            if branch_id == '3':
                query = """
                SELECT c.CustomerCode, c.CustomerName, c.PrimaryPhone, c.BranchID, c.CreatedDate
                FROM Acc_Customers c
                LEFT JOIN Sys_Previews p ON c.CustomerCode = p.CustomerCode
                WHERE p.CustomerCode IS NULL
                ORDER BY c.CreatedDate DESC
                """
                cursor.execute(query)
            else:
                query = """
                SELECT c.CustomerCode, c.CustomerName, c.PrimaryPhone, c.BranchID, c.CreatedDate
                FROM Acc_Customers c
                LEFT JOIN Sys_Previews p ON c.CustomerCode = p.CustomerCode
                WHERE c.BranchID = ? AND p.CustomerCode IS NULL
                ORDER BY c.CreatedDate DESC
                """
                cursor.execute(query, (int(branch_id),))
        else:
            if branch_id == '3':
                query = """
                SELECT c.CustomerCode, c.CustomerName, c.PrimaryPhone, c.BranchID, c.CreatedDate
                FROM Acc_Customers c
                LEFT JOIN Sys_Previews p ON c.CustomerCode = p.CustomerCode
                WHERE c.CreatedDate >= DATEADD(day, -?, GETDATE()) AND p.CustomerCode IS NULL
                ORDER BY c.CreatedDate DESC
                """
                cursor.execute(query, (days,))
            else:
                query = """
                SELECT c.CustomerCode, c.CustomerName, c.PrimaryPhone, c.BranchID, c.CreatedDate
                FROM Acc_Customers c
                LEFT JOIN Sys_Previews p ON c.CustomerCode = p.CustomerCode
                WHERE c.BranchID = ? AND c.CreatedDate >= DATEADD(day, -?, GETDATE()) AND p.CustomerCode IS NULL
                ORDER BY c.CreatedDate DESC
                """
                cursor.execute(query, (int(branch_id), days))

        customers = cursor.fetchall()
        conn.close()

        if not customers:
            return "✅ جميع العملاء لديهم معاينات في الفترة المحددة\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

        # تحضير التقرير
        branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'جميع الفروع'}
        period_text = f"آخر {days} أيام" if days else "جميع الفترات"

        result = f"""📊 **العملاء بدون معاينات**

🏢 **الفرع:** {branch_names.get(branch_id, 'غير محدد')}
📅 **الفترة:** {period_text}
👥 **العدد:** {len(customers)}

📋 **قائمة العملاء:**
"""

        for i, customer in enumerate(customers[:30], 1):  # أول 30 عميل
            branch_name = "التجمع" if customer[3] == 1 else "مدينة نصر" if customer[3] == 2 else "غير محدد"
            date_str = customer[4].strftime('%Y-%m-%d') if customer[4] else 'غير محدد'

            result += f"{i}. {customer[1] or 'غير محدد'} | {customer[0] or 'غير محدد'} | {customer[2] or 'غير محدد'} | {branch_name} | {date_str}\n"

        if len(customers) > 30:
            result += f"\n... و {len(customers) - 30} عميل آخر"

        result += "\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

        return result

    except Exception as e:
        logger.error(f"خطأ في الحصول على العملاء بدون معاينات: {str(e)}")
        return f"❌ حدث خطأ أثناء تحضير التقرير: {str(e)}\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

def get_previews_without_meetings(branch_id, days):
    """الحصول على المعاينات بدون اجتماعات"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

        cursor = conn.cursor()

        # بناء الاستعلام
        if days is None:
            if branch_id == '3':
                query = """
                SELECT p.PreviewCode, c.CustomerName, c.PrimaryPhone, c.BranchID, p.PreviewDate
                FROM Sys_Previews p
                INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
                LEFT JOIN Sys_Meetings m ON p.PreviewCode = m.PreviewCode
                WHERE m.PreviewCode IS NULL
                ORDER BY p.PreviewDate DESC
                """
                cursor.execute(query)
            else:
                query = """
                SELECT p.PreviewCode, c.CustomerName, c.PrimaryPhone, c.BranchID, p.PreviewDate
                FROM Sys_Previews p
                INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
                LEFT JOIN Sys_Meetings m ON p.PreviewCode = m.PreviewCode
                WHERE c.BranchID = ? AND m.PreviewCode IS NULL
                ORDER BY p.PreviewDate DESC
                """
                cursor.execute(query, (int(branch_id),))
        else:
            if branch_id == '3':
                query = """
                SELECT p.PreviewCode, c.CustomerName, c.PrimaryPhone, c.BranchID, p.PreviewDate
                FROM Sys_Previews p
                INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
                LEFT JOIN Sys_Meetings m ON p.PreviewCode = m.PreviewCode
                WHERE p.PreviewDate >= DATEADD(day, -?, GETDATE()) AND m.PreviewCode IS NULL
                ORDER BY p.PreviewDate DESC
                """
                cursor.execute(query, (days,))
            else:
                query = """
                SELECT p.PreviewCode, c.CustomerName, c.PrimaryPhone, c.BranchID, p.PreviewDate
                FROM Sys_Previews p
                INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
                LEFT JOIN Sys_Meetings m ON p.PreviewCode = m.PreviewCode
                WHERE c.BranchID = ? AND p.PreviewDate >= DATEADD(day, -?, GETDATE()) AND m.PreviewCode IS NULL
                ORDER BY p.PreviewDate DESC
                """
                cursor.execute(query, (int(branch_id), days))

        previews = cursor.fetchall()
        conn.close()

        if not previews:
            return "✅ جميع المعاينات لديها اجتماعات في الفترة المحددة\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

        # تحضير التقرير
        branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'جميع الفروع'}
        period_text = f"آخر {days} أيام" if days else "جميع الفترات"

        result = f"""👁️ **المعاينات بدون اجتماعات**

🏢 **الفرع:** {branch_names.get(branch_id, 'غير محدد')}
📅 **الفترة:** {period_text}
📊 **العدد:** {len(previews)}

📋 **قائمة المعاينات:**
"""

        for i, preview in enumerate(previews[:30], 1):  # أول 30 معاينة
            branch_name = "التجمع" if preview[3] == 1 else "مدينة نصر" if preview[3] == 2 else "غير محدد"
            date_str = preview[4].strftime('%Y-%m-%d') if preview[4] else 'غير محدد'

            result += f"{i}. {preview[1] or 'غير محدد'} | {preview[0] or 'غير محدد'} | {preview[2] or 'غير محدد'} | {branch_name} | {date_str}\n"

        if len(previews) > 30:
            result += f"\n... و {len(previews) - 30} معاينة أخرى"

        result += "\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

        return result

    except Exception as e:
        logger.error(f"خطأ في الحصول على المعاينات بدون اجتماعات: {str(e)}")
        return f"❌ حدث خطأ أثناء تحضير التقرير: {str(e)}\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

def get_meetings_without_designs(branch_id, days):
    """الحصول على الاجتماعات بدون تصميمات"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

        cursor = conn.cursor()

        # بناء الاستعلام
        if days is None:
            if branch_id == '3':
                query = """
                SELECT m.MeetingCode, c.CustomerName, c.PrimaryPhone, c.BranchID, m.MeetingDate
                FROM Sys_Meetings m
                INNER JOIN Sys_Previews p ON m.PreviewCode = p.PreviewCode
                INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
                LEFT JOIN Sys_Designs d ON m.MeetingCode = d.MeetingCode
                WHERE d.MeetingCode IS NULL
                ORDER BY m.MeetingDate DESC
                """
                cursor.execute(query)
            else:
                query = """
                SELECT m.MeetingCode, c.CustomerName, c.PrimaryPhone, c.BranchID, m.MeetingDate
                FROM Sys_Meetings m
                INNER JOIN Sys_Previews p ON m.PreviewCode = p.PreviewCode
                INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
                LEFT JOIN Sys_Designs d ON m.MeetingCode = d.MeetingCode
                WHERE c.BranchID = ? AND d.MeetingCode IS NULL
                ORDER BY m.MeetingDate DESC
                """
                cursor.execute(query, (int(branch_id),))
        else:
            if branch_id == '3':
                query = """
                SELECT m.MeetingCode, c.CustomerName, c.PrimaryPhone, c.BranchID, m.MeetingDate
                FROM Sys_Meetings m
                INNER JOIN Sys_Previews p ON m.PreviewCode = p.PreviewCode
                INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
                LEFT JOIN Sys_Designs d ON m.MeetingCode = d.MeetingCode
                WHERE m.MeetingDate >= DATEADD(day, -?, GETDATE()) AND d.MeetingCode IS NULL
                ORDER BY m.MeetingDate DESC
                """
                cursor.execute(query, (days,))
            else:
                query = """
                SELECT m.MeetingCode, c.CustomerName, c.PrimaryPhone, c.BranchID, m.MeetingDate
                FROM Sys_Meetings m
                INNER JOIN Sys_Previews p ON m.PreviewCode = p.PreviewCode
                INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
                LEFT JOIN Sys_Designs d ON m.MeetingCode = d.MeetingCode
                WHERE c.BranchID = ? AND m.MeetingDate >= DATEADD(day, -?, GETDATE()) AND d.MeetingCode IS NULL
                ORDER BY m.MeetingDate DESC
                """
                cursor.execute(query, (int(branch_id), days))

        meetings = cursor.fetchall()
        conn.close()

        if not meetings:
            return "✅ جميع الاجتماعات لديها تصميمات في الفترة المحددة\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

        # تحضير التقرير
        branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'جميع الفروع'}
        period_text = f"آخر {days} أيام" if days else "جميع الفترات"

        result = f"""🤝 **الاجتماعات بدون تصميمات**

🏢 **الفرع:** {branch_names.get(branch_id, 'غير محدد')}
📅 **الفترة:** {period_text}
📊 **العدد:** {len(meetings)}

📋 **قائمة الاجتماعات:**
"""

        for i, meeting in enumerate(meetings[:30], 1):  # أول 30 اجتماع
            branch_name = "التجمع" if meeting[3] == 1 else "مدينة نصر" if meeting[3] == 2 else "غير محدد"
            date_str = meeting[4].strftime('%Y-%m-%d') if meeting[4] else 'غير محدد'

            result += f"{i}. {meeting[1] or 'غير محدد'} | {meeting[0] or 'غير محدد'} | {meeting[2] or 'غير محدد'} | {branch_name} | {date_str}\n"

        if len(meetings) > 30:
            result += f"\n... و {len(meetings) - 30} اجتماع آخر"

        result += "\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

        return result

    except Exception as e:
        logger.error(f"خطأ في الحصول على الاجتماعات بدون تصميمات: {str(e)}")
        return f"❌ حدث خطأ أثناء تحضير التقرير: {str(e)}\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

def get_designs_without_contracts(branch_id, days):
    """الحصول على التصميمات بدون عقود"""
    try:
        conn = connect_to_db()
        if not conn:
            return "❌ تعذر الاتصال بقاعدة البيانات\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

        cursor = conn.cursor()

        # بناء الاستعلام
        if days is None:
            if branch_id == '3':
                query = """
                SELECT d.DesignCode, c.CustomerName, c.PrimaryPhone, c.BranchID, d.DesignDate
                FROM Sys_Designs d
                INNER JOIN Sys_Meetings m ON d.MeetingCode = m.MeetingCode
                INNER JOIN Sys_Previews p ON m.PreviewCode = p.PreviewCode
                INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
                LEFT JOIN Acc_Contracts ct ON d.DesignCode = ct.DesignCode
                WHERE ct.DesignCode IS NULL
                ORDER BY d.DesignDate DESC
                """
                cursor.execute(query)
            else:
                query = """
                SELECT d.DesignCode, c.CustomerName, c.PrimaryPhone, c.BranchID, d.DesignDate
                FROM Sys_Designs d
                INNER JOIN Sys_Meetings m ON d.MeetingCode = m.MeetingCode
                INNER JOIN Sys_Previews p ON m.PreviewCode = p.PreviewCode
                INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
                LEFT JOIN Acc_Contracts ct ON d.DesignCode = ct.DesignCode
                WHERE c.BranchID = ? AND ct.DesignCode IS NULL
                ORDER BY d.DesignDate DESC
                """
                cursor.execute(query, (int(branch_id),))
        else:
            if branch_id == '3':
                query = """
                SELECT d.DesignCode, c.CustomerName, c.PrimaryPhone, c.BranchID, d.DesignDate
                FROM Sys_Designs d
                INNER JOIN Sys_Meetings m ON d.MeetingCode = m.MeetingCode
                INNER JOIN Sys_Previews p ON m.PreviewCode = p.PreviewCode
                INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
                LEFT JOIN Acc_Contracts ct ON d.DesignCode = ct.DesignCode
                WHERE d.DesignDate >= DATEADD(day, -?, GETDATE()) AND ct.DesignCode IS NULL
                ORDER BY d.DesignDate DESC
                """
                cursor.execute(query, (days,))
            else:
                query = """
                SELECT d.DesignCode, c.CustomerName, c.PrimaryPhone, c.BranchID, d.DesignDate
                FROM Sys_Designs d
                INNER JOIN Sys_Meetings m ON d.MeetingCode = m.MeetingCode
                INNER JOIN Sys_Previews p ON m.PreviewCode = p.PreviewCode
                INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
                LEFT JOIN Acc_Contracts ct ON d.DesignCode = ct.DesignCode
                WHERE c.BranchID = ? AND d.DesignDate >= DATEADD(day, -?, GETDATE()) AND ct.DesignCode IS NULL
                ORDER BY d.DesignDate DESC
                """
                cursor.execute(query, (int(branch_id), days))

        designs = cursor.fetchall()
        conn.close()

        if not designs:
            return "✅ جميع التصميمات لديها عقود في الفترة المحددة\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

        # تحضير التقرير
        branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'جميع الفروع'}
        period_text = f"آخر {days} أيام" if days else "جميع الفترات"

        result = f"""🎨 **التصميمات بدون عقود**

🏢 **الفرع:** {branch_names.get(branch_id, 'غير محدد')}
📅 **الفترة:** {period_text}
📊 **العدد:** {len(designs)}

📋 **قائمة التصميمات:**
"""

        for i, design in enumerate(designs[:30], 1):  # أول 30 تصميم
            branch_name = "التجمع" if design[3] == 1 else "مدينة نصر" if design[3] == 2 else "غير محدد"
            date_str = design[4].strftime('%Y-%m-%d') if design[4] else 'غير محدد'

            result += f"{i}. {design[1] or 'غير محدد'} | {design[0] or 'غير محدد'} | {design[2] or 'غير محدد'} | {branch_name} | {date_str}\n"

        if len(designs) > 30:
            result += f"\n... و {len(designs) - 30} تصميم آخر"

        result += "\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

        return result

    except Exception as e:
        logger.error(f"خطأ في الحصول على التصميمات بدون عقود: {str(e)}")
        return f"❌ حدث خطأ أثناء تحضير التقرير: {str(e)}\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start"

# دوال البوت
async def start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """دالة البداية"""
    if not update.effective_user or not update.message:
        return

    user_id = update.effective_user.id
    user_name = update.effective_user.first_name or "مستخدم"

    # إضافة المستخدم لقائمة الإشعارات إذا لم يكن موجوداً
    if user_id not in NOTIFICATION_USERS:
        NOTIFICATION_USERS.append(user_id)
        logger.info(f"تم إضافة مستخدم جديد لقائمة الإشعارات: {user_id}")

    # إعادة تعيين حالة المستخدم
    user_states[user_id] = {'waiting_for': None}

    keyboard = [
        [KeyboardButton("🔍 بحث عميل"), KeyboardButton("📊 استعلام عن العملاء")],
        [KeyboardButton("🌍 عدد العملاء حسب المناطق"), KeyboardButton("📱 عدد عملاء وسائل التواصل")],
        [KeyboardButton("📊 عملاء بدون معاينات"), KeyboardButton("👁️ معاينات بدون اجتماعات")],
        [KeyboardButton("🤝 اجتماعات بدون تصميمات"), KeyboardButton("🎨 تصميمات بدون عقود")],
        [KeyboardButton("❌ إنهاء المحادثة")],
    ]

    reply_markup = ReplyKeyboardMarkup(keyboard, one_time_keyboard=False, resize_keyboard=True)

    welcome_msg = f"""🌟 **Terra Bot - النسخة المحسنة** 🌟

👤 **المستخدم:** {user_name}
📅 **التاريخ:** {datetime.now().strftime('%Y-%m-%d %H:%M')}
🚀 **الإصدار:** محسن ومطور

🔔 **ميزات جديدة:**
• إشعارات تلقائية للعمليات الجديدة
• واجهة محسنة بدون أزرار إضافية
• أوامر سريعة في النص

اختر أحد الخيارات التالية:"""

    await update.message.reply_text(welcome_msg, reply_markup=reply_markup, parse_mode='Markdown')
    logger.info(f"بدء جلسة جديدة للمستخدم: {user_name} ({user_id})")

async def test_db_command(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """اختبار قاعدة البيانات"""
    if not update.effective_user or not update.message:
        return

    user_name = update.effective_user.first_name or "مستخدم"
    logger.info(f"طلب اختبار قاعدة البيانات من: {user_name}")

    await update.message.reply_text("🔄 جاري اختبار قاعدة البيانات...")

    result = test_database()
    await update.message.reply_text(result, parse_mode='Markdown')

async def search_customer_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """بداية البحث عن عميل"""
    if not update.effective_user or not update.message:
        return

    user_id = update.effective_user.id
    user_name = update.effective_user.first_name or "مستخدم"
    logger.info(f"طلب بحث عميل من: {user_name}")

    user_states[user_id] = {'waiting_for': 'customer_search'}

    await update.message.reply_text(
        "🔍 **بحث عن عميل**\n\n"
        "من فضلك أدخل:\n"
        "• كود العميل\n"
        "• رقم الهاتف الأساسي\n"
        "• رقم الهاتف الفرعي\n\n"
        "مثال: 12345 أو 01012345678\n\n"
        "💡 للإلغاء اكتب: /start",
        parse_mode='Markdown'
    )

async def customers_inquiry_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """بداية استعلام العملاء"""
    if not update.effective_user or not update.message:
        return

    user_id = update.effective_user.id
    user_name = update.effective_user.first_name or "مستخدم"
    logger.info(f"طلب استعلام عملاء من: {user_name}")

    user_states[user_id] = {'waiting_for': 'branch_selection_customers'}

    await update.message.reply_text(
        "📍 **من فضلك اختر الفرع:**\n\n"
        "1 - فرع التجمع\n"
        "2 - فرع مدينة نصر\n"
        "3 - للكل\n\n"
        "💡 للإلغاء اكتب: /start",
        parse_mode='Markdown'
    )

async def regions_inquiry_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """بداية استعلام العملاء حسب المناطق"""
    if not update.effective_user or not update.message:
        return

    user_id = update.effective_user.id
    user_name = update.effective_user.first_name or "مستخدم"
    logger.info(f"طلب استعلام المناطق من: {user_name}")

    user_states[user_id] = {'waiting_for': 'branch_selection_regions'}

    await update.message.reply_text(
        "📍 **من فضلك اختر الفرع:**\n\n"
        "1 - فرع التجمع\n"
        "2 - فرع مدينة نصر\n"
        "3 - للكل\n\n"
        "💡 للإلغاء اكتب: /start",
        parse_mode='Markdown'
    )

async def social_media_inquiry_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """بداية استعلام العملاء حسب وسائل التواصل"""
    if not update.effective_user or not update.message:
        return

    user_id = update.effective_user.id
    user_name = update.effective_user.first_name or "مستخدم"
    logger.info(f"طلب استعلام وسائل التواصل من: {user_name}")

    user_states[user_id] = {'waiting_for': 'branch_selection_social_media'}

    await update.message.reply_text(
        "📍 **من فضلك اختر الفرع:**\n\n"
        "1 - فرع التجمع\n"
        "2 - فرع مدينة نصر\n"
        "3 - للكل\n\n"
        "💡 للإلغاء اكتب: /start",
        parse_mode='Markdown'
    )

async def customers_without_previews_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """بداية استعلام العملاء بدون معاينات"""
    if not update.effective_user or not update.message:
        return

    user_id = update.effective_user.id
    user_name = update.effective_user.first_name or "مستخدم"
    logger.info(f"طلب استعلام العملاء بدون معاينات من: {user_name}")

    user_states[user_id] = {'waiting_for': 'branch_selection_no_previews'}

    await update.message.reply_text(
        "📍 **من فضلك اختر الفرع:**\n\n"
        "1 - فرع التجمع\n"
        "2 - فرع مدينة نصر\n"
        "3 - للكل\n\n"
        "💡 للإلغاء اكتب: /start",
        parse_mode='Markdown'
    )

async def previews_without_meetings_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """بداية استعلام معاينات بدون اجتماعات"""
    if not update.effective_user or not update.message:
        return

    user_id = update.effective_user.id
    user_name = update.effective_user.first_name or "مستخدم"
    logger.info(f"طلب استعلام معاينات بدون اجتماعات من: {user_name}")

    user_states[user_id] = {'waiting_for': 'branch_selection_previews_no_meetings'}

    await update.message.reply_text(
        "📍 **من فضلك اختر الفرع:**\n\n"
        "1 - فرع التجمع\n"
        "2 - فرع مدينة نصر\n"
        "3 - للكل\n\n"
        "💡 للإلغاء اكتب: /start",
        parse_mode='Markdown'
    )

async def meetings_without_designs_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """بداية استعلام اجتماعات بدون تصميمات"""
    if not update.effective_user or not update.message:
        return

    user_id = update.effective_user.id
    user_name = update.effective_user.first_name or "مستخدم"
    logger.info(f"طلب استعلام اجتماعات بدون تصميمات من: {user_name}")

    user_states[user_id] = {'waiting_for': 'branch_selection_meetings_no_designs'}

    await update.message.reply_text(
        "📍 **من فضلك اختر الفرع:**\n\n"
        "1 - فرع التجمع\n"
        "2 - فرع مدينة نصر\n"
        "3 - للكل\n\n"
        "💡 للإلغاء اكتب: /start",
        parse_mode='Markdown'
    )

async def designs_without_contracts_start(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """بداية استعلام تصميمات بدون عقود"""
    if not update.effective_user or not update.message:
        return

    user_id = update.effective_user.id
    user_name = update.effective_user.first_name or "مستخدم"
    logger.info(f"طلب استعلام تصميمات بدون عقود من: {user_name}")

    user_states[user_id] = {'waiting_for': 'branch_selection_designs_no_contracts'}

    await update.message.reply_text(
        "📍 **من فضلك اختر الفرع:**\n\n"
        "1 - فرع التجمع\n"
        "2 - فرع مدينة نصر\n"
        "3 - للكل\n\n"
        "💡 للإلغاء اكتب: /start",
        parse_mode='Markdown'
    )

def is_menu_button(text):
    """التحقق من أن النص هو زر من القائمة الرئيسية"""
    menu_buttons = [
        '🔍 بحث عميل', '📊 استعلام عن العملاء', '📱 عدد عملاء وسائل التواصل',
        '🌍 عدد العملاء حسب المناطق', '📊 عملاء بدون معاينات',
        '👁️ معاينات بدون اجتماعات', '🤝 اجتماعات بدون تصميمات',
        '🎨 تصميمات بدون عقود', '❌ إنهاء المحادثة'
    ]
    return text in menu_buttons or text.startswith('/')

async def send_long_message(update, message_text, parse_mode=None):
    """إرسال رسالة طويلة مقسمة إلى أجزاء"""
    max_length = 4000  # ترك مساحة أمان

    if len(message_text) <= max_length:
        try:
            await update.message.reply_text(message_text, parse_mode=parse_mode)
        except Exception as e:
            logger.error(f"خطأ في إرسال الرسالة: {str(e)}")
            await update.message.reply_text(message_text)
        return

    # تقسيم الرسالة الطويلة
    lines = message_text.split('\n')
    current_message = ""
    message_count = 1

    for line in lines:
        if len(current_message + line + '\n') > max_length:
            if current_message:
                header = f"📄 **الجزء {message_count}:**\n\n" if message_count > 1 else ""
                try:
                    await update.message.reply_text(header + current_message, parse_mode=parse_mode)
                except Exception as e:
                    logger.error(f"خطأ في إرسال الجزء {message_count}: {str(e)}")
                    await update.message.reply_text(header + current_message)

                await asyncio.sleep(1)
                message_count += 1
                current_message = ""

        current_message += line + '\n'

    # إرسال الجزء الأخير
    if current_message:
        header = f"📄 **الجزء {message_count}:**\n\n" if message_count > 1 else ""
        try:
            await update.message.reply_text(header + current_message, parse_mode=parse_mode)
        except Exception as e:
            logger.error(f"خطأ في إرسال الجزء الأخير: {str(e)}")
            await update.message.reply_text(header + current_message)

async def handle_text(update: Update, context: ContextTypes.DEFAULT_TYPE):
    """معالجة النصوص"""
    if not update.effective_user or not update.message or not update.message.text:
        return

    user_id = update.effective_user.id
    user_input = update.message.text.strip()
    user_name = update.effective_user.first_name or "مستخدم"

    # التحقق من حالة المستخدم
    user_state = user_states.get(user_id, {})
    waiting_for = user_state.get('waiting_for')

    # إذا كان المستخدم في حالة انتظار وأدخل زر من القائمة، إعادة توجيه
    if waiting_for and is_menu_button(user_input):
        logger.info(f"إعادة توجيه من حالة {waiting_for} إلى أمر جديد: {user_input}")
        user_states[user_id] = {'waiting_for': None}
        await handle_text(update, context)
        return

    # معالجة أزرار القائمة الرئيسية
    if user_input == '🔍 بحث عميل':
        await search_customer_start(update, context)
    elif user_input == '📊 استعلام عن العملاء':
        await customers_inquiry_start(update, context)
    elif user_input == '🌍 عدد العملاء حسب المناطق':
        await regions_inquiry_start(update, context)
    elif user_input == '📱 عدد عملاء وسائل التواصل':
        await social_media_inquiry_start(update, context)
    elif user_input == '📊 عملاء بدون معاينات':
        await customers_without_previews_start(update, context)
    elif user_input == '👁️ معاينات بدون اجتماعات':
        await previews_without_meetings_start(update, context)
    elif user_input == '🤝 اجتماعات بدون تصميمات':
        await meetings_without_designs_start(update, context)
    elif user_input == '🎨 تصميمات بدون عقود':
        await designs_without_contracts_start(update, context)
    elif user_input == '❌ إنهاء المحادثة':
        await update.message.reply_text("👋 شكراً لاستخدام Terra Bot!\n\n🔄 لبدء جلسة جديدة اكتب: /start")
        user_states[user_id] = {'waiting_for': None}

    # معالجة حالات الانتظار
    elif waiting_for == 'customer_search':
        if user_input.lower() in ['الغاء', 'إلغاء', 'خروج', 'cancel'] or user_input.startswith('/'):
            await start(update, context)
            return

        logger.info(f"بحث عن عميل: {user_input} من: {user_name}")
        await update.message.reply_text("🔄 جاري البحث...")

        try:
            result = search_customer_in_db(user_input)
            await send_long_message(update, result)

            # الحفاظ على حالة البحث للبحث التالي
            if "📋 **بيانات العميل**" in result:
                user_states[user_id] = {'waiting_for': 'customer_search'}
            else:
                user_states[user_id] = {'waiting_for': None}

        except Exception as e:
            logger.error(f"خطأ في البحث: {str(e)}")
            await update.message.reply_text(f"❌ حدث خطأ أثناء البحث\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start")

    elif waiting_for in ['branch_selection_customers', 'branch_selection_regions', 'branch_selection_social_media',
                        'branch_selection_no_previews', 'branch_selection_previews_no_meetings',
                        'branch_selection_meetings_no_designs', 'branch_selection_designs_no_contracts']:
        if user_input in ['1', '2', '3']:
            branch_names = {'1': 'فرع التجمع', '2': 'فرع مدينة نصر', '3': 'للكل'}
            selected_branch = branch_names[user_input]

            await update.message.reply_text(f"✅ تم اختيار {selected_branch}")

            # تحديد نوع الاستعلام التالي
            if waiting_for == 'branch_selection_customers':
                next_state = 'days_customers'
            elif waiting_for == 'branch_selection_regions':
                next_state = 'days_regions'
            elif waiting_for == 'branch_selection_social_media':
                next_state = 'days_social_media'
            elif waiting_for == 'branch_selection_no_previews':
                next_state = 'days_no_previews'
            elif waiting_for == 'branch_selection_previews_no_meetings':
                next_state = 'days_previews_no_meetings'
            elif waiting_for == 'branch_selection_meetings_no_designs':
                next_state = 'days_meetings_no_designs'
            elif waiting_for == 'branch_selection_designs_no_contracts':
                next_state = 'days_designs_no_contracts'

            user_states[user_id] = {
                'waiting_for': next_state,
                'selected_branch': user_input,
                'branch_name': selected_branch
            }

            await update.message.reply_text(
                f"📅 من فضلك ادخل عدد أيام مدة البحث أو اكتب # للكل\n\n"
                f"🏢 الفرع المختار: {selected_branch}\n\n"
                f"💡 للإلغاء اكتب: /start"
            )
        else:
            await update.message.reply_text("⚠️ من فضلك اختر رقم صحيح: 1 أو 2 أو 3")

    elif waiting_for.startswith('days_'):
        if user_input.startswith('/'):
            await start(update, context)
            return

        if user_input == '#':
            days_value = None
            days_text = "للكل"
        else:
            try:
                days_value = int(user_input)
                days_text = f"{days_value} أيام"
            except ValueError:
                await update.message.reply_text("⚠️ من فضلك أدخل رقم صحيح أو # للكل")
                return

        branch_id = user_state.get('selected_branch', '1')
        branch_name = user_state.get('branch_name', 'غير محدد')

        await update.message.reply_text("🔄 جاري تحضير التقرير...")

        try:
            # تحديد الوظيفة المناسبة حسب نوع الاستعلام
            if waiting_for == 'days_customers':
                result = get_customers_list(branch_id, days_value)
            elif waiting_for == 'days_regions':
                result = get_previews_count_by_regions(branch_id, days_value)
            elif waiting_for == 'days_social_media':
                result = get_previews_count_by_social_media(branch_id, days_value)
            elif waiting_for == 'days_no_previews':
                result = get_customers_without_previews(branch_id, days_value)
            elif waiting_for == 'days_previews_no_meetings':
                result = get_previews_without_meetings(branch_id, days_value)
            elif waiting_for == 'days_meetings_no_designs':
                result = get_meetings_without_designs(branch_id, days_value)
            elif waiting_for == 'days_designs_no_contracts':
                result = get_designs_without_contracts(branch_id, days_value)
            else:
                result = "❌ نوع استعلام غير معروف"

            await send_long_message(update, result)
            user_states[user_id] = {'waiting_for': None}

        except Exception as e:
            logger.error(f"خطأ في تحضير التقرير: {str(e)}")
            await update.message.reply_text(f"❌ حدث خطأ أثناء تحضير التقرير\n\n🔙 للعودة للقائمة الرئيسية اكتب: /start")
            user_states[user_id] = {'waiting_for': None}

    else:
        # رسالة غير معروفة
        await update.message.reply_text(
            "❓ لم أفهم طلبك\n\n"
            "🔄 للعودة للقائمة الرئيسية اكتب: /start\n"
            "🧪 لاختبار قاعدة البيانات اكتب: /test"
        )

def main():
    """الدالة الرئيسية لتشغيل البوت"""
    try:
        # إنشاء التطبيق
        application = Application.builder().token(BOT_TOKEN).build()

        # إضافة معالجات الأوامر
        application.add_handler(CommandHandler("start", start))
        application.add_handler(CommandHandler("test", test_db_command))

        # إضافة معالج النصوص
        application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_text))

        # بدء البوت
        logger.info("🚀 بدء تشغيل Terra Bot المحسن...")
        logger.info("🔗 اختبار الاتصال بقاعدة البيانات...")

        # اختبار الاتصال بقاعدة البيانات عند البدء
        test_result = test_database()
        if "✅" in test_result:
            logger.info("✅ تم الاتصال بقاعدة البيانات بنجاح")
        else:
            logger.error("❌ فشل الاتصال بقاعدة البيانات")
            logger.error(test_result)

        logger.info("📱 البوت جاهز لاستقبال الرسائل...")

        # تشغيل البوت
        application.run_polling(drop_pending_updates=True)

    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {str(e)}")
        raise

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("🛑 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ عام في البوت: {str(e)}")
        raise
