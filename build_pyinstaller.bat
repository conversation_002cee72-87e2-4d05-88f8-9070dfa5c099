@echo off
chcp 65001 >nul
title Terra Bot Enhanced - PyInstaller Build
color 0A

echo ========================================
echo    Terra Bot Enhanced - PyInstaller Build
echo    مع إعداد التشغيل التلقائي المتقدم
echo ========================================
echo.

echo [1/6] تحقق من Python...
python --version
if %errorlevel% neq 0 (
    echo ❌ خطأ: Python غير مثبت أو غير موجود في PATH
    echo 💡 قم بتثبيت Python من: https://python.org
    pause
    exit /b 1
)
echo ✅ Python موجود

echo.
echo [2/6] تثبيت PyInstaller...
pip install pyinstaller
if %errorlevel% neq 0 (
    echo ❌ خطأ: فشل في تثبيت PyInstaller
    pause
    exit /b 1
)
echo ✅ تم تثبيت PyInstaller

echo.
echo [3/6] تثبيت المكتبات المطلوبة...
pip install python-telegram-bot pyodbc
if %errorlevel% neq 0 (
    echo ❌ خطأ: فشل في تثبيت المكتبات
    pause
    exit /b 1
)
echo ✅ تم تثبيت المكتبات

echo.
echo [4/6] تنظيف الملفات القديمة...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"
if exist "__pycache__" rmdir /s /q "__pycache__"
echo ✅ تم تنظيف الملفات القديمة

echo.
echo [5/6] بناء الملف التنفيذي باستخدام PyInstaller...

REM استخدام ملف spec المخصص
if exist "terra_bot.spec" (
    echo استخدام ملف spec المخصص...
    pyinstaller terra_bot.spec --clean --noconfirm
) else (
    echo استخدام الأوامر المباشرة...
    pyinstaller ^
        --onefile ^
        --console ^
        --name "TerraBot_Enhanced" ^
        --add-data "version_info.txt;." ^
        --hidden-import "telegram" ^
        --hidden-import "telegram.ext" ^
        --hidden-import "pyodbc" ^
        --hidden-import "asyncio" ^
        --hidden-import "threading" ^
        --hidden-import "winreg" ^
        --exclude-module "tkinter" ^
        --exclude-module "matplotlib" ^
        --exclude-module "numpy" ^
        --exclude-module "pandas" ^
        --clean ^
        --noconfirm ^
        enhanced_notifications.py
)

if %errorlevel% neq 0 (
    echo ❌ خطأ: فشل في بناء الملف التنفيذي
    pause
    exit /b 1
)
echo ✅ تم بناء الملف التنفيذي

echo.
echo [6/6] إنشاء الملفات الإضافية...

REM إنشاء مجلد التوزيع
if not exist "TerraBot_Distribution" mkdir "TerraBot_Distribution"

REM نسخ الملف التنفيذي
if exist "dist\TerraBot_Enhanced.exe" (
    copy "dist\TerraBot_Enhanced.exe" "TerraBot_Distribution\" >nul
    echo ✅ تم نسخ الملف التنفيذي
) else (
    echo ❌ لم يتم العثور على الملف التنفيذي
    pause
    exit /b 1
)

REM إنشاء ملف إدارة التشغيل التلقائي المحسن
echo إنشاء ملف إدارة التشغيل التلقائي...
(
echo @echo off
echo chcp 65001 ^>nul
echo title Terra Bot - إعداد التشغيل التلقائي
echo color 0B
echo.
echo ========================================
echo     Terra Bot Enhanced - إعداد التشغيل التلقائي
echo ========================================
echo.
echo 🤖 **Terra Bot Enhanced**
echo 📅 الإصدار: 2.0
echo 🏢 الشركة: Terra Company
echo.
echo اختر أحد الخيارات:
echo.
echo 1. ✅ إضافة للتشغيل التلقائي عند بداية النظام
echo 2. ❌ إزالة من التشغيل التلقائي  
echo 3. 📊 فحص حالة التشغيل التلقائي
echo 4. 🚀 تشغيل البوت الآن
echo 5. ❌ إلغاء
echo.
set /p choice="اختيارك (1-5): "
echo.
if "%%choice%%"=="1" (
    echo 🔄 جاري إضافة Terra Bot للتشغيل التلقائي...
    reg add "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "TerraBot Enhanced" /t REG_SZ /d "%%~dp0TerraBot_Enhanced.exe" /f ^>nul 2^>^&1
    if %%errorlevel%%==0 (
        echo ✅ تم إضافة Terra Bot للتشغيل التلقائي بنجاح
        echo 🎯 سيتم تشغيل البوت تلقائياً عند بداية تشغيل النظام
        echo 💡 يمكنك إعادة تشغيل الكمبيوتر للتأكد من عمل التشغيل التلقائي
    ^) else (
        echo ❌ فشل في إضافة التشغيل التلقائي
        echo 💡 تأكد من تشغيل الملف كمدير
    ^)
^) else if "%%choice%%"=="2" (
    echo 🔄 جاري إزالة Terra Bot من التشغيل التلقائي...
    reg delete "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "TerraBot Enhanced" /f ^>nul 2^>^&1
    if %%errorlevel%%==0 (
        echo ✅ تم إزالة Terra Bot من التشغيل التلقائي بنجاح
    ^) else (
        echo ⚠️ Terra Bot غير موجود في التشغيل التلقائي أو حدث خطأ
    ^)
^) else if "%%choice%%"=="3" (
    echo 🔍 فحص حالة التشغيل التلقائي...
    reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "TerraBot Enhanced" ^>nul 2^>^&1
    if %%errorlevel%%==0 (
        echo ✅ Terra Bot موجود في التشغيل التلقائي
        echo 📍 المسار المسجل:
        reg query "HKCU\Software\Microsoft\Windows\CurrentVersion\Run" /v "TerraBot Enhanced"
    ^) else (
        echo ❌ Terra Bot غير موجود في التشغيل التلقائي
    ^)
^) else if "%%choice%%"=="4" (
    echo 🚀 تشغيل Terra Bot...
    start "" "%%~dp0TerraBot_Enhanced.exe"
    echo ✅ تم تشغيل البوت
^) else (
    echo ❌ تم الإلغاء
^)
echo.
echo ========================================
echo شكراً لاستخدام Terra Bot Enhanced!
echo ========================================
pause
) > "TerraBot_Distribution\startup_manager.bat"
echo ✅ تم إنشاء ملف إدارة التشغيل التلقائي

REM إنشاء ملف التعليمات
echo إنشاء ملف التعليمات...
(
echo ========================================
echo     Terra Bot Enhanced - تعليمات الاستخدام
echo ========================================
echo.
echo 🤖 **Terra Bot Enhanced v2.0**
echo 🏢 **الشركة:** Terra Company
echo 📅 **تاريخ البناء:** %date% %time%
echo.
echo 📁 **الملفات الموجودة:**
echo    - TerraBot_Enhanced.exe : الملف الرئيسي للبوت
echo    - startup_manager.bat   : إدارة التشغيل التلقائي
echo    - README.txt           : هذا الملف
echo.
echo 🚀 **طريقة التشغيل:**
echo    1. شغل TerraBot_Enhanced.exe
echo    2. اختر وضع التشغيل:
echo       - 1: بوت تليجرام مع إشعارات ^(افتراضي^)
echo       - 2: مراقب قاعدة البيانات فقط
echo    3. البوت سيبدأ العمل تلقائياً
echo.
echo ⚙️ **التشغيل التلقائي:**
echo    - شغل startup_manager.bat لإدارة التشغيل التلقائي
echo    - يمكن إضافة/إزالة البوت من بداية تشغيل النظام
echo    - يمكن فحص حالة التشغيل التلقائي
echo.
echo 📱 **الأوامر المتاحة في التليجرام:**
echo    /start - بدء البوت وعرض القائمة الرئيسية
echo    /test  - اختبار الاتصال بقاعدة البيانات  
echo    /id    - عرض معرف المستخدم/الجروب للإشعارات
echo.
echo 🔔 **نظام الإشعارات:**
echo    - يرسل إشعارات تلقائية عند إضافة بيانات جديدة
echo    - يراقب: العملاء، المعاينات، الاجتماعات، التصميمات، العقود
echo    - يعرض تفاصيل شاملة مع ربط جميع الجداول
echo    - يظهر: الفرع، المنطقة، من أنشأ، طريقة الدفع، وسيلة التواصل
echo.
echo 🔧 **إعدادات قاعدة البيانات:**
echo    - الخادم: . ^(المحلي^)
echo    - قاعدة البيانات: Terra
echo    - المستخدم: sa
echo    - كلمة المرور: Ret_ME@
echo.
echo 📞 **معرفات الإشعارات الحالية:**
echo    - 1107000748 ^(مستخدم 1^)
echo    - 1206533289 ^(مستخدم 2^)
echo    - لإضافة معرفات جديدة: استخدم /id في البوت
echo.
echo 🛠️ **استكشاف الأخطاء:**
echo    - تأكد من تشغيل SQL Server
echo    - تحقق من إعدادات قاعدة البيانات
echo    - تأكد من صحة توكن البوت
echo    - للجروبات: أضف البوت للجروب أولاً
echo.
echo 🔒 **الأمان:**
echo    - البوت يقرأ فقط من قاعدة البيانات
echo    - لا يكتب أو يعدل أي بيانات
echo    - آمن للاستخدام في بيئة الإنتاج
echo    - يتجاهل البيانات التجريبية
echo.
echo 💡 **ملاحظات مهمة:**
echo    - يحتاج اتصال بالإنترنت للعمل
echo    - يعمل في الخلفية بعد التشغيل
echo    - يمكن إيقافه من نافذة الكونسول ^(Ctrl+C^)
echo    - يحفظ سجل العمليات في ملف terra_bot.log
echo.
echo ========================================
echo     للدعم الفني: اتصل بفريق التطوير
echo     © 2024 Terra Company - جميع الحقوق محفوظة
echo ========================================
) > "TerraBot_Distribution\README.txt"
echo ✅ تم إنشاء ملف التعليمات

REM إنشاء ملف تشغيل سريع
echo إنشاء ملف تشغيل سريع...
(
echo @echo off
echo title Terra Bot Enhanced - تشغيل سريع
echo echo 🚀 تشغيل Terra Bot Enhanced...
echo start "" "TerraBot_Enhanced.exe"
echo echo ✅ تم تشغيل البوت
echo timeout /t 2 /nobreak ^>nul
) > "TerraBot_Distribution\run_bot.bat"
echo ✅ تم إنشاء ملف التشغيل السريع

echo.
echo ========================================
echo 🎉 تم إنشاء الملف التنفيذي بنجاح!
echo ========================================
echo 📁 **مجلد التوزيع:** TerraBot_Distribution
echo 📄 **الملفات المُنشأة:**
echo    ✅ TerraBot_Enhanced.exe    - الملف التنفيذي الرئيسي
echo    ✅ startup_manager.bat      - إدارة التشغيل التلقائي المحسن
echo    ✅ README.txt              - تعليمات الاستخدام الشاملة
echo    ✅ run_bot.bat             - تشغيل سريع للبوت
echo.
echo 📊 **حجم الملف:** 
for %%A in ("TerraBot_Distribution\TerraBot_Enhanced.exe") do echo    %%~zA bytes
echo.
echo 🔧 **تم البناء باستخدام:** PyInstaller
echo 📅 **تاريخ البناء:** %date% %time%
echo ========================================
echo.

echo 💡 **الخيارات المتاحة:**
echo 1. 📁 فتح مجلد التوزيع
echo 2. 🚀 تشغيل البوت الآن
echo 3. ⚙️ إعداد التشغيل التلقائي
echo 4. 📋 نسخ الملفات لمكان آخر
echo 5. ❌ إنهاء
echo.
set /p choice="اختيارك (1-5): "

if "%choice%"=="1" (
    echo 📁 فتح مجلد التوزيع...
    explorer "TerraBot_Distribution"
) else if "%choice%"=="2" (
    echo 🚀 تشغيل البوت...
    cd "TerraBot_Distribution"
    start TerraBot_Enhanced.exe
) else if "%choice%"=="3" (
    echo ⚙️ إعداد التشغيل التلقائي...
    cd "TerraBot_Distribution"
    start startup_manager.bat
) else if "%choice%"=="4" (
    echo 📋 يمكنك نسخ مجلد TerraBot_Distribution لأي مكان تريده
    echo 💡 الملف التنفيذي مستقل ولا يحتاج تثبيت
    explorer "TerraBot_Distribution"
) else (
    echo ❌ تم الإنهاء
)

echo.
echo 🎯 **Terra Bot Enhanced جاهز للاستخدام!**
echo 📱 **لا تنس إضافة البوت للجروبات المطلوبة**
echo 🔔 **استخدم /id لمعرفة معرفات الإشعارات**
echo.
echo شكراً لاستخدام Terra Bot Enhanced!
pause
