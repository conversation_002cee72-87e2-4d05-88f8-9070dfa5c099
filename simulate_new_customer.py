#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
محاكاة إضافة عميل جديد لاختبار نظام الإشعارات
"""

import pyodbc
from datetime import datetime

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

def connect_to_db():
    """الاتصال بقاعدة البيانات"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['user']};PWD={DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def get_next_customer_code():
    """الحصول على كود العميل التالي"""
    try:
        conn = connect_to_db()
        if not conn:
            return None
        
        cursor = conn.cursor()
        
        # الحصول على آخر كود عميل
        cursor.execute("SELECT MAX(CAST(CustomerCode AS INT)) FROM Acc_Customers WHERE ISNUMERIC(CustomerCode) = 1")
        result = cursor.fetchone()
        
        if result and result[0]:
            next_code = str(result[0] + 1)
        else:
            next_code = "1000"  # كود افتراضي
        
        conn.close()
        return next_code
        
    except Exception as e:
        print(f"❌ خطأ في الحصول على كود العميل التالي: {str(e)}")
        return None

def add_test_customer():
    """إضافة عميل تجريبي"""
    try:
        conn = connect_to_db()
        if not conn:
            return False
        
        cursor = conn.cursor()
        
        # الحصول على كود العميل التالي
        customer_code = get_next_customer_code()
        if not customer_code:
            print("❌ فشل في الحصول على كود العميل")
            return False
        
        # بيانات العميل التجريبي
        customer_name = f"عميل تجريبي {datetime.now().strftime('%H:%M:%S')}"
        phone = f"0101234{datetime.now().strftime('%H%M')}"
        branch_id = 3  # فرع التجمع
        
        # إدراج العميل الجديد
        insert_query = """
        INSERT INTO Acc_Customers 
        (CustomerCode, NameAr, MainPhoneNo, BranchId, AddDate, AddUser, Status, IsDeleted)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        cursor.execute(insert_query, (
            customer_code,
            customer_name,
            phone,
            branch_id,
            datetime.now(),
            1,  # AddUser
            1,  # Status
            0   # IsDeleted
        ))
        
        conn.commit()
        conn.close()
        
        print(f"✅ تم إضافة عميل تجريبي بنجاح:")
        print(f"   الكود: {customer_code}")
        print(f"   الاسم: {customer_name}")
        print(f"   الهاتف: {phone}")
        print(f"   الفرع: {branch_id}")
        print(f"   الوقت: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إضافة العميل التجريبي: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🧪 محاكاة إضافة عميل جديد")
    print("=" * 40)
    
    # إضافة عميل تجريبي
    success = add_test_customer()
    
    if success:
        print("\n✅ تم إضافة العميل بنجاح!")
        print("🔔 يجب أن يصل إشعار تلقائي خلال 30 ثانية")
        print("📱 تحقق من التليجرام للإشعارات")
    else:
        print("\n❌ فشل في إضافة العميل")
    
    print("\n" + "=" * 40)

if __name__ == "__main__":
    main()
