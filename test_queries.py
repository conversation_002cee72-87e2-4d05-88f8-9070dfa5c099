#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الاستعلامات مع أسماء الجداول الصحيحة
"""

import pyodbc

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

def connect_to_db():
    """الاتصال بقاعدة البيانات"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['user']};PWD={DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def test_customers_query():
    """اختبار استعلام العملاء"""
    print("🔍 اختبار استعلام العملاء...")
    try:
        conn = connect_to_db()
        if not conn:
            return False
        
        cursor = conn.cursor()
        query = "SELECT TOP 5 CustomerCode, NameAr, MainPhoneNo, BranchId, AddDate FROM Acc_Customers WHERE IsDeleted = 0 ORDER BY AddDate DESC"
        cursor.execute(query)
        customers = cursor.fetchall()
        
        print(f"✅ تم العثور على {len(customers)} عملاء")
        for customer in customers:
            print(f"   - {customer[1] or 'غير محدد'} | {customer[0] or 'غير محدد'} | {customer[2] or 'غير محدد'}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استعلام العملاء: {str(e)}")
        return False

def test_previews_query():
    """اختبار استعلام المعاينات"""
    print("\n🔍 اختبار استعلام المعاينات...")
    try:
        conn = connect_to_db()
        if not conn:
            return False
        
        cursor = conn.cursor()
        query = """
        SELECT TOP 5 p.PreviewCode, c.CustomerName, c.PrimaryPhone, c.BranchID, p.PreviewDate
        FROM Sys_Previews p
        INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
        ORDER BY p.PreviewDate DESC
        """
        cursor.execute(query)
        previews = cursor.fetchall()
        
        print(f"✅ تم العثور على {len(previews)} معاينات")
        for preview in previews:
            print(f"   - {preview[1] or 'غير محدد'} | {preview[0] or 'غير محدد'} | {preview[2] or 'غير محدد'}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استعلام المعاينات: {str(e)}")
        return False

def test_meetings_query():
    """اختبار استعلام الاجتماعات"""
    print("\n🔍 اختبار استعلام الاجتماعات...")
    try:
        conn = connect_to_db()
        if not conn:
            return False
        
        cursor = conn.cursor()
        query = """
        SELECT TOP 5 m.MeetingCode, c.CustomerName, c.PrimaryPhone, c.BranchID, m.MeetingDate
        FROM Sys_Meetings m
        INNER JOIN Sys_Previews p ON m.PreviewCode = p.PreviewCode
        INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
        ORDER BY m.MeetingDate DESC
        """
        cursor.execute(query)
        meetings = cursor.fetchall()
        
        print(f"✅ تم العثور على {len(meetings)} اجتماعات")
        for meeting in meetings:
            print(f"   - {meeting[1] or 'غير محدد'} | {meeting[0] or 'غير محدد'} | {meeting[2] or 'غير محدد'}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استعلام الاجتماعات: {str(e)}")
        return False

def test_designs_query():
    """اختبار استعلام التصميمات"""
    print("\n🔍 اختبار استعلام التصميمات...")
    try:
        conn = connect_to_db()
        if not conn:
            return False
        
        cursor = conn.cursor()
        query = """
        SELECT TOP 5 d.DesignCode, c.CustomerName, c.PrimaryPhone, c.BranchID, d.DesignDate
        FROM Sys_Designs d
        INNER JOIN Sys_Meetings m ON d.MeetingCode = m.MeetingCode
        INNER JOIN Sys_Previews p ON m.PreviewCode = p.PreviewCode
        INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
        ORDER BY d.DesignDate DESC
        """
        cursor.execute(query)
        designs = cursor.fetchall()
        
        print(f"✅ تم العثور على {len(designs)} تصميمات")
        for design in designs:
            print(f"   - {design[1] or 'غير محدد'} | {design[0] or 'غير محدد'} | {design[2] or 'غير محدد'}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استعلام التصميمات: {str(e)}")
        return False

def test_contracts_query():
    """اختبار استعلام العقود"""
    print("\n🔍 اختبار استعلام العقود...")
    try:
        conn = connect_to_db()
        if not conn:
            return False
        
        cursor = conn.cursor()
        query = """
        SELECT TOP 5 ct.ContractCode, c.CustomerName, c.PrimaryPhone, c.BranchID, ct.ContractDate
        FROM Acc_Contracts ct
        INNER JOIN Sys_Designs d ON ct.DesignCode = d.DesignCode
        INNER JOIN Sys_Meetings m ON d.MeetingCode = m.MeetingCode
        INNER JOIN Sys_Previews p ON m.PreviewCode = p.PreviewCode
        INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
        ORDER BY ct.ContractDate DESC
        """
        cursor.execute(query)
        contracts = cursor.fetchall()
        
        print(f"✅ تم العثور على {len(contracts)} عقود")
        for contract in contracts:
            print(f"   - {contract[1] or 'غير محدد'} | {contract[0] or 'غير محدد'} | {contract[2] or 'غير محدد'}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استعلام العقود: {str(e)}")
        return False

def test_regions_query():
    """اختبار استعلام المناطق"""
    print("\n🔍 اختبار استعلام المناطق...")
    try:
        conn = connect_to_db()
        if not conn:
            return False
        
        cursor = conn.cursor()
        query = """
        SELECT CustomerRegion, COUNT(*) as Count
        FROM Sys_Previews p
        INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
        GROUP BY CustomerRegion
        ORDER BY Count DESC
        """
        cursor.execute(query)
        regions = cursor.fetchall()
        
        print(f"✅ تم العثور على {len(regions)} منطقة")
        for region in regions[:5]:
            print(f"   - {region[0] or 'غير محدد'}: {region[1]} معاينة")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في استعلام المناطق: {str(e)}")
        return False

def main():
    """الدالة الرئيسية"""
    print("🌟 اختبار استعلامات قاعدة البيانات Terra 🌟")
    print("=" * 60)
    
    tests = [
        test_customers_query,
        test_previews_query,
        test_meetings_query,
        test_designs_query,
        test_contracts_query,
        test_regions_query
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار: {passed}/{total} اختبارات نجحت")
    
    if passed == total:
        print("🎉 جميع الاستعلامات تعمل بنجاح!")
        print("✅ البوت جاهز للتشغيل")
    else:
        print("⚠️ بعض الاستعلامات تحتاج إلى مراجعة")
    
    return passed == total

if __name__ == "__main__":
    main()
