#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pyodbc
from datetime import datetime

def fix_unique_constraint():
    """إصلاح الـ UNIQUE constraint ليشمل BranchId"""
    try:
        # الاتصال بقاعدة بيانات الإشعارات
        conn_str = "DRIVER={SQL Server};SERVER=.;DATABASE=Terra_Notifications_DB1;UID=sa;PWD=*******"
        conn = pyodbc.connect(conn_str)
        
        cursor = conn.cursor()
        
        print("🔧 إصلاح الـ UNIQUE constraint...")
        
        # حذف الـ constraint القديم
        print("🗑️ حذف الـ constraint القديم...")
        try:
            cursor.execute("ALTER TABLE Notifications_Log DROP CONSTRAINT UK_Notifications_DataType_RecordId")
            print("✅ تم حذف الـ constraint القديم")
        except Exception as e:
            print(f"⚠️ خطأ في حذف الـ constraint: {e}")
        
        # إضافة constraint جديد يشمل BranchId
        print("➕ إضافة constraint جديد...")
        try:
            cursor.execute("""
                ALTER TABLE Notifications_Log 
                ADD CONSTRAINT UK_Notifications_DataType_RecordId_Branch 
                UNIQUE (DataType, RecordId, BranchId)
            """)
            print("✅ تم إضافة الـ constraint الجديد: (DataType, RecordId, BranchId)")
        except Exception as e:
            print(f"❌ خطأ في إضافة الـ constraint: {e}")
        
        # التحقق من النتيجة
        print("\n🔍 التحقق من الـ constraints الجديدة...")
        cursor.execute("""
            SELECT 
                tc.CONSTRAINT_NAME,
                tc.CONSTRAINT_TYPE,
                kcu.COLUMN_NAME
            FROM INFORMATION_SCHEMA.TABLE_CONSTRAINTS tc
            JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE kcu 
                ON tc.CONSTRAINT_NAME = kcu.CONSTRAINT_NAME
            WHERE tc.TABLE_NAME = 'Notifications_Log'
                AND tc.CONSTRAINT_TYPE IN ('UNIQUE', 'PRIMARY KEY')
            ORDER BY tc.CONSTRAINT_NAME, kcu.ORDINAL_POSITION
        """)
        
        constraints = cursor.fetchall()
        
        if constraints:
            print("📊 الـ constraints الجديدة:")
            current_constraint = None
            columns = []
            
            for row in constraints:
                constraint_name = row[0]
                constraint_type = row[1]
                column_name = row[2]
                
                if constraint_name != current_constraint:
                    if current_constraint:
                        print(f"   {current_constraint} ({constraint_type}): {', '.join(columns)}")
                    current_constraint = constraint_name
                    columns = [column_name]
                else:
                    columns.append(column_name)
            
            # طباعة آخر constraint
            if current_constraint:
                print(f"   {current_constraint} ({constraint_type}): {', '.join(columns)}")
        
        conn.commit()
        conn.close()
        
        print("\n✅ تم إصلاح الـ constraint بنجاح!")
        print("🎯 الآن يمكن حفظ نفس الكود في فروع مختلفة")
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    fix_unique_constraint()
