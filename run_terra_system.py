#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تشغيل نظام Terra المتكامل
يشغل البوت ومراقب قاعدة البيانات معاً
"""

import asyncio
import logging
import subprocess
import sys
import time
from datetime import datetime

# إعداد التسجيل
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO,
    handlers=[
        logging.FileHandler('terra_system.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

def run_bot():
    """تشغيل البوت"""
    try:
        logger.info("🤖 بدء تشغيل Terra Bot...")
        result = subprocess.run([sys.executable, "terra_bot_improved.py"], 
                              capture_output=True, text=True, encoding='utf-8')
        if result.returncode != 0:
            logger.error(f"❌ خطأ في تشغيل البوت: {result.stderr}")
        return result.returncode
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل البوت: {str(e)}")
        return 1

def run_monitor():
    """تشغيل مراقب قاعدة البيانات"""
    try:
        logger.info("🔍 بدء تشغيل مراقب قاعدة البيانات...")
        result = subprocess.run([sys.executable, "database_monitor.py"], 
                              capture_output=True, text=True, encoding='utf-8')
        if result.returncode != 0:
            logger.error(f"❌ خطأ في تشغيل المراقب: {result.stderr}")
        return result.returncode
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل المراقب: {str(e)}")
        return 1

async def run_both_async():
    """تشغيل البوت والمراقب بشكل متوازي"""
    try:
        logger.info("🚀 بدء تشغيل نظام Terra المتكامل...")
        logger.info(f"⏰ وقت البدء: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # تشغيل البوت والمراقب في عمليات منفصلة
        bot_process = subprocess.Popen([sys.executable, "terra_bot_improved.py"])
        monitor_process = subprocess.Popen([sys.executable, "database_monitor.py"])
        
        logger.info(f"🤖 البوت يعمل - PID: {bot_process.pid}")
        logger.info(f"🔍 المراقب يعمل - PID: {monitor_process.pid}")
        
        # انتظار العمليات
        try:
            while True:
                # فحص حالة العمليات
                bot_status = bot_process.poll()
                monitor_status = monitor_process.poll()
                
                if bot_status is not None:
                    logger.error(f"❌ البوت توقف بكود: {bot_status}")
                    # إعادة تشغيل البوت
                    logger.info("🔄 إعادة تشغيل البوت...")
                    bot_process = subprocess.Popen([sys.executable, "terra_bot_improved.py"])
                    logger.info(f"🤖 البوت يعمل مرة أخرى - PID: {bot_process.pid}")
                
                if monitor_status is not None:
                    logger.error(f"❌ المراقب توقف بكود: {monitor_status}")
                    # إعادة تشغيل المراقب
                    logger.info("🔄 إعادة تشغيل المراقب...")
                    monitor_process = subprocess.Popen([sys.executable, "database_monitor.py"])
                    logger.info(f"🔍 المراقب يعمل مرة أخرى - PID: {monitor_process.pid}")
                
                # انتظار 10 ثوان قبل الفحص التالي
                await asyncio.sleep(10)
                
        except KeyboardInterrupt:
            logger.info("🛑 تم إيقاف النظام بواسطة المستخدم")
            
            # إيقاف العمليات
            logger.info("⏹️ إيقاف البوت...")
            bot_process.terminate()
            bot_process.wait()
            
            logger.info("⏹️ إيقاف المراقب...")
            monitor_process.terminate()
            monitor_process.wait()
            
            logger.info("✅ تم إيقاف جميع العمليات بنجاح")
            
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل النظام: {str(e)}")
        raise

def main():
    """الدالة الرئيسية"""
    try:
        print("🌟 مرحباً بك في نظام Terra المتكامل 🌟")
        print("=" * 50)
        print("هذا النظام يشمل:")
        print("🤖 Terra Bot - بوت تليجرام للاستعلامات")
        print("🔍 Database Monitor - مراقب قاعدة البيانات للإشعارات")
        print("=" * 50)
        
        choice = input("\nاختر طريقة التشغيل:\n1. تشغيل البوت فقط\n2. تشغيل المراقب فقط\n3. تشغيل النظام المتكامل (موصى به)\n\nاختيارك (1-3): ")
        
        if choice == "1":
            logger.info("تم اختيار تشغيل البوت فقط")
            return run_bot()
        elif choice == "2":
            logger.info("تم اختيار تشغيل المراقب فقط")
            return run_monitor()
        elif choice == "3":
            logger.info("تم اختيار تشغيل النظام المتكامل")
            asyncio.run(run_both_async())
        else:
            print("❌ اختيار غير صحيح")
            return 1
            
    except KeyboardInterrupt:
        logger.info("🛑 تم إيقاف النظام بواسطة المستخدم")
        return 0
    except Exception as e:
        logger.error(f"❌ خطأ عام في النظام: {str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
