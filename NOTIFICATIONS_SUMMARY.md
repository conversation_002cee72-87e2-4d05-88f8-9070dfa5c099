# 🔔 نظام الإشعارات التلقائية - Terra Bot

## ✅ تم إضافة نظام الإشعارات التلقائية بنجاح!

### 📋 ما تم إضافته:

#### 1. **إعدادات الإشعارات**
```python
NOTIFICATION_CHAT_IDS = [
    "1107000748",      # مستخدم 1
    "1206533289",      # مستخدم 2
    "-1002266485376"   # الجروب
]
ENABLE_NOTIFICATIONS = True  # تفعيل/إلغاء الإشعارات
```

#### 2. **دوال مراقبة قاعدة البيانات**
- ✅ `check_new_customers()` - فحص العملاء الجدد
- ✅ `check_new_previews()` - فحص المعاينات الجديدة
- ✅ `check_new_meetings()` - فحص الاجتماعات الجديدة
- ✅ `check_new_designs()` - فحص التصميمات الجديدة
- ✅ `check_new_contracts()` - فحص العقود الجديدة

#### 3. **دالة إرسال الإشعارات**
```python
async def send_notification(bot, message):
    """إرسال إشعار تلقائي لجميع المستخدمين والجروبات"""
```

#### 4. **مراقب قاعدة البيانات**
```python
async def monitor_database(bot):
    """مراقبة قاعدة البيانات للتغييرات الجديدة"""
```

### 🔔 أنواع الإشعارات:

#### 1. **👤 عميل جديد**
```
🔔 إشعار تلقائي من Terra Bot

👤 عميل جديد تم إضافته

🔢 الكود: 12345
👤 الاسم: أحمد محمد
📱 الهاتف: 01012345678
🏢 الفرع: فرع التجمع
📅 تاريخ الإضافة: 2024-01-15 14:30

⏰ 2024-01-15 14:30:25
```

#### 2. **👁️ معاينة جديدة**
```
🔔 إشعار تلقائي من Terra Bot

👁️ معاينة جديدة تم إضافتها

🔢 كود المعاينة: P12345
👤 العميل: أحمد محمد
📱 الهاتف: 01012345678
🏢 الفرع: فرع التجمع
📅 تاريخ المعاينة: 2024-01-15 15:00

⏰ 2024-01-15 15:00:30
```

#### 3. **🤝 اجتماع جديد**
```
🔔 إشعار تلقائي من Terra Bot

🤝 اجتماع جديد تم إضافته

🔢 كود الاجتماع: M12345
👤 العميل: أحمد محمد
📱 الهاتف: 01012345678
🏢 الفرع: فرع التجمع
📅 تاريخ الاجتماع: 2024-01-15 16:00

⏰ 2024-01-15 16:00:45
```

#### 4. **🎨 تصميم جديد**
```
🔔 إشعار تلقائي من Terra Bot

🎨 تصميم جديد تم إضافته

🔢 كود التصميم: D12345
👤 العميل: أحمد محمد
📱 الهاتف: 01012345678
🏢 الفرع: فرع التجمع
📅 تاريخ التصميم: 2024-01-15 17:00

⏰ 2024-01-15 17:00:15
```

#### 5. **📄 عقد جديد**
```
🔔 إشعار تلقائي من Terra Bot

📄 عقد جديد تم إضافته

🔢 كود العقد: C12345
👤 العميل: أحمد محمد
📱 الهاتف: 01012345678
🏢 الفرع: فرع التجمع
📅 تاريخ العقد: 2024-01-15 18:00

⏰ 2024-01-15 18:00:20
```

### ⚙️ كيفية عمل النظام:

1. **🔍 المراقبة المستمرة**: يفحص النظام قاعدة البيانات كل 30 ثانية
2. **📊 مقارنة البيانات**: يقارن آخر البيانات المضافة مع المحفوظة سابقاً
3. **🔔 إرسال الإشعار**: عند اكتشاف بيانات جديدة، يرسل إشعار فوري
4. **👥 إرسال متعدد**: يرسل الإشعار لجميع المستخدمين والجروبات المحددة

### 🚀 طريقة التشغيل:

#### الطريقة الأولى: ملف التشغيل السريع
```bash
start_terra_with_notifications.bat
```

#### الطريقة الثانية: تشغيل مباشر
```bash
python "terra_bot_clean old.py"
```

### 📊 معلومات تقنية:

- **⏱️ تكرار الفحص**: كل 30 ثانية
- **🔄 إعادة المحاولة**: 60 ثانية في حالة الخطأ
- **🧵 التشغيل**: مراقب قاعدة البيانات يعمل في خيط منفصل
- **📱 التوافق**: يعمل مع المستخدمين والجروبات والقنوات

### 🎯 المستفيدون من الإشعارات:

1. **المستخدم الأول**: `1107000748`
2. **المستخدم الثاني**: `1206533289`
3. **الجروب**: `-1002266485376`

### 🔧 إعدادات متقدمة:

#### تعطيل الإشعارات:
```python
ENABLE_NOTIFICATIONS = False
```

#### إضافة مستخدمين جدد:
```python
NOTIFICATION_CHAT_IDS = [
    "1107000748",
    "1206533289", 
    "-1002266485376",
    "معرف_جديد_هنا"
]
```

#### تغيير تكرار الفحص:
```python
# في دالة monitor_database()
await asyncio.sleep(30)  # غير الرقم حسب الحاجة
```

### ✅ النتيجة النهائية:

🎉 **تم إضافة نظام إشعارات تلقائية متكامل للبوت!**

- ✅ يرسل إشعارات فورية عند أي تغيير في قاعدة البيانات
- ✅ يعمل بشكل مستقل مع البوت
- ✅ يرسل للمستخدمين والجروبات المحددة
- ✅ يتضمن جميع التفاصيل المهمة في كل إشعار
- ✅ يعمل بشكل مستمر ومستقر

**البوت الآن جاهز للعمل مع نظام الإشعارات التلقائية!** 🚀
