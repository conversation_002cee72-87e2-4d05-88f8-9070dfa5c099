#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مراقب تلقائي للعملاء الجدد - يعمل بشكل مستمر
"""

import asyncio
import pyodbc
from datetime import datetime
from telegram import Bot

# إعدادات
BOT_TOKEN = "**********************************************"
CHAT_IDS = ["1107000748", "1206533289"]

DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

# متغير لحفظ آخر عميل
last_customer_id = None

def get_latest_customer():
    """الحصول على آخر عميل"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['user']};PWD={DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT TOP 1 CustomerCode, NameAr, MainPhoneNo, BranchId, AddDate 
            FROM Acc_Customers 
            WHERE IsDeleted = 0 
            ORDER BY AddDate DESC
        """)
        
        result = cursor.fetchone()
        conn.close()
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في قاعدة البيانات: {str(e)}")
        return None

async def send_new_customer_notification(customer):
    """إرسال إشعار عميل جديد"""
    try:
        branch_name = "فرع التجمع" if customer[3] == 3 else "فرع مدينة نصر" if customer[3] == 2 else "غير محدد"
        
        message = f"""🔔 **عميل جديد - Terra Bot**

👤 **تم إضافة عميل جديد الآن!**

🔢 **الكود:** {customer[0]}
👤 **الاسم:** {customer[1] or 'غير محدد'}
📱 **الهاتف:** {customer[2] or 'غير محدد'}
🏢 **الفرع:** {branch_name}
📅 **التاريخ:** {customer[4].strftime('%Y-%m-%d %H:%M') if customer[4] else 'غير محدد'}

⏰ **وقت الإشعار:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
        
        bot = Bot(token=BOT_TOKEN)
        
        for chat_id in CHAT_IDS:
            try:
                await bot.send_message(
                    chat_id=chat_id,
                    text=message,
                    parse_mode='Markdown'
                )
                print(f"✅ تم إرسال إشعار العميل {customer[0]} إلى {chat_id}")
                
            except Exception as e:
                print(f"❌ فشل إرسال إلى {chat_id}: {str(e)}")
        
    except Exception as e:
        print(f"❌ خطأ في إرسال الإشعار: {str(e)}")

async def monitor_customers():
    """مراقبة العملاء الجدد"""
    global last_customer_id
    
    print("🔍 بدء مراقبة العملاء الجدد...")
    
    # الحصول على آخر عميل حالياً
    current_customer = get_latest_customer()
    if current_customer:
        last_customer_id = current_customer[0]
        print(f"📊 آخر عميل حالياً: {last_customer_id}")
    
    cycle = 0
    
    while True:
        try:
            cycle += 1
            current_time = datetime.now().strftime('%H:%M:%S')
            print(f"\n🔄 فحص #{cycle} - {current_time}")
            
            # فحص آخر عميل
            current_customer = get_latest_customer()
            
            if current_customer:
                current_id = current_customer[0]
                
                if current_id != last_customer_id:
                    print(f"🆕 عميل جديد اكتُشف! الكود: {current_id}")
                    
                    # إرسال إشعار
                    await send_new_customer_notification(current_customer)
                    
                    # تحديث آخر عميل
                    last_customer_id = current_id
                    
                    print(f"✅ تم تحديث آخر عميل إلى: {current_id}")
                else:
                    print(f"📊 لا توجد عملاء جدد (آخر عميل: {current_id})")
            else:
                print("⚠️ لم يتم العثور على عملاء")
            
            # انتظار 15 ثانية
            await asyncio.sleep(15)
            
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف المراقبة")
            break
        except Exception as e:
            print(f"❌ خطأ في المراقبة: {str(e)}")
            await asyncio.sleep(30)

if __name__ == "__main__":
    print("🚀 مراقب العملاء الجدد - Terra Bot")
    print("=" * 50)
    print("⏹️ اضغط Ctrl+C لإيقاف المراقبة")
    print("=" * 50)
    
    try:
        asyncio.run(monitor_customers())
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف المراقب")
