#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار إرسال إشعارات للمستخدمين
"""

import asyncio
from datetime import datetime
from telegram import Bot

# إعدادات
BOT_TOKEN = "**********************************************"
USER_CHAT_IDS = [
    "1107000748",
    "1206533289"
]

async def test_user_notifications():
    """اختبار إرسال إشعارات للمستخدمين"""
    print("🧪 اختبار إرسال إشعارات للمستخدمين")
    print("=" * 50)
    
    try:
        bot = Bot(token=BOT_TOKEN)
        
        # اختبار البوت
        print("1️⃣ اختبار البوت...")
        bot_info = await bot.get_me()
        print(f"✅ البوت: {bot_info.first_name} (@{bot_info.username})")
        
        # اختبار إرسال للمستخدمين
        print("\n2️⃣ اختبار إرسال للمستخدمين...")
        
        test_message = f"""🔔 **اختبار نظام الإشعارات - Terra Bot**

👤 **عميل جديد تم إضافته**

🔢 **الكود:** 999
👤 **الاسم:** عميل اختبار النظام
📱 **الهاتف:** 01012345678
🏢 **الفرع:** فرع التجمع
📅 **تاريخ الإضافة:** {datetime.now().strftime('%Y-%m-%d %H:%M')}

⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
        
        success_count = 0
        for chat_id in USER_CHAT_IDS:
            try:
                await bot.send_message(
                    chat_id=chat_id,
                    text=test_message,
                    parse_mode='Markdown'
                )
                print(f"✅ تم إرسال اختبار إلى المستخدم {chat_id}")
                success_count += 1
                
            except Exception as e:
                print(f"❌ فشل إرسال إلى المستخدم {chat_id}: {str(e)}")
        
        print(f"\n📊 النتيجة: تم إرسال {success_count}/{len(USER_CHAT_IDS)} إشعارات بنجاح")
        
        if success_count == len(USER_CHAT_IDS):
            print("🎉 جميع الإشعارات للمستخدمين تعمل بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_user_notifications())
