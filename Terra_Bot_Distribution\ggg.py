#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إشعارات محسن مع تفاصيل شاملة من جميع الجداول المرتبطة
قراءة فقط - لا يكتب أي بيانات في قاعدة البيانات
"""

    #'server': 'hej08pxktvw.sn.mynetname.net',
    #'server': 'localhost',

import asyncio
import pyodbc
import os
import logging
from datetime import datetime
from telegram import Bot

# استيراد الإعدادات - بيانات ثابتة في الكود
print("✅ استخدام الإعدادات الثابتة في الكود")

#8159472977:AAHnzuRjZ_PoaHBgBgzLoacTpzIotxg9Jz8

# إعدادات ثابتة في الكود
#BOT_TOKEN = "7664606990:AAFbBWGShtg00af-qkbCm9VRpvqq7M--bU0"
BOT_TOKEN = "8159472977:AAHnzuRjZ_PoaHBgBgzLoacTpzIotxg9Jz8"
DEFAULT_NOTIFICATION_CHAT_IDS = [
    1107000748,      # مستخدم 1
    1206533289,      # مستخدم 2
    -1002255521584,  # جروب TERRABOT
    -1002308493862   # الجروب الرئيسي
]

# المستخدمون المصرح لهم باستخدام أوامر الاختبار
AUTHORIZED_USERS = [
    1107000748,      # مستخدم 1
    1206533289       # مستخدم 2
]

# إعدادات قاعدة البيانات الثابتة
MAIN_DB_CONFIG = {
    'server': 'hej08pxktvw.sn.mynetname.net',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

NOTIFICATIONS_DB_CONFIG = {
    'server': 'hej08pxktvw.sn.mynetname.net',
    'database': 'Terra_Notifications_DB1',
    'user': 'sa',
    'password': 'Ret_ME@'
}

print("✅ تم تحميل الإعدادات الثابتة")
print(f"🤖 توكن البوت: {BOT_TOKEN[:20]}...")
print(f"🖥️ خادم قاعدة البيانات: {MAIN_DB_CONFIG['server']}")
print(f"👤 المستخدم: {MAIN_DB_CONFIG['user']}")

# متغير عالمي للبوت
telegram_app = None

def get_active_recipients(branch_id=None):
    """الحصول على قائمة المستقبلين النشطين من قاعدة بيانات الإشعارات حسب الفرع"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            print("⚠️ لا يمكن الاتصال بقاعدة بيانات الإشعارات - استخدام القائمة الافتراضية")
            if logger:
                logger.warning("لا يمكن الاتصال بقاعدة بيانات الإشعارات - استخدام القائمة الافتراضية")
            return DEFAULT_NOTIFICATION_CHAT_IDS

        cursor = conn.cursor()

        # بناء الاستعلام حسب الفرع
        if branch_id == 2:  # فرع مدينة نصر
            query = """
                SELECT ChatId, RecipientName, RecipientType
                FROM Notification_Recipients
                WHERE IsActive = 1 AND ReceiveNasrBranch = 1
                ORDER BY RecipientType, RecipientName
            """
        elif branch_id == 3:  # فرع التجمع
            query = """
                SELECT ChatId, RecipientName, RecipientType
                FROM Notification_Recipients
                WHERE IsActive = 1 AND ReceiveTajammuBranch = 1
                ORDER BY RecipientType, RecipientName
            """
        else:  # جميع المستقبلين النشطين (للرسائل العامة)
            query = """
                SELECT ChatId, RecipientName, RecipientType
                FROM Notification_Recipients
                WHERE IsActive = 1
                ORDER BY RecipientType, RecipientName
            """

        cursor.execute(query)
        results = cursor.fetchall()
        conn.close()

        if results:
            chat_ids = [row[0] for row in results]
            branch_text = ""
            if branch_id == 2:
                branch_text = " (فرع مدينة نصر)"
            elif branch_id == 3:
                branch_text = " (فرع التجمع)"

            print(f"📋 تم تحميل {len(chat_ids)} مستقبل نشط من قاعدة البيانات{branch_text}:")
            for row in results:
                recipient_type = "👤 مستخدم" if row[2] == 'User' else "👥 جروب"
                print(f"   {recipient_type}: {row[1]} ({row[0]})")

            if logger:
                logger.info(f"📋 تم تحميل {len(chat_ids)} مستقبل نشط من قاعدة البيانات{branch_text}")

            return chat_ids
        else:
            print(f"⚠️ لا توجد مستقبلين نشطين في قاعدة البيانات للفرع {branch_id} - استخدام القائمة الافتراضية")
            if logger:
                logger.warning(f"لا توجد مستقبلين نشطين في قاعدة البيانات للفرع {branch_id} - استخدام القائمة الافتراضية")
            return DEFAULT_NOTIFICATION_CHAT_IDS

    except Exception as e:
        error_msg = f"❌ خطأ في تحميل المستقبلين من قاعدة البيانات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return DEFAULT_NOTIFICATION_CHAT_IDS

# تم نقل إعدادات قاعدة البيانات إلى ملف config.py

# تم حذف المتغيرات غير المستخدمة

# ملف Log لحفظ الأخطاء والأحداث
LOG_FILE = "terra_bot.log"

# إعداد نظام Logging
def setup_logging():
    """إعداد نظام logging لحفظ الأخطاء والأحداث"""
    try:
        # إنشاء logger
        logger = logging.getLogger('TerraBot')
        logger.setLevel(logging.DEBUG)

        # إزالة handlers القديمة إذا وجدت
        for handler in logger.handlers[:]:
            logger.removeHandler(handler)

        # إنشاء formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )

        # إنشاء file handler
        file_handler = logging.FileHandler(LOG_FILE, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        file_handler.setFormatter(formatter)

        # إنشاء console handler
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        console_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        console_handler.setFormatter(console_formatter)

        # إضافة handlers للlogger
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)

        return logger

    except Exception as e:
        print(f"❌ خطأ في إعداد نظام Logging: {str(e)}")
        return None

# إنشاء logger عام
logger = setup_logging()

def connect_to_main_db():
    """الاتصال بقاعدة البيانات الأصلية (Terra) - قراءة فقط"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={MAIN_DB_CONFIG['server']};DATABASE={MAIN_DB_CONFIG['database']};UID={MAIN_DB_CONFIG['user']};PWD={MAIN_DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        if logger:
            logger.info("✅ تم الاتصال بقاعدة البيانات الأصلية بنجاح")
        return conn
    except Exception as e:
        error_msg = f"❌ خطأ في الاتصال بقاعدة البيانات الأصلية: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

def connect_to_notifications_db():
    """الاتصال بقاعدة بيانات الإشعارات (Terra_Notifications_DB1) - قراءة وكتابة"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={NOTIFICATIONS_DB_CONFIG['server']};DATABASE={NOTIFICATIONS_DB_CONFIG['database']};UID={NOTIFICATIONS_DB_CONFIG['user']};PWD={NOTIFICATIONS_DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        if logger:
            logger.info("✅ تم الاتصال بقاعدة بيانات الإشعارات بنجاح")
        return conn
    except Exception as e:
        error_msg = f"❌ خطأ في الاتصال بقاعدة بيانات الإشعارات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

# للتوافق مع الكود القديم
def connect_to_db():
    """الاتصال بقاعدة البيانات الأصلية - للتوافق مع الكود القديم"""
    return connect_to_main_db()

def init_notifications_db():
    """التحقق من وجود جداول قاعدة بيانات الإشعارات وإنشاؤها إذا لزم الأمر"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return False

        cursor = conn.cursor()

        # فحص وجود جدول سجل الإشعارات
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Notifications_Log' AND xtype='U')
            BEGIN
                CREATE TABLE Notifications_Log (
                    LogId INT IDENTITY(1,1) PRIMARY KEY,
                    DataType NVARCHAR(50) NOT NULL,
                    RecordId INT NOT NULL,
                    RecordName NVARCHAR(255),
                    BranchId INT,
                    BranchName NVARCHAR(100),
                    RecordUniqueId INT,
                    AddDate DATETIME,
                    AddedBy NVARCHAR(100),
                    NotificationDate DATETIME DEFAULT GETDATE(),
                    NotificationStatus NVARCHAR(20) DEFAULT N'تم الإرسال',
                    IsActive BIT DEFAULT 1,
                    Notes NVARCHAR(500),
                    CONSTRAINT UK_Notifications_DataType_RecordId_Branch UNIQUE(DataType, RecordId, BranchId)
                );

                CREATE INDEX IX_Notifications_DataType ON Notifications_Log(DataType);
                CREATE INDEX IX_Notifications_RecordId ON Notifications_Log(RecordId);
                CREATE INDEX IX_Notifications_Date ON Notifications_Log(NotificationDate);
                CREATE INDEX IX_Notifications_Branch ON Notifications_Log(BranchId);
            END

            -- إضافة الأعمدة الجديدة إذا لم تكن موجودة
            IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Notifications_Log' AND COLUMN_NAME = 'BranchId')
            BEGIN
                ALTER TABLE Notifications_Log ADD BranchId INT;
            END

            IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_NAME = 'Notifications_Log' AND COLUMN_NAME = 'RecordUniqueId')
            BEGIN
                ALTER TABLE Notifications_Log ADD RecordUniqueId INT;
            END
        """)

        # فحص وجود جدول المستقبلين
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Notification_Recipients' AND xtype='U')
            BEGIN
                CREATE TABLE Notification_Recipients (
                    RecipientId INT IDENTITY(1,1) PRIMARY KEY,
                    ChatId BIGINT NOT NULL UNIQUE,
                    RecipientName NVARCHAR(100) NOT NULL,
                    RecipientType NVARCHAR(20) NOT NULL CHECK (RecipientType IN ('User', 'Group')),
                    IsActive BIT DEFAULT 1,
                    AddDate DATETIME DEFAULT GETDATE(),
                    Notes NVARCHAR(500),
                    ReceiveNasrBranch BIT DEFAULT 1,
                    ReceiveTajammuBranch BIT DEFAULT 1,
                    ReceiveReports BIT DEFAULT 1
                );

                CREATE INDEX IX_Recipients_ChatId ON Notification_Recipients(ChatId);
                CREATE INDEX IX_Recipients_Active ON Notification_Recipients(IsActive);

                -- إدراج المستقبلين الافتراضيين من ملف الإعدادات
                -- سيتم إدراجهم تلقائٍ عند أول تشغيل إذا لم يكونوا موجودين
            END
        """)

        # فحص وجود جدول التقارير المجدولة
        print("🔄 فحص جدول التقارير المجدولة...")
        try:
            cursor.execute("""
                IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Scheduled_Reports' AND xtype='U')
                BEGIN
                    CREATE TABLE Scheduled_Reports (
                        ReportId INT IDENTITY(1,1) PRIMARY KEY,
                        ReportName NVARCHAR(100) NOT NULL,
                        ReportType NVARCHAR(50) NOT NULL,
                        ScheduleType NVARCHAR(20) NOT NULL,
                        IntervalValue INT NOT NULL DEFAULT 1,
                        DaysToInclude INT NOT NULL DEFAULT 1,
                        LastRunTime DATETIME NULL,
                        NextRunTime DATETIME NOT NULL,
                        IsActive BIT DEFAULT 1,
                        BranchIds NVARCHAR(100) NULL,
                        AddDate DATETIME DEFAULT GETDATE(),
                        Notes NVARCHAR(500)
                    );
                END
            """)
            conn.commit()
            print("✅ تم إنشاء جدول Scheduled_Reports")

            # إنشاء الفهارس
            cursor.execute("""
                IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ScheduledReports_NextRun')
                BEGIN
                    CREATE INDEX IX_ScheduledReports_NextRun ON Scheduled_Reports(NextRunTime, IsActive);
                END
            """)

            cursor.execute("""
                IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_ScheduledReports_Type')
                BEGIN
                    CREATE INDEX IX_ScheduledReports_Type ON Scheduled_Reports(ReportType, IsActive);
                END
            """)
            conn.commit()
            print("✅ تم إنشاء فهارس جدول التقارير")

            # إدراج التقارير الافتراضية
            cursor.execute("SELECT COUNT(*) FROM Scheduled_Reports")
            count = cursor.fetchone()[0]
            if count == 0:
                cursor.execute("""
                    INSERT INTO Scheduled_Reports (ReportName, ReportType, ScheduleType, IntervalValue, DaysToInclude, NextRunTime, BranchIds, Notes)
                    VALUES
                        (N'تقرير إحصائي يومي', 'statistics', 'daily', 1, 1, DATEADD(HOUR, 1, GETDATE()), NULL, N'تقرير يومي شامل لجميع الفروع'),
                        (N'تقرير إحصائي أسبوعي', 'statistics', 'weekly', 1, 7, DATEADD(DAY, 1, GETDATE()), NULL, N'تقرير أسبوعي شامل لجميع الفروع'),
                        (N'تقرير فرع مدينة نصر', 'statistics', 'daily', 1, 1, DATEADD(HOUR, 2, GETDATE()), '2', N'تقرير يومي لفرع مدينة نصر فقط'),
                        (N'تقرير فرع التجمع', 'statistics', 'daily', 1, 1, DATEADD(HOUR, 2, GETDATE()), '3', N'تقرير يومي لفرع التجمع فقط')
                """)
                conn.commit()
                print("✅ تم إدراج التقارير الافتراضية")
            else:
                print(f"ℹ️ يوجد {count} تقرير مجدول بالفعل")

        except Exception as e:
            print(f"❌ خطأ في إنشاء جدول التقارير: {str(e)}")
            if logger:
                logger.error(f"خطأ في إنشاء جدول التقارير: {str(e)}")

        # فحص وجود جدول إعدادات الفروع للتقارير
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Branch_Report_Settings' AND xtype='U')
            BEGIN
                CREATE TABLE Branch_Report_Settings (
                    SettingId INT IDENTITY(1,1) PRIMARY KEY,
                    BranchId INT NOT NULL,
                    BranchName NVARCHAR(100) NOT NULL,
                    IsActiveForReports BIT DEFAULT 1,
                    ReceiveDailyReports BIT DEFAULT 1,
                    ReceiveWeeklyReports BIT DEFAULT 1,
                    ReceiveMonthlyReports BIT DEFAULT 1,
                    ReportRecipients NVARCHAR(500) NULL, -- معرفات المستقبلين مفصولة بفاصلة (NULL = جميع المستقبلين)
                    AddDate DATETIME DEFAULT GETDATE(),
                    UpdateDate DATETIME NULL,
                    Notes NVARCHAR(500)
                );

                CREATE INDEX IX_BranchReportSettings_Branch ON Branch_Report_Settings(BranchId, IsActiveForReports);

                -- إدراج إعدادات افتراضية للفروع
                INSERT INTO Branch_Report_Settings (BranchId, BranchName, IsActiveForReports, Notes)
                VALUES
                    (2, N'مدينة نصر', 1, N'فرع مدينة نصر - نشط للتقارير'),
                    (3, N'التجمع', 1, N'فرع التجمع - نشط للتقارير');
            END
        """)

        conn.commit()

        # التحقق من وجود الجداول الجديدة
        print("🔍 التحقق من الجداول الجديدة...")
        try:
            # فحص جدول التقارير المجدولة
            cursor.execute("SELECT COUNT(*) FROM Scheduled_Reports")
            reports_count = cursor.fetchone()[0]
            print(f"📊 جدول التقارير المجدولة: {reports_count} تقرير")

            # فحص جدول إعدادات الفروع
            cursor.execute("SELECT COUNT(*) FROM Branch_Report_Settings")
            settings_count = cursor.fetchone()[0]
            print(f"🏢 جدول إعدادات الفروع: {settings_count} إعداد")

            # عرض التقارير المجدولة
            cursor.execute("SELECT ReportName, ScheduleType, IsActive FROM Scheduled_Reports")
            reports = cursor.fetchall()
            print("📋 التقارير المجدولة:")
            for report in reports:
                status = "✅ نشط" if report[2] else "❌ معطل"
                print(f"   - {report[0]} ({report[1]}) - {status}")

        except Exception as e:
            print(f"⚠️ تحذير: خطأ في فحص الجداول الجديدة: {str(e)}")

        # إدراج المستقبلين الافتراضيين من ملف الإعدادات إذا لم يكونوا موجودين
        try:
            for chat_id in DEFAULT_NOTIFICATION_CHAT_IDS:
                cursor.execute("""
                    IF NOT EXISTS (SELECT 1 FROM Notification_Recipients WHERE ChatId = ?)
                    BEGIN
                        INSERT INTO Notification_Recipients (ChatId, RecipientName, RecipientType, IsActive)
                        VALUES (?, N'مستقبل افتراضي', CASE WHEN ? < 0 THEN 'Group' ELSE 'User' END, 1)
                    END
                """, (chat_id, chat_id, chat_id))
            conn.commit()
        except Exception as e:
            print(f"⚠️ تحذير: لم يتم إدراج المستقبلين الافتراضيين: {str(e)}")

        conn.close()

        success_msg = "✅ تم التحقق من جداول قاعدة بيانات الإشعارات والتقارير المجدولة"
        print(success_msg)
        if logger:
            logger.info(success_msg)
        return True

    except Exception as e:
        error_msg = f"❌ خطأ في إنشاء جداول قاعدة بيانات الإشعارات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return False

def save_notification_to_db(data_type, code, name, branch_id, branch_name, unique_id, add_date, added_by):
    """حفظ بيانات الإشعار في قاعدة بيانات الإشعارات"""
    try:
        print(f"🔄 محاولة حفظ {data_type} - كود {code} - معرف فريد {unique_id}")

        conn = connect_to_notifications_db()
        if not conn:
            print("❌ فشل الاتصال بقاعدة بيانات الإشعارات")
            return False

        cursor = conn.cursor()

        # التحقق أولاً من وجود السجل لتجنب التكرار
        print(f"🔍 فحص وجود السجل: {data_type} - معرف فريد {unique_id}")
        cursor.execute("""
            SELECT COUNT(*) FROM Notifications_Log
            WHERE DataType = ? AND RecordUniqueId = ?
        """, (data_type, unique_id))

        result = cursor.fetchone()
        if result and result[0] > 0:
            # السجل موجود بالفعل
            print(f"⚠️ السجل موجود بالفعل: {data_type} - معرف فريد {unique_id}")
            conn.close()
            return True

        # إدراج السجل الجديد
        print(f"💾 إدراج سجل جديد: {data_type} - كود {code}")
        cursor.execute("""
            INSERT INTO Notifications_Log
            (DataType, RecordId, RecordName, BranchId, BranchName, RecordUniqueId, AddDate, AddedBy, NotificationDate, NotificationStatus)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, GETDATE(), N'تم الإرسال')
        """, (data_type, code, name, branch_id, branch_name, unique_id, add_date, added_by))

        conn.commit()
        conn.close()

        success_msg = f"✅ تم حفظ {data_type} - كود {code} - معرف فريد {unique_id} في قاعدة بيانات الإشعارات"
        print(success_msg)
        if logger:
            logger.info(success_msg)
        return True

    except Exception as e:
        # تجاهل أخطاء UNIQUE KEY constraint لأنها تعني أن السجل موجود بالفعل
        if "UNIQUE KEY constraint" in str(e) or "2627" in str(e):
            print(f"⚠️ السجل موجود بالفعل (UNIQUE constraint): {data_type} - معرف فريد {unique_id}")
            return True

        error_msg = f"❌ خطأ في حفظ البيانات في قاعدة بيانات الإشعارات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return False





# تم حذف دالة load_last_ids_from_db() غير المستخدمة

def get_statistics_report(days=1, branch_ids=None):
    """الحصول على التقرير الإحصائي الشامل"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # تحديد فلتر الفروع
        branch_filter = ""
        if branch_ids:
            if isinstance(branch_ids, str):
                branch_list = [x.strip() for x in branch_ids.split(',') if x.strip()]
                if branch_list:
                    branch_filter = f" AND BranchId IN ({','.join(branch_list)})"
            elif isinstance(branch_ids, list):
                if branch_ids:
                    branch_filter = f" AND BranchId IN ({','.join(map(str, branch_ids))})"

        # استعلام التقرير الإحصائي الشامل
        query = f"""
        DECLARE @Days INT = ?;

        -- العملاء
        WITH CustomersSummary AS (
            SELECT BranchId, COUNT(*) AS CustomersCount
            FROM Acc_Customers
            WHERE AddDate >= DATEADD(DAY, -@Days, CAST(GETDATE() AS DATE))
              AND AddDate < DATEADD(DAY, 1, CAST(GETDATE() AS DATE))
              AND IsDeleted = 0{branch_filter}
            GROUP BY BranchId
        ),
        -- المعاينات
        PreviewsSummary AS (
            SELECT
                BranchId,
                COUNT(*) AS PreviewsCount,
                SUM(ISNULL(TotalValue, 0)) AS PreviewsTotalValue,
                COUNT(CASE WHEN ISNULL(TotalValue, 0) > 0 THEN 1 END) AS PreviewsPaidCount,
                COUNT(CASE WHEN ISNULL(TotalValue, 0) = 0 THEN 1 END) AS PreviewsFreeCount
            FROM Sys_Previews
            WHERE AddDate >= DATEADD(DAY, -@Days, CAST(GETDATE() AS DATE))
              AND AddDate < DATEADD(DAY, 1, CAST(GETDATE() AS DATE))
              AND IsDeleted = 0{branch_filter}
            GROUP BY BranchId
        ),
        -- الاجتماعات
        MeetingsSummary AS (
            SELECT BranchId, COUNT(*) AS MeetingsCount
            FROM Sys_Meetings
            WHERE AddDate >= DATEADD(DAY, -@Days, CAST(GETDATE() AS DATE))
              AND AddDate < DATEADD(DAY, 1, CAST(GETDATE() AS DATE))
              AND IsDeleted = 0{branch_filter}
            GROUP BY BranchId
        ),
        -- التصميمات
        DesignsSummary AS (
            SELECT BranchId, COUNT(*) AS DesignsCount
            FROM Sys_Designs
            WHERE AddDate >= DATEADD(DAY, -@Days, CAST(GETDATE() AS DATE))
              AND AddDate < DATEADD(DAY, 1, CAST(GETDATE() AS DATE))
              AND IsDeleted = 0{branch_filter}
            GROUP BY BranchId
        ),
        -- العقود
        ContractsSummary AS (
            SELECT 
                BranchId,
                COUNT(*) AS ContractsCount,
                SUM(ISNULL(TotalValue, 0) + ISNULL(DiscountValue, 0)) AS ContractsTotalValue,
                SUM(ISNULL(DiscountValue, 0)) AS ContractsTotalDiscount,
                SUM(ISNULL(TotalValue, 0)) AS ContractsNetTotal
            FROM Acc_Contracts
            WHERE AddDate >= DATEADD(DAY, -@Days, CAST(GETDATE() AS DATE))
              AND AddDate < DATEADD(DAY, 1, CAST(GETDATE() AS DATE))
              AND IsDeleted = 0
            GROUP BY BranchId
        ),
        OffersSummary AS (
            SELECT 
                BranchId,
                COUNT(*) AS OffersCount,
                SUM(ISNULL(TotalValue, 0) + ISNULL(DiscountValue, 0)) AS OffersTotalValue,
                SUM(ISNULL(DiscountValue, 0)) AS OffersTotalDiscount,
                SUM(ISNULL(TotalValue, 0)) AS OffersNetTotal
            FROM Acc_Offers
            WHERE AddDate >= DATEADD(DAY, -@Days, CAST(GETDATE() AS DATE))
              AND AddDate < DATEADD(DAY, 1, CAST(GETDATE() AS DATE))
              AND IsDeleted = 0
            GROUP BY BranchId
        )

        -- التجميع النهائي
        SELECT
            B.BranchId,
            B.NameAr AS BranchName,
            ISNULL(CS.CustomersCount, 0) AS CustomersCount,
            ISNULL(PS.PreviewsCount, 0) AS PreviewsCount,
            ISNULL(PS.PreviewsTotalValue, 0) AS PreviewsTotalValue,
            ISNULL(PS.PreviewsPaidCount, 0) AS PreviewsPaidCount,
            ISNULL(PS.PreviewsFreeCount, 0) AS PreviewsFreeCount,
            ISNULL(MS.MeetingsCount, 0) AS MeetingsCount,
            ISNULL(DS.DesignsCount, 0) AS DesignsCount,
            ISNULL(CTS.ContractsCount, 0) AS ContractsCount,
            ISNULL(CTS.ContractsTotalValue, 0) AS ContractsTotalValue,
            ISNULL(CTS.ContractsTotalDiscount, 0) AS ContractsTotalDiscount,
            ISNULL(CTS.ContractsNetTotal, 0) AS ContractsNetTotal,
            ISNULL(OS.OffersCount, 0) AS OffersCount,
            ISNULL(OS.OffersTotalValue, 0) AS OffersTotalValue,
            ISNULL(OS.OffersTotalDiscount, 0) AS OffersTotalDiscount,
            ISNULL(OS.OffersNetTotal, 0) AS OffersNetTotal

        FROM Sys_Branches B
        LEFT JOIN CustomersSummary CS ON B.BranchId = CS.BranchId
        LEFT JOIN PreviewsSummary PS ON B.BranchId = PS.BranchId
        LEFT JOIN MeetingsSummary MS ON B.BranchId = MS.BranchId
        LEFT JOIN DesignsSummary DS ON B.BranchId = DS.BranchId
        LEFT JOIN ContractsSummary CTS ON B.BranchId = CTS.BranchId
        LEFT JOIN OffersSummary OS ON B.BranchId = OS.BranchId

        WHERE B.IsDeleted = 0

        ORDER BY B.NameAr
        """

        cursor.execute(query, (days,))
        results = cursor.fetchall()
        conn.close()

        return results

    except Exception as e:
        print(f"❌ خطأ في الحصول على التقرير الإحصائي: {str(e)}")
        if logger:
            logger.error(f"خطأ في الحصول على التقرير الإحصائي: {str(e)}")
        return None

def format_statistics_report(data, days, report_name=None, branch_ids=None):
    """تنسيق التقرير الإحصائي للإرسال"""
    if not data:
        return "❌ لا توجد بيانات للعرض"

    # حساب الإجماليات
    total_customers = sum(row[2] for row in data)
    total_previews = sum(row[3] for row in data)
    total_previews_value = sum(row[4] for row in data)
    total_meetings = sum(row[7] for row in data)
    total_designs = sum(row[8] for row in data)
    total_contracts = sum(row[9] for row in data)
    total_contracts_value = sum(row[10] for row in data)
    total_offers = sum(row[13] for row in data)
    total_offers_value = sum(row[14] for row in data)

    # عنوان التقرير
    title = report_name or f"تقرير إحصائي شامل - آخر {days} أيام"

    # معلومات الفروع المحددة
    branch_info = ""
    if branch_ids:
        if isinstance(branch_ids, str):
            branch_list = [x.strip() for x in branch_ids.split(',') if x.strip()]
            branch_info = f"\n🏢 الفروع المحددة: {', '.join(branch_list)}"
        elif isinstance(branch_ids, list):
            branch_info = f"\n🏢 الفروع المحددة: {', '.join(map(str, branch_ids))}"
    else:
        branch_info = "\n🏢 جميع الفروع"

    # تنسيق التقرير
    report = f"""📊 **{title}**
📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}{branch_info}

═══════════════════════════════════

📈 **الإجماليات العامة:**
👥 العملاء الجدد: {total_customers:,}
👁️ المعاينات: {total_previews:,} (قيمة: {total_previews_value:,.0f} جنيه)
🤝 الاجتماعات: {total_meetings:,}
🎨 التصميمات: {total_designs:,}
📋 العقود: {total_contracts:,} (قيمة: {total_contracts_value:,.0f} جنيه)
💼 العروض: {total_offers:,} (قيمة: {total_offers_value:,.0f} جنيه)

═══════════════════════════════════

🏢 **تفاصيل الفروع:**

"""

    for row in data:
        branch_name = row[1]
        customers = row[2]
        previews = row[3]
        previews_value = row[4]
        previews_paid = row[5]
        previews_free = row[6]
        meetings = row[7]
        designs = row[8]
        contracts = row[9]
        contracts_value = row[10]
        contracts_discount = row[11]
        contracts_net = row[12]
        offers = row[13]
        offers_value = row[14]
        offers_discount = row[15]
        offers_net = row[16]

        # تخطي الفروع التي لا تحتوي على أي بيانات
        if (customers + previews + meetings + designs + contracts + offers) == 0:
            continue

        report += f"""🏪 **{branch_name}**
├─ 👥 عملاء جدد: {customers:,}
├─ 👁 معاينات: {previews:,} (مدفوعة: {previews_paid}, مجانية: {previews_free})
│   💰 قيمة المعاينات: {previews_value:,.0f} جنيه
├─ 🤝 اجتماعات: {meetings:,}
├─ 🎨 تصميمات: {designs:,}
├─ 📋 عقود: {contracts:,}
│   💰 اجمالى العقود: {contracts_value:,.0f} جنيه
│   🏷 اجمالي خصم العقود: {contracts_discount:,.0f} جنيه
│   💵 صافي العقود: {contracts_net:,.0f} جنيه
└─ 💼 عدد طلب التسعير: {offers:,}
    💰 اجمالي طلب التسعير: {offers_value:,.0f} جنيه
    🏷 اجمالي خصومات طلب التسعير: {offers_discount:,.0f} جنيه
    💵 صافي طلبات التسعير: {offers_net:,.0f} جنيه

"""

    report += f"""
═══════════════════════════════════
⏰ تم إنشاء التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🤖 Terra Bot - نظام التقارير التلقائي"""

    return report

def send_custom_statistical_report(days_to_include, target_branches=None, report_name="التقرير الإحصائي المخصص"):
    """إرسال تقرير إحصائي مخصص"""
    try:
        # الحصول على بيانات التقرير الإحصائي
        data = get_statistics_report(days_to_include, target_branches)
        if data:
            # تنسيق التقرير مع اسم مخصص
            formatted_report = format_statistics_report(data, days_to_include, report_name, target_branches)

            # إرسال التقرير
            send_notification_sync(formatted_report)

            print(f"✅ تم إرسال {report_name} بنجاح")
            return True
        else:
            print(f"⚠️ لا توجد بيانات للتقرير: {report_name}")
            return False
    except Exception as e:
        print(f"❌ خطأ في إرسال التقرير {report_name}: {str(e)}")
        return False

def update_statistical_report_status(report_id):
    """تحديث حالة إرسال التقرير الإحصائي"""
    try:
        notifications_conn = connect_to_notifications_db()
        if not notifications_conn:
            return False

        cursor = notifications_conn.cursor()

        # تحديث حالة الإرسال والمواعيد
        cursor.execute("""
            UPDATE Statistical_Reports_Config
            SET SendStatus = N'تم الإرسال',
                LastSentTime = GETDATE(),
                NextSendTime = CASE
                    WHEN ScheduleType = 'daily' THEN DATEADD(DAY, IntervalDays, NextSendTime)
                    WHEN ScheduleType = 'weekly' THEN DATEADD(WEEK, IntervalDays, NextSendTime)
                    WHEN ScheduleType = 'monthly' THEN DATEADD(MONTH, IntervalDays, NextSendTime)
                    WHEN ScheduleType = 'hourly' THEN DATEADD(HOUR, IntervalDays, NextSendTime)
                    WHEN ScheduleType = 'custom' THEN DATEADD(MINUTE, IntervalDays, GETDATE())
                    ELSE DATEADD(DAY, IntervalDays, NextSendTime)
                END,
                UpdatedDate = GETDATE()
            WHERE ReportId = ?
        """, (report_id,))

        notifications_conn.commit()
        notifications_conn.close()

        print(f"✅ تم تحديث حالة التقرير {report_id}")
        return True

    except Exception as e:
        print(f"❌ خطأ في تحديث حالة التقرير {report_id}: {str(e)}")
        return False

# تم حذف دالة send_quarter_hourly_report() لأن QUARTER_HOURLY_STATS
# يستخدم الآن send_custom_statistical_report() مثل باقي التقارير

def check_scheduled_reports():
    """فحص التقارير المجدولة وإرسال التقارير المستحقة"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return

        cursor = conn.cursor()

        # التحقق من وجود الجداول المختلفة
        use_statistical_config = False
        use_new_structure = False

        # فحص جدول التقارير الإحصائية المخصصة
        try:
            cursor.execute("SELECT COUNT(*) FROM Statistical_Reports_Config WHERE 1=0")
            use_statistical_config = True
        except:
            pass

        # فحص الجداول الجديدة العامة
        if not use_statistical_config:
            try:
                cursor.execute("SELECT COUNT(*) FROM Report_Schedules WHERE 1=0")
                use_new_structure = True
            except:
                use_new_structure = False

        if use_statistical_config:
            # استخدام جدول التقارير الإحصائية المخصصة
            print("🔍 فحص جدول التقارير الإحصائية المخصصة...")

            # أولاً: عرض جميع التقارير للتشخيص
            cursor.execute("""
                SELECT
                    ReportId,
                    ReportName,
                    NextSendTime,
                    IsActive,
                    CASE WHEN NextSendTime <= GETDATE() THEN 'مستحق' ELSE 'ليس مستحق' END as Status
                FROM Statistical_Reports_Config
                ORDER BY NextSendTime
            """)

            all_reports = cursor.fetchall()
            print(f"📊 إجمالي التقارير: {len(all_reports)}")
            for report in all_reports:
                print(f"   {report[0]} - {report[1]} | {report[2]} | {'نشط' if report[3] else 'معطل'} | {report[4]}")

            # ثانياً: البحث عن التقارير المستحقة
            cursor.execute("""
                SELECT
                    ReportId,
                    ReportName,
                    ReportCode,
                    ScheduleType,
                    IntervalDays,
                    StatisticsPeriodDays,
                    BranchIds,
                    Notes,
                    ReportName
                FROM Statistical_Reports_Config
                WHERE IsActive = 1 AND NextSendTime <= GETDATE()
                ORDER BY NextSendTime
            """)
        elif use_new_structure:
            # استخدام الهيكل الجديد
            cursor.execute("""
                SELECT
                    s.ScheduleId,
                    s.ScheduleName,
                    t.ReportCode,
                    s.ScheduleType,
                    s.IntervalValue,
                    s.DaysToInclude,
                    s.BranchIds,
                    s.Notes,
                    t.ReportNameAr
                FROM Report_Schedules s
                INNER JOIN Report_Types t ON s.ReportTypeId = t.ReportTypeId
                WHERE s.IsActive = 1 AND s.NextRunTime <= GETDATE()
                ORDER BY s.NextRunTime
            """)
        else:
            # استخدام الهيكل القديم
            cursor.execute("""
                SELECT ReportId, ReportName, ReportType, ScheduleType, IntervalValue,
                       DaysToInclude, BranchIds, Notes, ReportName
                FROM Scheduled_Reports
                WHERE IsActive = 1 AND NextRunTime <= GETDATE()
                ORDER BY NextRunTime
            """)

        scheduled_reports = cursor.fetchall()
        print(f"⏰ التقارير المستحقة للإرسال: {len(scheduled_reports)}")

        for report in scheduled_reports:
            if use_statistical_config:
                # جدول التقارير الإحصائية المخصصة
                report_id = report[0]
                report_name = report[1]
                report_code = report[2]
                schedule_type = report[3]
                interval_value = report[4]
                days_to_include = report[5]
                branch_ids = report[6]
                # notes = report[7]  # متاح للاستخدام المستقبلي
                report_type_name = report[8]

                print(f"📊 تنفيذ التقرير المجدول: {report_name}")

                try:
                    # تحديد الفروع المطلوبة
                    if branch_ids:
                        target_branches = [int(x.strip()) for x in branch_ids.split(',') if x.strip().isdigit()]
                    else:
                        target_branches = None  # جميع الفروع

                    # تنفيذ التقرير حسب النوع
                    if report_code in ['CUSTOM_STATS_1', 'CUSTOM_STATS_5', 'WEEKLY_STATS', 'MONTHLY_STATS', 'QUARTER_HOURLY_STATS']:
                        send_custom_statistical_report(days_to_include, target_branches, report_name)
                    else:
                        print(f"⚠️ نوع تقرير غير معروف: {report_code}")
                        continue

                    # تحديث حالة الإرسال
                    update_statistical_report_status(report_id)

                    print(f"✅ تم إرسال التقرير: {report_name}")
                    if logger:
                        logger.info(f"تم إرسال التقرير المجدول: {report_name} (كود: {report_code})")

                except Exception as e:
                    error_msg = f"❌ خطأ في تنفيذ التقرير {report_name}: {str(e)}"
                    print(error_msg)
                    if logger:
                        logger.error(error_msg)

            elif use_new_structure:
                # الهيكل الجديد
                schedule_id = report[0]
                schedule_name = report[1]
                report_code = report[2]
                schedule_type = report[3]
                interval_value = report[4]
                days_to_include = report[5]
                branch_ids = report[6]
                notes = report[7]
                report_type_name = report[8]

                print(f"📊 تنفيذ التقرير المجدول: {schedule_name}")

                try:
                    # تنفيذ التقرير حسب الكود
                    if report_code in ['DAILY_STATS', 'WEEKLY_STATS', 'MONTHLY_STATS', 'CUSTOM_STATS', 'BRANCH_DAILY', 'PERFORMANCE_WEEKLY']:
                        # الحصول على بيانات التقرير الإحصائي
                        data = get_statistics_report(days_to_include, branch_ids)
                        if data:
                            # تنسيق التقرير مع اسم مخصص
                            custom_title = f"{report_type_name} - {schedule_name}"
                            formatted_report = format_statistics_report(data, days_to_include, custom_title, branch_ids)

                            # إرسال التقرير
                            send_notification_sync(formatted_report)

                            print(f"✅ تم إرسال التقرير: {schedule_name}")
                            if logger:
                                logger.info(f"تم إرسال التقرير المجدول: {schedule_name} (كود: {report_code})")
                        else:
                            print(f"⚠️ لا توجد بيانات للتقرير: {schedule_name}")
                    else:
                        print(f"⚠️ نوع تقرير غير مدعوم: {report_code}")

                    # تحديث موعد التشغيل التالي
                    update_next_run_time_new(schedule_id, schedule_type, interval_value)

                except Exception as e:
                    error_msg = f"❌ خطأ في تنفيذ التقرير {schedule_name}: {str(e)}"
                    print(error_msg)
                    if logger:
                        logger.error(error_msg)
            else:
                # الهيكل القديم
                report_id = report[0]
                report_name = report[1]
                report_type = report[2]
                schedule_type = report[3]
                interval_value = report[4]
                days_to_include = report[5]
                branch_ids = report[6]
                notes = report[7]

                print(f"📊 تنفيذ التقرير المجدول: {report_name}")

                try:
                    # تنفيذ التقرير حسب النوع
                    if report_type == 'statistics':
                        # الحصول على بيانات التقرير الإحصائي
                        data = get_statistics_report(days_to_include, branch_ids)
                        if data:
                            # تنسيق التقرير
                            formatted_report = format_statistics_report(data, days_to_include, report_name, branch_ids)

                            # إرسال التقرير
                            send_notification_sync(formatted_report)

                            print(f"✅ تم إرسال التقرير: {report_name}")
                            if logger:
                                logger.info(f"تم إرسال التقرير المجدول: {report_name}")
                        else:
                            print(f"⚠️ لا توجد بيانات للتقرير: {report_name}")

                    # تحديث موعد التشغيل التالي
                    update_next_run_time(report_id, schedule_type, interval_value)

                except Exception as e:
                    error_msg = f"❌ خطأ في تنفيذ التقرير {report_name}: {str(e)}"
                    print(error_msg)
                    if logger:
                        logger.error(error_msg)

        conn.close()

        if scheduled_reports:
            print(f"📊 تم فحص {len(scheduled_reports)} تقرير مجدول")

    except Exception as e:
        error_msg = f"❌ خطأ في فحص التقارير المجدولة: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)

def update_next_run_time(report_id, schedule_type, interval_value):
    """تحديث موعد التشغيل التالي للتقرير"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return

        cursor = conn.cursor()

        # حساب الموعد التالي حسب نوع الجدولة
        if schedule_type == 'hourly':
            next_run_sql = f"DATEADD(HOUR, {interval_value}, GETDATE())"
        elif schedule_type == 'daily':
            next_run_sql = f"DATEADD(DAY, {interval_value}, GETDATE())"
        elif schedule_type == 'weekly':
            next_run_sql = f"DATEADD(WEEK, {interval_value}, GETDATE())"
        elif schedule_type == 'monthly':
            next_run_sql = f"DATEADD(MONTH, {interval_value}, GETDATE())"
        else:
            next_run_sql = "DATEADD(DAY, 1, GETDATE())"  # افتراضي: يوم واحد

        # تحديث موعد التشغيل التالي وآخر تشغيل
        cursor.execute(f"""
            UPDATE Scheduled_Reports
            SET LastRunTime = GETDATE(),
                NextRunTime = {next_run_sql}
            WHERE ReportId = ?
        """, (report_id,))

        conn.commit()
        conn.close()

    except Exception as e:
        error_msg = f"❌ خطأ في تحديث موعد التقرير {report_id}: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)

def update_next_run_time_new(schedule_id, schedule_type, interval_value):
    """تحديث موعد التشغيل التالي للتقرير في الهيكل الجديد"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return

        cursor = conn.cursor()

        # حساب الموعد التالي حسب نوع الجدولة
        if schedule_type == 'hourly':
            next_run_sql = f"DATEADD(HOUR, {interval_value}, GETDATE())"
        elif schedule_type == 'daily':
            # للتقارير اليومية، نحافظ على نفس الوقت في اليوم التالي
            next_run_sql = f"DATEADD(DAY, {interval_value}, GETDATE())"
        elif schedule_type == 'weekly':
            next_run_sql = f"DATEADD(WEEK, {interval_value}, GETDATE())"
        elif schedule_type == 'monthly':
            next_run_sql = f"DATEADD(MONTH, {interval_value}, GETDATE())"
        else:
            next_run_sql = "DATEADD(DAY, 1, GETDATE())"  # افتراضي: يوم واحد

        # تحديث موعد التشغيل التالي وآخر تشغيل
        cursor.execute(f"""
            UPDATE Report_Schedules
            SET LastRunTime = GETDATE(),
                NextRunTime = {next_run_sql},
                UpdateDate = GETDATE()
            WHERE ScheduleId = ?
        """, (schedule_id,))

        conn.commit()
        conn.close()

        print(f"✅ تم تحديث موعد التقرير {schedule_id}")

    except Exception as e:
        error_msg = f"❌ خطأ في تحديث موعد التقرير {schedule_id}: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)

def update_statistical_report_schedule(report_id, schedule_type, interval_days):
    """تحديث موعد التشغيل التالي للتقرير في جدول Statistical_Reports_Config"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return False

        cursor = conn.cursor()

        # حساب الموعد التالي حسب نوع الجدولة
        if schedule_type == 'hourly':
            next_run_sql = "DATEADD(HOUR, 1, GETDATE())"
        elif schedule_type == 'daily':
            next_run_sql = f"DATEADD(DAY, {interval_days}, GETDATE())"
        elif schedule_type == 'weekly':
            next_run_sql = f"DATEADD(DAY, {interval_days * 7}, GETDATE())"
        elif schedule_type == 'monthly':
            next_run_sql = f"DATEADD(DAY, {interval_days * 30}, GETDATE())"
        elif schedule_type == 'custom':
            next_run_sql = f"DATEADD(DAY, {interval_days}, GETDATE())"
        else:
            next_run_sql = "DATEADD(DAY, 1, GETDATE())"  # افتراضي: يوم واحد

        # تحديث موعد التشغيل التالي وآخر تشغيل
        cursor.execute(f"""
            UPDATE Statistical_Reports_Config
            SET LastSentTime = GETDATE(),
                NextSendTime = {next_run_sql},
                SendStatus = 'sent',
                UpdatedDate = GETDATE()
            WHERE ReportId = ?
        """, (report_id,))

        conn.commit()
        conn.close()

        print(f"✅ تم تحديث موعد التقرير الإحصائي {report_id}")
        return True

    except Exception as e:
        error_msg = f"❌ خطأ في تحديث موعد التقرير الإحصائي {report_id}: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return False

def check_scheduled_statistical_reports():
    """فحص التقارير الإحصائية المجدولة وإرسالها عند حان وقتها"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return 0

        cursor = conn.cursor()

        # البحث عن التقارير المستحقة للإرسال
        cursor.execute("""
            SELECT
                ReportId,
                ReportName,
                ReportCode,
                ScheduleType,
                IntervalDays,
                StatisticsPeriodDays,
                SendTime,
                BranchIds
            FROM Statistical_Reports_Config
            WHERE IsActive = 1
              AND (NextSendTime IS NULL OR NextSendTime <= GETDATE())
              AND (SendStatus != 'sending' OR SendStatus IS NULL)
        """)

        due_reports = cursor.fetchall()
        conn.close()

        reports_sent = 0

        for report in due_reports:
            report_id = report[0]
            report_name = report[1]
            report_code = report[2]
            schedule_type = report[3]
            interval_days = report[4]
            statistics_period = report[5]
            send_time = report[6]
            branch_ids = report[7]

            print(f"📊 تشغيل التقرير المجدول: {report_name} ({report_code})")

            # تنفيذ التقرير حسب نوعه
            if report_code == 'DUE_PAYMENTS_HOURLY':
                # تقرير مواعيد الاستحقاق كل ساعة
                result = check_due_payments()
                if result and (result.get('overdue_count', 0) > 0 or result.get('upcoming_count', 0) > 0):
                    reports_sent += 1

            elif report_code == 'SUMMARY_5MINUTES':
                # التقرير الشامل المختصر كل 5 دقائق
                result = get_due_payments_summary()
                if result:
                    summary_msg = f"""📊 تقرير مواعيد الاستحقاق الشامل

🔴 الأقساط المتأخرة:
   📋 العدد: {result.get('overdue_count', 0)} قسط
   💰 المبلغ: {result.get('overdue_amount', 0):,.0f} جنيه
   للاطلاع على العملاء اضغط امر /q1

🟡 المستحقة اليوم:
   📋 العدد: {result.get('today_count', 0)} قسط
   💰 المبلغ: {result.get('today_amount', 0):,.0f} جنيه
   للاطلاع على العملاء اضغط امر /q2

🟢 المستحقة خلال 3 أيام:
   📋 العدد: {result.get('upcoming_count', 0)} قسط
   💰 المبلغ: {result.get('upcoming_amount', 0):,.0f} جنيه
   للاطلاع على العملاء اضغط امر /q3

🔵 الأقساط المستحقة بالكامل:
   📋 العدد: {result.get('all_due_count', 0)} قسط
   💰 المبلغ: {result.get('all_due_amount', 0):,.0f} جنيه
   للاطلاع على العملاء اضغط امر /q4

────────────────────────────────────────

📊 الإجمالي:
   📋 إجمالي الأقساط: {result.get('total_count', 0)} قسط
   💰 إجمالي المبلغ: {result.get('total_amount', 0):,.0f} جنيه

📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}

⚠️ الأولويات:
{"🔴 متابعة عاجلة للأقساط المتأخرة" if result.get('overdue_count', 0) > 0 else "✅ لا توجد أقساط متأخرة"}

✅ تم إرسال التفاصيل الكاملة للأقساط المتأخرة"""

                    send_notification_sync(summary_msg)
                    reports_sent += 1

            elif report_code == 'COMPREHENSIVE_3DAYS':
                # التقرير الشامل كل 3 أيام
                result = check_due_payments()
                if result:
                    # إرسال ملخص إضافي للتقرير الشامل
                    summary_msg = f"""📊 التقرير الشامل - كل 3 أيام

🔴 أقساط متأخرة: {result.get('overdue_count', 0)}
🟢 أقساط قادمة: {result.get('upcoming_count', 0)}
📅 فترة التقرير: آخر {statistics_period} أيام

⏰ تم إنشاء التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}"""

                    send_notification_sync(summary_msg)
                    reports_sent += 1

            # تحديث موعد التقرير التالي
            update_statistical_report_schedule(report_id, schedule_type, interval_days)

        if reports_sent > 0:
            print(f"📊 تم إرسال {reports_sent} تقرير إحصائي مجدول")

        return reports_sent

    except Exception as e:
        error_msg = f"❌ خطأ في فحص التقارير الإحصائية المجدولة: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return 0

def get_due_payments_summary():
    """الحصول على ملخص مواعيد الاستحقاق بدون إرسال تفاصيل"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # إحصائيات الأقساط المتأخرة
        cursor.execute("""
            SELECT
                COUNT(*) as OverdueCount,
                ISNULL(SUM(P.Amount), 0) as OverdueAmount
            FROM Sys_Payments P
            INNER JOIN Acc_Contracts CT ON P.ContractId = CT.ContractId
            WHERE P.PaymentDate < CAST(GETDATE() AS DATE)
              AND (P.IsPaid = 0 OR P.IsPaid IS NULL)
              AND (P.IsDeleted = 0 OR P.IsDeleted IS NULL)
              AND CT.IsDeleted = 0
        """)
        overdue_stats = cursor.fetchone()
        overdue_count = overdue_stats[0] if overdue_stats else 0
        overdue_amount = overdue_stats[1] if overdue_stats else 0

        # إحصائيات الأقساط المستحقة اليوم
        cursor.execute("""
            SELECT
                COUNT(*) as TodayCount,
                ISNULL(SUM(P.Amount), 0) as TodayAmount
            FROM Sys_Payments P
            INNER JOIN Acc_Contracts CT ON P.ContractId = CT.ContractId
            WHERE P.PaymentDate = CAST(GETDATE() AS DATE)
              AND (P.IsPaid = 0 OR P.IsPaid IS NULL)
              AND (P.IsDeleted = 0 OR P.IsDeleted IS NULL)
              AND CT.IsDeleted = 0
        """)
        today_stats = cursor.fetchone()
        today_count = today_stats[0] if today_stats else 0
        today_amount = today_stats[1] if today_stats else 0

        # إحصائيات الأقساط القادمة (خلال 3 أيام)
        cursor.execute("""
            SELECT
                COUNT(*) as UpcomingCount,
                ISNULL(SUM(P.Amount), 0) as UpcomingAmount
            FROM Sys_Payments P
            INNER JOIN Acc_Contracts CT ON P.ContractId = CT.ContractId
            WHERE P.PaymentDate > CAST(GETDATE() AS DATE)
              AND P.PaymentDate <= DATEADD(day, 3, CAST(GETDATE() AS DATE))
              AND (P.IsPaid = 0 OR P.IsPaid IS NULL)
              AND (P.IsDeleted = 0 OR P.IsDeleted IS NULL)
              AND CT.IsDeleted = 0
        """)
        upcoming_stats = cursor.fetchone()
        upcoming_count = upcoming_stats[0] if upcoming_stats else 0
        upcoming_amount = upcoming_stats[1] if upcoming_stats else 0

        # إحصائيات جميع الأقساط المستحقة
        cursor.execute("""
            SELECT
                COUNT(*) as AllDueCount,
                ISNULL(SUM(P.Amount), 0) as AllDueAmount
            FROM Sys_Payments P
            INNER JOIN Acc_Contracts CT ON P.ContractId = CT.ContractId
            WHERE (P.IsPaid = 0 OR P.IsPaid IS NULL)
              AND (P.IsDeleted = 0 OR P.IsDeleted IS NULL)
              AND CT.IsDeleted = 0
        """)
        all_due_stats = cursor.fetchone()
        all_due_count = all_due_stats[0] if all_due_stats else 0
        all_due_amount = all_due_stats[1] if all_due_stats else 0

        conn.close()

        total_count = overdue_count + today_count + upcoming_count
        total_amount = overdue_amount + today_amount + upcoming_amount

        return {
            'overdue_count': overdue_count,
            'overdue_amount': overdue_amount,
            'today_count': today_count,
            'today_amount': today_amount,
            'upcoming_count': upcoming_count,
            'upcoming_amount': upcoming_amount,
            'all_due_count': all_due_count,
            'all_due_amount': all_due_amount,
            'total_count': total_count,
            'total_amount': total_amount
        }

    except Exception as e:
        print(f"❌ خطأ في الحصول على ملخص مواعيد الاستحقاق: {str(e)}")
        return None

def is_notification_enabled(notification_type, branch_id=None):
    """التحقق من تفعيل نوع الإشعار"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return True  # افتراضي: مفعل إذا لم نتمكن من الاتصال

        cursor = conn.cursor()

        # البحث عن إعدادات هذا النوع من الإشعارات
        if branch_id:
            # البحث عن إعدادات خاصة بالفرع أولاً
            cursor.execute("""
                SELECT SendInstantNotification FROM Notification_Types_Settings
                WHERE NotificationType = ? AND (BranchId = ? OR BranchId IS NULL)
                ORDER BY BranchId DESC
            """, (notification_type, branch_id))
        else:
            # البحث عن الإعدادات العامة
            cursor.execute("""
                SELECT SendInstantNotification FROM Notification_Types_Settings
                WHERE NotificationType = ? AND BranchId IS NULL
            """, (notification_type,))

        result = cursor.fetchone()
        conn.close()

        if result:
            return bool(result[0])
        else:
            # إذا لم توجد إعدادات، افتراضي مفعل
            return True

    except Exception as e:
        print(f"⚠️ خطأ في فحص إعدادات الإشعار {notification_type}: {str(e)}")
        return True  # افتراضي: مفعل في حالة الخطأ

# تم حذف دالة get_latest_contract_with_details() غير المستخدمة

def send_notification_sync(message):
    """دالة مساعدة لإرسال الإشعارات من الدوال العادية"""
    import threading

    def run_async():
        try:
            asyncio.run(send_notification(message))
        except Exception as e:
            print(f"❌ خطأ في إرسال الإشعار: {str(e)}")
            if logger:
                logger.error(f"خطأ في إرسال الإشعار: {str(e)}")

    # تشغيل الدالة في thread منفصل لتجنب تضارب حلقات الأحداث
    thread = threading.Thread(target=run_async)
    thread.start()
    thread.join()  # انتظار انتهاء الإرسال

def log_notification_to_db(notification_type, title, message, count=None):
    """تسجيل الإشعار في قاعدة بيانات Terra Notifications"""
    try:
        notifications_conn = connect_to_notifications_db()
        if not notifications_conn:
            print("❌ فشل الاتصال بقاعدة بيانات الإشعارات")
            return False

        cursor = notifications_conn.cursor()

        # إدراج الإشعار في جدول Statistical_Reports_Config
        cursor.execute("""
            INSERT INTO Statistical_Reports_Config (
                ReportName,
                ReportCode,
                ScheduleType,
                IntervalDays,
                StatisticsPeriodDays,
                SendTime,
                BranchIds,
                IsActive,
                LastSentTime,
                NextSendTime,
                SendStatus,
                CreatedDate,
                UpdatedDate,
                Notes
            ) VALUES (?, ?, 'manual', 1, 1, GETDATE(), 'ALL', 1, GETDATE(), GETDATE(), 'sent', GETDATE(), GETDATE(), ?)
        """, (title, notification_type, f"Count: {count}, Message: {message[:200]}"))  # تقليم الملاحظات

        notifications_conn.commit()
        notifications_conn.close()

        print(f"✅ تم تسجيل الإشعار في Statistical_Reports_Config: {notification_type}")
        return True

    except Exception as e:
        print(f"❌ خطأ في تسجيل الإشعار: {str(e)}")
        if logger:
            logger.error(f"خطأ في تسجيل الإشعار: {str(e)}")
        return False

async def send_notification(message):
    """إرسال إشعار للمستخدمين المخولين"""
    try:
        if not message:
            warning_msg = "⚠️ محاولة إرسال رسالة فارغة"
            print(warning_msg)
            if logger:
                logger.warning(warning_msg)
            return

        bot = Bot(token=BOT_TOKEN)
        notification_text = f"🔔 **إشعار تلقائي من Terra Bot**\n\n{message}\n\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"

        # الحصول على قائمة المستقبلين النشطين
        active_recipients = get_active_recipients()

        start_msg = f"📤 بدء إرسال إشعار إلى {len(active_recipients)} مستقبل"
        print(start_msg)
        if logger:
            logger.info(start_msg)

        for chat_id in active_recipients:
            try:
                await bot.send_message(
                    chat_id=chat_id,
                    text=notification_text,
                    parse_mode='Markdown'
                )
                success_msg = f"✅ تم إرسال إشعار إلى {chat_id}"
                print(success_msg)
                if logger:
                    logger.info(success_msg)

            except Exception as e:
                error_msg = f"❌ فشل إرسال إشعار إلى {chat_id}: {str(e)}"
                print(error_msg)
                if logger:
                    logger.error(error_msg)

    except Exception as e:
        error_msg = f"❌ خطأ في إرسال الإشعارات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)

def get_customer_contracts_and_payments(customer_code, branch_id=None):
    """الحصول على تفاصيل العقود والمدفوعات لعميل معين
    Args:
        customer_code: كود العميل
        branch_id: معرف الفرع (اختياري) - إذا لم يُحدد سيعرض جميع العملاء بنفس الكود
    """
    try:
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # استعلام شامل للعقود والمدفوعات
        if branch_id:
            query = """
            DECLARE @CustomerCode NVARCHAR(50) = ?;
            DECLARE @BranchId INT = ?;

            -- المدفوعات التراكمية
            WITH PaymentsWithRunningTotal AS (
                SELECT
                    C.CustomerCode,
                    C.NameAr,
                    C.MainPhoneNo,
                    B.NameAr AS BranchName,
                    CT.ContractId,
                    CT.ContractCode,
                    CT.AddDate AS ContractAddDate,
                    (CT.TotalValue + CT.DiscountValue) AS TotalContract,
                    CT.DiscountValue AS DiscountOnly,
                    CT.TotalValue AS NetContractValue,
                    P.PaymentDate,
                    P.AddDate AS PaymentActualDate,
                    P.Amount AS PaymentAmount,
                    SUM(P.Amount) OVER (PARTITION BY CT.ContractId ORDER BY P.PaymentDate, P.PaymentId) AS RunningPaid
                FROM Acc_Contracts CT
                INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
                INNER JOIN Sys_Branches B ON C.BranchId = B.BranchId
                INNER JOIN Sys_Payments P
                    ON CT.ContractId = P.ContractId
                    AND P.IsDeleted = 0
                    AND P.IsPaid = 1
                WHERE CT.IsDeleted = 0
                  AND C.CustomerCode = @CustomerCode
                  AND C.BranchId = @BranchId
            ),

        -- الدفعات الغير مدفوعة
        UnpaidPayments AS (
            SELECT
                C.CustomerCode,
                C.NameAr,
                C.MainPhoneNo,
                B.NameAr AS BranchName,
                CT.ContractId,
                CT.ContractCode,
                CT.AddDate AS ContractAddDate,
                CT.TotalValue AS NetContractValue,
                P.PaymentDate,
                P.AddDate AS PaymentActualDate,
                P.Amount AS PaymentAmount
            FROM Acc_Contracts CT
            INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
            INNER JOIN Sys_Branches B ON C.BranchId = B.BranchId
            INNER JOIN Sys_Payments P
                ON CT.ContractId = P.ContractId
                AND (P.IsDeleted = 0 OR P.IsDeleted IS NULL)
                AND (P.IsPaid = 0 OR P.IsPaid IS NULL)
            WHERE CT.IsDeleted = 0
              AND C.CustomerCode = @CustomerCode""" + (
            " AND C.BranchId = @BranchId" if branch_id else ""
        ) + """
        )

        -- التغليف الكامل مع تحديد الأعمدة
        SELECT *
        FROM (
            SELECT
                B.NameAr AS BranchName,
                C.NameAr,
                C.CustomerCode,
                C.MainPhoneNo,
                CT.ContractCode,
                CT.ContractId,
                'ملخص العقد' AS RowType,
                (CT.TotalValue + CT.DiscountValue) AS TotalContract,
                CT.DiscountValue AS DiscountOnly,
                CT.TotalValue AS NetContractValue,
                CT.AddDate AS ContractAddDate,
                NULL AS PaymentDate,
                NULL AS PaymentActualDate,
                NULL AS PaymentAmount,
                NULL AS RunningPaid,
                NULL AS RemainingAfterThisPayment
            FROM Acc_Contracts CT
            INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
            INNER JOIN Sys_Branches B ON C.BranchId = B.BranchId
            WHERE CT.IsDeleted = 0
              AND C.CustomerCode = @CustomerCode""" + (
            " AND C.BranchId = @BranchId" if branch_id else ""
        ) + """

            UNION ALL

            SELECT
                BranchName,
                NameAr,
                CustomerCode,
                MainPhoneNo,
                ContractCode,
                ContractId,
                'دفعة' AS RowType,
                NULL AS TotalContract,
                NULL AS DiscountOnly,
                NetContractValue,
                ContractAddDate,
                PaymentDate,
                PaymentActualDate,
                PaymentAmount,
                RunningPaid,
                NetContractValue - RunningPaid AS RemainingAfterThisPayment
            FROM PaymentsWithRunningTotal

            UNION ALL

            SELECT
                BranchName,
                NameAr,
                CustomerCode,
                MainPhoneNo,
                ContractCode,
                ContractId,
                'دفعة غير مدفوعة' AS RowType,
                NULL AS TotalContract,
                NULL AS DiscountOnly,
                NetContractValue,
                ContractAddDate,
                PaymentDate,
                PaymentActualDate,
                PaymentAmount,
                NULL AS RunningPaid,
                NULL AS RemainingAfterThisPayment
            FROM UnpaidPayments
        ) AS FinalResult

        ORDER BY
            ContractId,
            CASE
                WHEN RowType = 'ملخص العقد' THEN 0
                WHEN RowType = 'دفعة' THEN 1
                ELSE 2
            END,
            PaymentDate;
        """
        else:
            query = """
            DECLARE @CustomerCode NVARCHAR(50) = ?;

            -- المدفوعات التراكمية
            WITH PaymentsWithRunningTotal AS (
                SELECT
                    C.CustomerCode,
                    C.NameAr,
                    C.MainPhoneNo,
                    B.NameAr AS BranchName,
                    CT.ContractId,
                    CT.ContractCode,
                    CT.AddDate AS ContractAddDate,
                    (CT.TotalValue + CT.DiscountValue) AS TotalContract,
                    CT.DiscountValue AS DiscountOnly,
                    CT.TotalValue AS NetContractValue,
                    P.PaymentDate,
                    P.AddDate AS PaymentActualDate,
                    P.Amount AS PaymentAmount,
                    SUM(P.Amount) OVER (PARTITION BY CT.ContractId ORDER BY P.PaymentDate, P.PaymentId) AS RunningPaid
                FROM Acc_Contracts CT
                INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
                INNER JOIN Sys_Branches B ON C.BranchId = B.BranchId
                INNER JOIN Sys_Payments P
                    ON CT.ContractId = P.ContractId
                    AND P.IsDeleted = 0
                    AND P.IsPaid = 1
                WHERE CT.IsDeleted = 0
                  AND C.CustomerCode = @CustomerCode
            ),

        -- الدفعات الغير مدفوعة
        UnpaidPayments AS (
            SELECT
                C.CustomerCode,
                C.NameAr,
                C.MainPhoneNo,
                B.NameAr AS BranchName,
                CT.ContractId,
                CT.ContractCode,
                CT.AddDate AS ContractAddDate,
                CT.TotalValue AS NetContractValue,
                P.PaymentDate,
                P.AddDate AS PaymentActualDate,
                P.Amount AS PaymentAmount
            FROM Acc_Contracts CT
            INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
            INNER JOIN Sys_Branches B ON C.BranchId = B.BranchId
            INNER JOIN Sys_Payments P
                ON CT.ContractId = P.ContractId
                AND (P.IsDeleted = 0 OR P.IsDeleted IS NULL)
                AND (P.IsPaid = 0 OR P.IsPaid IS NULL)
            WHERE CT.IsDeleted = 0
              AND C.CustomerCode = @CustomerCode
        )

        -- التغليف الكامل مع تحديد الأعمدة
        SELECT *
        FROM (
            SELECT
                B.NameAr AS BranchName,
                C.NameAr,
                C.CustomerCode,
                C.MainPhoneNo,
                CT.ContractCode,
                CT.ContractId,
                'ملخص العقد' AS RowType,
                (CT.TotalValue + CT.DiscountValue) AS TotalContract,
                CT.DiscountValue AS DiscountOnly,
                CT.TotalValue AS NetContractValue,
                CT.AddDate AS ContractAddDate,
                NULL AS PaymentDate,
                NULL AS PaymentActualDate,
                NULL AS PaymentAmount,
                NULL AS RunningPaid,
                NULL AS RemainingAfterThisPayment
            FROM Acc_Contracts CT
            INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
            INNER JOIN Sys_Branches B ON C.BranchId = B.BranchId
            WHERE CT.IsDeleted = 0
              AND C.CustomerCode = @CustomerCode

            UNION ALL

            SELECT
                BranchName,
                NameAr,
                CustomerCode,
                MainPhoneNo,
                ContractCode,
                ContractId,
                'دفعة' AS RowType,
                NULL AS TotalContract,
                NULL AS DiscountOnly,
                NetContractValue,
                ContractAddDate,
                PaymentDate,
                PaymentActualDate,
                PaymentAmount,
                RunningPaid,
                NetContractValue - RunningPaid AS RemainingAfterThisPayment
            FROM PaymentsWithRunningTotal

            UNION ALL

            SELECT
                BranchName,
                NameAr,
                CustomerCode,
                MainPhoneNo,
                ContractCode,
                ContractId,
                'دفعة غير مدفوعة' AS RowType,
                NULL AS TotalContract,
                NULL AS DiscountOnly,
                NetContractValue,
                ContractAddDate,
                PaymentDate,
                PaymentActualDate,
                PaymentAmount,
                NULL AS RunningPaid,
                NULL AS RemainingAfterThisPayment
            FROM UnpaidPayments
        ) AS FinalResult

        ORDER BY
            ContractId,
            CASE
                WHEN RowType = 'ملخص العقد' THEN 0
                WHEN RowType = 'دفعة' THEN 1
                ELSE 2
            END,
            PaymentDate;
        """

        # تنفيذ الاستعلام مع أو بدون معرف الفرع
        if branch_id:
            cursor.execute(query, (customer_code, branch_id))
        else:
            cursor.execute(query, (customer_code,))
        results = cursor.fetchall()
        conn.close()

        return results

    except Exception as e:
        error_msg = f"❌ خطأ في الحصول على بيانات العميل {customer_code}: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

def safe_format_date(date_value, default='غير محدد'):
    """تنسيق التاريخ بشكل آمن مع معالجة الأخطاء"""
    if not date_value:
        return default

    try:
        if hasattr(date_value, 'strftime'):
            return date_value.strftime('%Y-%m-%d')
        else:
            # إذا كان string، أخذ أول 10 أحرف
            date_str = str(date_value)[:10]
            # التحقق من صحة التنسيق
            if len(date_str) == 10 and date_str[4] == '-' and date_str[7] == '-':
                return date_str
            else:
                return str(date_value)
    except:
        return str(date_value) if date_value else default

def calculate_days_difference(date_value):
    """حساب الفرق بالأيام بين التاريخ المعطى واليوم الحالي"""
    if not date_value:
        return None

    try:
        if hasattr(date_value, 'date'):
            target_date = date_value.date()
        elif hasattr(date_value, 'strftime'):
            target_date = date_value
        else:
            # محاولة تحويل النص لتاريخ
            from datetime import datetime as dt
            date_str = str(date_value)[:10]
            target_date = dt.strptime(date_str, '%Y-%m-%d').date()

        return (datetime.now().date() - target_date).days
    except:
        return None

def format_customer_contracts_report(data, customer_code, for_private_chat=False):
    """تنسيق تقرير العقود والمدفوعات للعميل"""
    if not data:
        return f"❌ لا توجد بيانات للعميل: {customer_code}"

    # تجميع البيانات حسب العقد
    contracts = {}
    customer_name = ""
    branch_name = ""
    phone_number = ""

    for row in data:
        branch_name = row[0]           # BranchName
        customer_name = row[1]         # NameAr
        customer_code_from_data = row[2]  # CustomerCode
        phone_number = row[3] or "غير محدد"  # MainPhoneNo
        contract_code = row[4]         # ContractCode
        contract_id = row[5]           # ContractId
        row_type = row[6]              # RowType

        if contract_id not in contracts:
            contracts[contract_id] = {
                'contract_code': contract_code,
                'summary': None,
                'payments': [],
                'unpaid_payments': []
            }

        if row_type == 'ملخص العقد':
            contracts[contract_id]['summary'] = row
        elif row_type == 'دفعة':
            contracts[contract_id]['payments'].append(row)
        elif row_type == 'دفعة غير مدفوعة':
            contracts[contract_id]['unpaid_payments'].append(row)

    # تنسيق التقرير
    report = f"""📋 **تقرير العقود والمدفوعات**
🏢 الفرع: {branch_name}
👤 العميل: {customer_name}
📱 رقم الهاتف: {phone_number}
🔢 كود العميل: {customer_code}
📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

═══════════════════════════════════

"""

    total_contracts_value = 0
    total_paid = 0
    total_remaining = 0
    total_unpaid_installments = 0

    for contract_id, contract_data in contracts.items():
        summary = contract_data['summary']
        payments = contract_data['payments']
        unpaid_payments = contract_data['unpaid_payments']

        if summary:
            contract_code = summary[4]    # ContractCode
            total_contract = summary[7] or 0  # TotalContract
            discount = summary[8] or 0    # DiscountOnly
            net_value = summary[9] or 0   # NetContractValue

            # تنسيق التاريخ باستخدام الدالة المساعدة
            contract_date = safe_format_date(summary[10])  # ContractAddDate

            # تحويل القيم إلى أرقام للعمليات الحسابية
            try:
                net_value_num = float(net_value) if net_value else 0
                total_contracts_value += net_value_num

                # حساب إجمالي المدفوع لهذا العقد
                contract_paid = sum(float(payment[13]) if payment[13] else 0 for payment in payments)  # PaymentAmount
                contract_remaining = net_value_num - contract_paid

                total_paid += contract_paid
                total_remaining += contract_remaining
            except (ValueError, TypeError) as e:
                print(f"❌ خطأ في تحويل القيم الرقمية: {e}")
                # استخدام قيم افتراضية
                contract_paid = 0
                contract_remaining = 0
            total_unpaid_installments += len(unpaid_payments)

            # تحويل آمن للقيم قبل التنسيق
            def safe_format_number(value):
                try:
                    return f"{float(value):,.0f}" if value else "0"
                except (ValueError, TypeError):
                    return str(value) if value else "0"

            report += f"""📄 **عقد رقم: {contract_code}**
├─ 📅 تاريخ العقد: {contract_date}
├─ 💰 إجمالي العقد: {safe_format_number(total_contract)} جنيه
├─ 🏷️ الخصم: {safe_format_number(discount)} جنيه
├─ 💵 صافي العقد: {safe_format_number(net_value)} جنيه
├─ ✅ المدفوع: {safe_format_number(contract_paid)} جنيه
└─ ⏳ المتبقي: {safe_format_number(contract_remaining)} جنيه

"""

            # عرض المدفوعات
            if payments:
                report += "💳 **المدفوعات:**\n"
                for payment in payments:
                    # تنسيق تاريخ المدفوعات باستخدام الدالة المساعدة
                    payment_date = safe_format_date(payment[11])  # PaymentDate

                    payment_amount = payment[13] or 0  # PaymentAmount
                    running_paid = payment[14] or 0   # RunningPaid
                    remaining = payment[15] or 0      # RemainingAfterThisPayment

                    report += f"   • {payment_date}: {safe_format_number(payment_amount)} جنيه (إجمالي مدفوع: {safe_format_number(running_paid)}, متبقي: {safe_format_number(remaining)})\n"
                report += "\n"

            # عرض الأقساط غير المدفوعة
            if unpaid_payments:
                report += "⏰ **الأقساط المستحقة:**\n"
                for unpaid in unpaid_payments:
                    # تنسيق تاريخ الاستحقاق باستخدام الدالة المساعدة
                    due_date = safe_format_date(unpaid[11])  # PaymentDate
                    amount = unpaid[13] or 0  # PaymentAmount

                    # حساب الأيام المتبقية/المتأخرة
                    days_overdue = calculate_days_difference(unpaid[11])  # PaymentDate

                    # تحديد حالة الاستحقاق
                    if days_overdue is not None:
                        if days_overdue > 0:
                            status = f"🔴 متأخر {days_overdue} يوم"
                        elif days_overdue == 0:
                            status = "🟡 مستحق اليوم"
                        else:
                            status = f"🟢 باقي {abs(days_overdue)} يوم"
                    else:
                        status = "⚪ غير محدد"

                    # تحويل المبلغ إلى رقم للتنسيق
                    try:
                        amount_num = float(amount) if amount else 0
                        report += f"   • {due_date}: {amount_num:,.0f} جنيه - {status}\n"
                    except (ValueError, TypeError):
                        report += f"   • {due_date}: {amount} جنيه - {status}\n"
                report += "\n"

        report += "─" * 40 + "\n\n"

    # الملخص العام - تحويل آمن للقيم
    def safe_format_number(value):
        try:
            return f"{float(value):,.0f}" if value else "0"
        except (ValueError, TypeError):
            return str(value) if value else "0"

    def safe_percentage(paid, total):
        try:
            paid_num = float(paid) if paid else 0
            total_num = float(total) if total else 0
            return f"{(paid_num/total_num*100) if total_num > 0 else 0:.1f}"
        except (ValueError, TypeError):
            return "0.0"

    report += f"""📊 **الملخص العام:**
💼 إجمالي قيمة العقود: {safe_format_number(total_contracts_value)} جنيه
✅ إجمالي المدفوع: {safe_format_number(total_paid)} جنيه
⏳ إجمالي المتبقي: {safe_format_number(total_remaining)} جنيه
📋 عدد الأقساط المستحقة: {total_unpaid_installments}
📈 نسبة السداد: {safe_percentage(total_paid, total_contracts_value)}%"""

    report += f"""

═══════════════════════════════════
⏰ تم إنشاء التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
🤖 Terra Bot - نظام التقارير التلقائي"""

    return report

def search_customer_by_code_or_phone(search_term):
    """البحث عن العميل بكود العميل أو رقم التليفون"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # البحث بكود العميل أو رقم التليفون (الأول والثاني)
        cursor.execute("""
            SELECT
                C.CustomerId,
                C.CustomerCode,
                C.NameAr,
                C.NameEn,
                C.MainPhoneNo,
                C.Email,
                B.NameAr AS BranchName,
                C.AddDate,
                'كود العميل' AS SearchType,
                C.BranchId
            FROM Acc_Customers C
            LEFT JOIN Sys_Branches B ON C.BranchId = B.BranchId
            WHERE C.CustomerCode = ? AND C.IsDeleted = 0

            UNION ALL

            SELECT
                C.CustomerId,
                C.CustomerCode,
                C.NameAr,
                C.NameEn,
                C.MainPhoneNo,
                C.Email,
                B.NameAr AS BranchName,
                C.AddDate,
                'رقم التليفون الأول' AS SearchType,
                C.BranchId
            FROM Acc_Customers C
            LEFT JOIN Sys_Branches B ON C.BranchId = B.BranchId
            WHERE C.MainPhoneNo = ? AND C.IsDeleted = 0

            UNION ALL

            SELECT
                C.CustomerId,
                C.CustomerCode,
                C.NameAr,
                C.NameEn,
                C.MainPhoneNo,
                C.Email,
                B.NameAr AS BranchName,
                C.AddDate,
                'رقم التليفون الثاني' AS SearchType,
                C.BranchId
            FROM Acc_Customers C
            LEFT JOIN Sys_Branches B ON C.BranchId = B.BranchId
            WHERE C.SubMainPhoneNo = ? AND C.IsDeleted = 0
        """, (search_term, search_term, search_term))

        results = cursor.fetchall()
        conn.close()

        return results

    except Exception as e:
        print(f"❌ خطأ في البحث عن العميل {search_term}: {str(e)}")
        if logger:
            logger.error(f"خطأ في البحث عن العميل {search_term}: {str(e)}")
        return None

def check_customer_exists(customer_code, branch_id=None):
    """التحقق من وجود العميل وإرجاع معلوماته الأساسية"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # البحث عن العميل مع أو بدون تحديد الفرع
        if branch_id:
            cursor.execute("""
                SELECT
                    C.CustomerId,
                    C.CustomerCode,
                    C.NameAr,
                    C.NameEn,
                    C.MainPhoneNo,
                    C.Email,
                    B.NameAr AS BranchName,
                    C.AddDate
                FROM Acc_Customers C
                LEFT JOIN Sys_Branches B ON C.BranchId = B.BranchId
                WHERE C.CustomerCode = ? AND C.BranchId = ? AND C.IsDeleted = 0
            """, (customer_code, branch_id))
        else:
            cursor.execute("""
                SELECT
                    C.CustomerId,
                    C.CustomerCode,
                    C.NameAr,
                    C.NameEn,
                    C.MainPhoneNo,
                    C.Email,
                    B.NameAr AS BranchName,
                    C.AddDate
                FROM Acc_Customers C
                LEFT JOIN Sys_Branches B ON C.BranchId = B.BranchId
                WHERE C.CustomerCode = ? AND C.IsDeleted = 0
            """, (customer_code,))

        result = cursor.fetchone()
        conn.close()

        return result

    except Exception as e:
        print(f"❌ خطأ في البحث عن العميل {customer_code}: {str(e)}")
        if logger:
            logger.error(f"خطأ في البحث عن العميل {customer_code}: {str(e)}")
        return None

def get_customer_contracts_count(customer_code, branch_id=None):
    """الحصول على عدد العقود للعميل
    Args:
        customer_code: كود العميل
        branch_id: معرف الفرع (اختياري)
    """
    try:
        conn = connect_to_main_db()
        if not conn:
            return 0

        cursor = conn.cursor()

        if branch_id:
            cursor.execute("""
                SELECT COUNT(*)
                FROM Acc_Contracts CT
                INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
                WHERE C.CustomerCode = ? AND C.BranchId = ? AND CT.IsDeleted = 0
            """, (customer_code, branch_id))
        else:
            cursor.execute("""
                SELECT COUNT(*)
                FROM Acc_Contracts CT
                INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
                WHERE C.CustomerCode = ? AND CT.IsDeleted = 0
            """, (customer_code,))

        result = cursor.fetchone()
        conn.close()

        return result[0] if result else 0

    except Exception as e:
        print(f"❌ خطأ في حساب عقود العميل {customer_code}: {str(e)}")
        return 0

def get_customer_payments_summary(customer_code):
    """الحصول على ملخص المدفوعات للعميل"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        cursor.execute("""
            SELECT
                COUNT(CASE WHEN P.IsPaid = 1 THEN 1 END) AS PaidCount,
                COUNT(CASE WHEN P.IsPaid = 0 OR P.IsPaid IS NULL THEN 1 END) AS UnpaidCount,
                SUM(CASE WHEN P.IsPaid = 1 THEN P.Amount ELSE 0 END) AS TotalPaid,
                SUM(CASE WHEN P.IsPaid = 0 OR P.IsPaid IS NULL THEN P.Amount ELSE 0 END) AS TotalUnpaid
            FROM Acc_Contracts CT
            INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
            INNER JOIN Sys_Payments P ON CT.ContractId = P.ContractId
            WHERE C.CustomerCode = ? AND CT.IsDeleted = 0 AND P.IsDeleted = 0
        """, (customer_code,))

        result = cursor.fetchone()
        conn.close()

        return result

    except Exception as e:
        print(f"❌ خطأ في حساب ملخص المدفوعات للعميل {customer_code}: {str(e)}")
        return None

def send_customer_contracts_report(customer_code):
    """إرسال تقرير العقود والمدفوعات لعميل معين مع خطوات الاستفسار"""
    try:
        print(f"🔍 بدء عملية البحث عن العميل: {customer_code}")

        # الخطوة 1: التحقق من وجود العميل
        print("📋 الخطوة 1: التحقق من وجود العميل...")
        customer_info = check_customer_exists(customer_code)

        if not customer_info:
            error_msg = f"❌ العميل غير موجود: {customer_code}"
            print(error_msg)
            send_notification_sync(error_msg)
            return False

        customer_name = customer_info[2]
        branch_name = customer_info[6]
        print(f"✅ تم العثور على العميل: {customer_name} - {branch_name}")

        # الخطوة 2: فحص عدد العقود
        print("📋 الخطوة 2: فحص عدد العقود...")
        contracts_count = get_customer_contracts_count(customer_code)
        print(f"📊 عدد العقود: {contracts_count}")

        if contracts_count == 0:
            warning_msg = f"⚠️ العميل {customer_name} ({customer_code}) لا يملك أي عقود"
            print(warning_msg)
            send_notification_sync(warning_msg)
            return False

        # الخطوة 3: فحص ملخص المدفوعات
        print("📋 الخطوة 3: فحص ملخص المدفوعات...")
        payments_summary = get_customer_payments_summary(customer_code)

        if payments_summary:
            paid_count = payments_summary[0] or 0
            unpaid_count = payments_summary[1] or 0
            total_paid = payments_summary[2] or 0
            total_unpaid = payments_summary[3] or 0
            print(f"💰 ملخص المدفوعات: مدفوع ({paid_count}) = {total_paid:,.0f} جنيه، مستحق ({unpaid_count}) = {total_unpaid:,.0f} جنيه")

        # الخطوة 4: الحصول على التفاصيل الكاملة
        print("📋 الخطوة 4: جلب التفاصيل الكاملة للعقود والمدفوعات...")
        data = get_customer_contracts_and_payments(customer_code)

        if data:
            print(f"📊 تم جلب {len(data)} سجل من البيانات")

            # الخطوة 5: تنسيق التقرير
            print("📋 الخطوة 5: تنسيق التقرير...")
            formatted_report = format_customer_contracts_report(data, customer_code)

            # الخطوة 6: إرسال التقرير
            print("📋 الخطوة 6: إرسال التقرير...")
            send_notification_sync(formatted_report)

            success_msg = f"✅ تم إرسال تقرير العميل {customer_name} ({customer_code}) بنجاح"
            print(success_msg)
            if logger:
                logger.info(f"تم إرسال تقرير العميل {customer_code}")
            return True
        else:
            error_msg = f"❌ فشل في جلب بيانات العميل: {customer_code}"
            print(error_msg)
            send_notification_sync(error_msg)
            return False

    except Exception as e:
        error_msg = f"❌ خطأ في إرسال تقرير العميل {customer_code}: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        send_notification_sync(f"❌ خطأ في إنشاء تقرير العميل {customer_code}")
        return False

def test_customer_contracts_report():
    """اختبار تقرير العقود والمدفوعات مع عميل تجريبي"""
    try:
        # يمكن تغيير كود العميل هنا للاختبار
        test_customer_code = "1213"  # كود العميل المطلوب اختباره

        print(f"🧪 اختبار تقرير العقود والمدفوعات للعميل: {test_customer_code}")

        # إرسال التقرير
        result = send_customer_contracts_report(test_customer_code)

        if result:
            print("✅ نجح الاختبار - تم إرسال التقرير")
        else:
            print("❌ فشل الاختبار - لم يتم إرسال التقرير")

        return result

    except Exception as e:
        error_msg = f"❌ خطأ في اختبار تقرير العقود: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return False

# دالة مساعدة لإرسال تقرير عميل من خلال كود العميل (للاستخدام الخارجي)
def search_and_get_customer_report(search_term):
    """البحث الذكي عن العميل وإرجاع التقرير"""
    try:
        print(f"🔍 البحث الذكي عن: {search_term}")

        # البحث بكود العميل أو رقم التليفون
        search_results = search_customer_by_code_or_phone(search_term)

        if not search_results:
            return f"❌ لم يتم العثور على عميل بالبيانات: {search_term}"

        # إرجاع التقرير للعميل الأول مباشرة (حتى لو كان هناك عدة عملاء)
        customer = search_results[0]
        customer_code = customer[1]
        customer_name = customer[2]
        phone = customer[4]
        branch_name = customer[6]
        search_type = customer[8]
        branch_id = customer[9]

        if len(search_results) == 1:
            print(f"✅ تم العثور على العميل بـ{search_type}: {customer_name} - {branch_name}")
        else:
            print(f"✅ تم العثور على {len(search_results)} عميل، سيتم عرض تقرير الأول: {customer_name} - {branch_name}")
            # عرض جميع العملاء الموجودين
            for i, cust in enumerate(search_results):
                print(f"   {i+1}. {cust[2]} - {cust[6]} (كود: {cust[1]})")

        # إنشاء التقرير للعميل الأول مع تحديد الفرع
        return get_customer_contracts_report_text(customer_code, branch_id)

    except Exception as e:
        error_msg = f"❌ خطأ في البحث الذكي عن {search_term}: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return f"❌ خطأ في البحث عن: {search_term}"

def search_and_get_customer_report_by_branch(search_term, branch_id, branch_name):
    """البحث الذكي عن العميل في فرع محدد وإرجاع تقريره"""
    try:
        print(f"🔍 البحث الذكي عن '{search_term}' في فرع {branch_name} (معرف: {branch_id})")

        # البحث عن العميل
        search_results = search_customer_by_code_or_phone(search_term)

        if not search_results:
            return f"❌ لم يتم العثور على عميل بالبيانات: {search_term}"

        # فلترة النتائج حسب الفرع المحدد
        branch_results = [result for result in search_results if result[9] == branch_id]

        if not branch_results:
            # عرض العملاء الموجودين في فروع أخرى
            other_branches = []
            for result in search_results:
                other_branch_name = "التجمع" if result[9] == 3 else "مدينة نصر" if result[9] == 2 else f"فرع {result[9]}"
                other_branches.append(f"• {result[2]} - {other_branch_name}")

            other_branches_text = "\n".join(other_branches)
            return f"""❌ لم يتم العثور على عميل بالبيانات '{search_term}' في فرع {branch_name}

🔍 **لكن تم العثور على عملاء بنفس البيانات في فروع أخرى:**
{other_branches_text}

💡 **لعرض تقرير عميل من فرع آخر:**
استخدم الأمر `/branches` واختر الفرع المطلوب"""

        # إرجاع التقرير للعميل الأول في الفرع المحدد
        customer = branch_results[0]
        customer_code = customer[1]
        customer_name = customer[2]

        if len(branch_results) == 1:
            print(f"✅ تم العثور على العميل في فرع {branch_name}: {customer_name}")
        else:
            print(f"✅ تم العثور على {len(branch_results)} عميل في فرع {branch_name}، سيتم عرض تقرير الأول: {customer_name}")

        # إنشاء التقرير للعميل مع تحديد الفرع
        return get_customer_contracts_report_text(customer_code, branch_id)

    except Exception as e:
        error_msg = f"❌ خطأ في البحث عن {search_term} في فرع {branch_name}: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return f"❌ خطأ في البحث عن: {search_term}"

def debug_phone_number(phone_number):
    """فحص رقم التليفون في قاعدة البيانات"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return "❌ خطأ في الاتصال بقاعدة البيانات"

        cursor = conn.cursor()

        # البحث عن الرقم في جميع الحقول
        cursor.execute("""
            SELECT
                C.CustomerId,
                C.BranchId,
                C.CustomerCode,
                C.NameAr,
                C.MainPhoneNo,
                C.SubMainPhoneNo,
                B.NameAr AS BranchName
            FROM Acc_Customers C
            LEFT JOIN Sys_Branches B ON C.BranchId = B.BranchId
            WHERE C.MainPhoneNo = ?
               OR C.SubMainPhoneNo = ?
               OR C.MainPhoneNo = ?
               OR C.SubMainPhoneNo = ?
            AND C.IsDeleted = 0
        """, (phone_number, phone_number, phone_number.lstrip('0'), phone_number.lstrip('0')))

        results = cursor.fetchall()
        conn.close()

        if not results:
            return f"❌ لم يتم العثور على رقم {phone_number} في قاعدة البيانات"

        report = f"🔍 **فحص رقم التليفون: {phone_number}**\n\n"
        report += f"📊 **تم العثور على {len(results)} نتيجة:**\n\n"

        for i, result in enumerate(results, 1):
            customer_id, branch_id, customer_code, name, main_phone, sub_phone, branch_name = result
            report += f"**{i}. {name}**\n"
            report += f"   🆔 معرف العميل: {customer_id}\n"
            report += f"   🔢 كود العميل: {customer_code}\n"
            report += f"   🏢 الفرع: {branch_name} (معرف: {branch_id})\n"
            report += f"   📱 التليفون الأول: {main_phone or 'غير محدد'}\n"
            report += f"   📞 التليفون الثاني: {sub_phone or 'غير محدد'}\n\n"

        return report

    except Exception as e:
        return f"❌ خطأ في فحص الرقم: {str(e)}"

def get_customer_contracts_report_text(customer_code, branch_id=None):
    """الحصول على نص تقرير العميل بدون إرسال
    Args:
        customer_code: كود العميل
        branch_id: معرف الفرع (اختياري)
    """
    try:
        print(f"🔍 جاري إنشاء تقرير للعميل: {customer_code}")

        # الخطوة 1: التحقق من وجود العميل
        customer_info = check_customer_exists(customer_code, branch_id)

        if not customer_info:
            return f"❌ العميل غير موجود: {customer_code}"

        customer_name = customer_info[2]
        branch_name = customer_info[6]
        print(f"✅ تم العثور على العميل: {customer_name} - {branch_name}")

        # الخطوة 2: فحص عدد العقود
        contracts_count = get_customer_contracts_count(customer_code, branch_id)
        print(f"📊 عدد العقود: {contracts_count}")

        if contracts_count == 0:
            return f"⚠️ العميل {customer_name} ({customer_code}) لا يملك أي عقود"

        # الخطوة 3: الحصول على التفاصيل الكاملة
        data = get_customer_contracts_and_payments(customer_code, branch_id)

        if data:
            print(f"📊 تم جلب {len(data)} سجل من البيانات")

            # تنسيق التقرير للمحادثة الخاصة
            formatted_report = format_customer_contracts_report(data, customer_code, for_private_chat=True)

            print(f"✅ تم إنشاء تقرير العميل {customer_name} ({customer_code}) بنجاح")
            return formatted_report
        else:
            return f"❌ فشل في جلب بيانات العميل: {customer_code}"

    except Exception as e:
        error_msg = f"❌ خطأ في إنشاء تقرير العميل {customer_code}: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return f"❌ خطأ في إنشاء تقرير العميل {customer_code}"

def check_contracts_and_payments_updates():
    """فحص التحديثات في العقود والمدفوعات وإرسال تقارير تلقائية"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # فحص العقود المحدثة حديثاً (آخر ساعة)
        cursor.execute("""
            SELECT
                C.CustomerCode,
                C.NameAr AS CustomerName,
                CT.ContractCode,
                CT.ContractId,
                CT.UpdateDate,
                B.NameAr AS BranchName
            FROM Acc_Contracts CT
            INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
            INNER JOIN Sys_Branches B ON C.BranchId = B.BranchId
            WHERE CT.UpdateDate >= DATEADD(HOUR, -1, GETDATE())
              AND CT.IsDeleted = 0
            ORDER BY CT.UpdateDate DESC
        """)

        updated_contracts = cursor.fetchall()

        # فحص المدفوعات الجديدة (آخر ساعة)
        cursor.execute("""
            SELECT
                C.CustomerCode,
                C.NameAr AS CustomerName,
                CT.ContractCode,
                P.Amount,
                P.PaymentDate,
                P.AddDate,
                B.NameAr AS BranchName
            FROM Sys_Payments P
            INNER JOIN Acc_Contracts CT ON P.ContractId = CT.ContractId
            INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
            INNER JOIN Sys_Branches B ON C.BranchId = B.BranchId
            WHERE P.AddDate >= DATEADD(HOUR, -1, GETDATE())
              AND P.IsDeleted = 0
              AND P.IsPaid = 1
            ORDER BY P.AddDate DESC
        """)

        new_payments = cursor.fetchall()
        conn.close()

        # إرسال تحديثات العقود
        for contract in updated_contracts:
            customer_code = contract[0]
            customer_name = contract[1]
            contract_code = contract[2]
            branch_name = contract[5]

            update_msg = f"""🔄 **تحديث عقد**

👤 العميل: {customer_name} ({customer_code})
🏢 الفرع: {branch_name}
📋 العقد: {contract_code}
⏰ وقت التحديث: {datetime.now().strftime('%Y-%m-%d %H:%M')}

📊 **تقرير محدث للعميل:**"""

            # إرسال التحديث
            send_notification_sync(update_msg)

            # إرسال التقرير المحدث
            send_customer_contracts_report(customer_code)

        # إرسال تحديثات المدفوعات
        for payment in new_payments:
            customer_code = payment[0]
            customer_name = payment[1]
            contract_code = payment[2]
            amount = payment[3]
            payment_date = safe_format_date(payment[4])
            branch_name = payment[6]

            payment_msg = f"""💰 **دفعة جديدة**

👤 العميل: {customer_name} ({customer_code})
🏢 الفرع: {branch_name}
📋 العقد: {contract_code}
💵 المبلغ: {amount:,.0f} جنيه
📅 تاريخ الدفعة: {payment_date}
⏰ وقت الإضافة: {datetime.now().strftime('%Y-%m-%d %H:%M')}

📊 **تقرير محدث للعميل:**"""

            # إرسال التحديث
            send_notification_sync(payment_msg)

            # إرسال التقرير المحدث
            send_customer_contracts_report(customer_code)

        if updated_contracts or new_payments:
            print(f"📊 تم إرسال {len(updated_contracts)} تحديث عقد و {len(new_payments)} دفعة جديدة")

        return len(updated_contracts) + len(new_payments)

    except Exception as e:
        error_msg = f"❌ خطأ في فحص تحديثات العقود والمدفوعات: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

def get_all_customers_by_branch(branch_id):
    """الحصول على جميع العملاء الذين لديهم عقود في فرع معين"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # الحصول على جميع العملاء الذين لديهم عقود في الفرع المحدد
        cursor.execute("""
            SELECT DISTINCT
                C.CustomerCode,
                C.NameAr,
                B.NameAr AS BranchName,
                COUNT(CT.ContractId) AS ContractsCount,
                SUM(CT.TotalValue) AS TotalContractsValue,
                SUM(ISNULL(P.Amount, 0)) AS TotalPaid
            FROM Acc_Customers C
            INNER JOIN Acc_Contracts CT ON C.CustomerId = CT.CustomerId
            INNER JOIN Sys_Branches B ON C.BranchId = B.BranchId
            LEFT JOIN Sys_Payments P ON CT.ContractId = P.ContractId AND P.IsPaid = 1 AND P.IsDeleted = 0
            WHERE C.BranchId = ? AND C.IsDeleted = 0 AND CT.IsDeleted = 0
            GROUP BY C.CustomerCode, C.NameAr, B.NameAr
            ORDER BY C.NameAr
        """, (branch_id,))

        results = cursor.fetchall()
        conn.close()

        return results

    except Exception as e:
        error_msg = f"❌ خطأ في الحصول على عملاء الفرع {branch_id}: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

def send_all_customers_reports_by_branch(branch_id):
    """إرسال تقارير جميع العملاء في فرع معين"""
    try:
        # الحصول على قائمة العملاء
        customers = get_all_customers_by_branch(branch_id)

        if not customers:
            return "❌ لا توجد عملاء في هذا الفرع"

        branch_name = customers[0][2]  # اسم الفرع من أول سجل

        # إرسال ملخص أولاً
        summary_msg = f"""📊 **تقارير عملاء فرع {branch_name}**

📋 عدد العملاء: {len(customers)}
📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}

🔄 جاري إرسال التقارير التفصيلية..."""

        send_notification_sync(summary_msg)

        # إرسال تقرير لكل عميل
        success_count = 0
        for customer in customers:
            customer_code = customer[0]
            customer_name = customer[1]
            contracts_count = customer[3]

            print(f"📋 إرسال تقرير للعميل: {customer_name} ({customer_code})")

            # إرسال تقرير العميل
            if send_customer_contracts_report(customer_code):
                success_count += 1

            # انتظار قصير بين التقارير لتجنب الإرهاق
            import time
            time.sleep(2)

        # إرسال ملخص نهائي
        final_msg = f"""✅ **انتهى إرسال تقارير فرع {branch_name}**

📊 تم إرسال {success_count} من {len(customers)} تقرير بنجاح
⏰ وقت الانتهاء: {datetime.now().strftime('%Y-%m-%d %H:%M')}"""

        send_notification_sync(final_msg)

        return f"✅ تم إرسال {success_count} تقرير من {len(customers)} عميل"

    except Exception as e:
        error_msg = f"❌ خطأ في إرسال تقارير الفرع {branch_id}: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return error_msg

async def send_all_customers_reports_by_branch_to_chat(branch_id, query):
    """إرسال تقارير جميع العملاء في فرع معين في المحادثة الحالية"""
    try:
        # الحصول على قائمة العملاء
        customers = get_all_customers_by_branch(branch_id)

        if not customers:
            await query.message.reply_text("❌ لا توجد عملاء في هذا الفرع")
            return

        branch_name = customers[0][2]  # اسم الفرع من أول سجل

        # إرسال ملخص أولاً
        summary_msg = f"""📊 **تقارير عملاء فرع {branch_name}**

📋 عدد العملاء: {len(customers)}
📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}

🔄 جاري إرسال التقارير التفصيلية..."""

        await query.message.reply_text(summary_msg, parse_mode='Markdown')

        # إرسال تقرير لكل عميل في المحادثة الحالية
        success_count = 0
        for customer in customers:
            customer_code = customer[0]
            customer_name = customer[1]

            print(f"📋 إرسال تقرير للعميل: {customer_name} ({customer_code})")

            try:
                # الحصول على تقرير العميل
                report_text = get_customer_contracts_report_text(customer_code, branch_id)

                if report_text and not report_text.startswith("❌"):
                    # إرسال التقرير في المحادثة الحالية
                    await query.message.reply_text(report_text, parse_mode='Markdown')
                    success_count += 1
                    print(f"✅ تم إرسال تقرير العميل: {customer_name} ({customer_code})")
                else:
                    # إرسال رسالة خطأ للعميل مع التفاصيل
                    error_msg = f"❌ خطأ في تقرير العميل {customer_name} ({customer_code})"
                    if report_text and report_text.startswith("❌"):
                        error_msg += f"\n📝 التفاصيل: {report_text}"
                    await query.message.reply_text(error_msg)
                    print(f"❌ فشل تقرير العميل: {customer_name} ({customer_code}) - {report_text}")
            except Exception as e:
                # معالجة الأخطاء غير المتوقعة
                error_msg = f"❌ خطأ غير متوقع في تقرير العميل {customer_name} ({customer_code}): {str(e)}"
                await query.message.reply_text(error_msg)
                print(error_msg)

        # إرسال ملخص النتائج
        final_msg = f"""✅ **تم الانتهاء من إرسال تقارير فرع {branch_name}**

📊 النتائج:
✅ تم إرسال: {success_count} تقرير
❌ فشل: {len(customers) - success_count} تقرير
📋 إجمالي العملاء: {len(customers)}

📅 تم في: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

        await query.message.reply_text(final_msg, parse_mode='Markdown')

    except Exception as e:
        error_msg = f"❌ خطأ في إرسال تقارير فرع {branch_id}: {str(e)}"
        print(error_msg)
        await query.message.reply_text(error_msg)

def check_due_payments():
    """فحص الأقساط المستحقة والمتأخرة وإرسال تنبيهات"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # فحص الأقساط المتأخرة (تاريخ الاستحقاق مضى)
        cursor.execute("""
            SELECT
                C.CustomerCode,
                C.NameAr AS CustomerName,
                CT.ContractCode,
                P.Amount,
                P.PaymentDate,
                DATEDIFF(DAY, P.PaymentDate, GETDATE()) AS DaysOverdue,
                B.NameAr AS BranchName,
                P.PaymentId,
                C.MainPhoneNo,
                C.SubMainPhoneNo
            FROM Sys_Payments P
            INNER JOIN Acc_Contracts CT ON P.ContractId = CT.ContractId
            INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
            INNER JOIN Sys_Branches B ON C.BranchId = B.BranchId
            WHERE P.PaymentDate < GETDATE()
              AND (P.IsPaid = 0 OR P.IsPaid IS NULL)
              AND (P.IsDeleted = 0 OR P.IsDeleted IS NULL)
              AND CT.IsDeleted = 0
              AND C.IsDeleted = 0
            ORDER BY P.PaymentDate ASC
        """)

        overdue_payments = cursor.fetchall()

        # فحص الأقساط المستحقة خلال الأيام القادمة (3 أيام)
        cursor.execute("""
            SELECT
                C.CustomerCode,
                C.NameAr AS CustomerName,
                CT.ContractCode,
                P.Amount,
                P.PaymentDate,
                DATEDIFF(DAY, GETDATE(), P.PaymentDate) AS DaysUntilDue,
                B.NameAr AS BranchName,
                P.PaymentId,
                C.MainPhoneNo,
                C.SubMainPhoneNo
            FROM Sys_Payments P
            INNER JOIN Acc_Contracts CT ON P.ContractId = CT.ContractId
            INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
            INNER JOIN Sys_Branches B ON C.BranchId = B.BranchId
            WHERE P.PaymentDate BETWEEN GETDATE() AND DATEADD(DAY, 3, GETDATE())
              AND (P.IsPaid = 0 OR P.IsPaid IS NULL)
              AND (P.IsDeleted = 0 OR P.IsDeleted IS NULL)
              AND CT.IsDeleted = 0
              AND C.IsDeleted = 0
            ORDER BY P.PaymentDate ASC
        """)

        upcoming_payments = cursor.fetchall()
        conn.close()

        notifications_sent = 0

        # إرسال تنبيهات الأقساط المتأخرة
        if overdue_payments:
            overdue_msg = f"""🔴 **أقساط متأخرة - تنبيه عاجل**
📅 تاريخ الفحص: {datetime.now().strftime('%Y-%m-%d %H:%M')}
📊 عدد الأقساط المتأخرة: {len(overdue_payments)}

═══════════════════════════════════

"""

            for payment in overdue_payments:  # جميع الأقساط المتأخرة
                customer_code = payment[0]
                customer_name = payment[1]
                contract_code = payment[2]
                amount = payment[3]
                due_date = safe_format_date(payment[4])
                days_overdue = payment[5]
                branch_name = payment[6]
                # payment[7] هو PaymentId
                main_phone = payment[8] or ''
                sub_phone = payment[9] or ''

                print(f"🔍 Debug - العميل: {customer_name}")
                print(f"    📊 عدد الحقول: {len(payment)}")
                print(f"    📊 جميع البيانات: {payment}")
                print(f"    📱 التليفون الأول (index 8): '{main_phone}' - نوع: {type(main_phone)}")
                print(f"    📞 التليفون الثاني (index 9): '{sub_phone}' - نوع: {type(sub_phone)}")

                # تحديد رقم التليفون المتاح
                phone_display = ""
                whatsapp_link = ""
                phone_for_whatsapp = ""

                if main_phone:
                    phone_display = f"📱 التليفون: {main_phone}"
                    phone_for_whatsapp = main_phone
                    if sub_phone:
                        phone_display += f" / {sub_phone}"
                elif sub_phone:
                    phone_display = f"📱 التليفون: {sub_phone}"
                    phone_for_whatsapp = sub_phone
                else:
                    phone_display = "📱 التليفون: غير محدد"

                # إنشاء رابط WhatsApp إذا كان هناك رقم تليفون
                if phone_for_whatsapp:
                    print(f"    🔧 معالجة رقم التليفون: '{phone_for_whatsapp}' - نوع: {type(phone_for_whatsapp)}")

                    # تحويل إلى نص أولاً
                    phone_str = str(phone_for_whatsapp).strip()
                    print(f"    📝 كنص: '{phone_str}'")

                    # تنظيف رقم التليفون (إزالة المسافات والرموز)
                    clean_phone = ''.join(filter(str.isdigit, phone_str))
                    print(f"    🧹 الرقم بعد التنظيف: '{clean_phone}' - طول: {len(clean_phone)}")

                    # معالجة الرقم وإنشاء رابط WhatsApp في جميع الأحوال
                    if len(clean_phone) < 10:
                        print(f"    ⚠️ رقم التليفون قصير: {len(clean_phone)} أرقام - سيتم إنشاء رابط على أي حال")
                        phone_display += " ⚠️ (قد يحتاج تصحيح)"

                    # إنشاء رابط WhatsApp للجميع:
                    # إضافة كود مصر إذا لم يكن موجود
                    original_clean = clean_phone
                    if clean_phone.startswith('01') and len(clean_phone) == 11:
                        clean_phone = '2' + clean_phone
                        print(f"    🇪🇬 أضيف كود مصر للرقم المصري: {original_clean} → {clean_phone}")
                    elif clean_phone.startswith('1') and len(clean_phone) == 10:
                        clean_phone = '20' + clean_phone
                        print(f"    🇪🇬 أضيف كود مصر الكامل: {original_clean} → {clean_phone}")
                    elif clean_phone.startswith('01') and len(clean_phone) < 11:
                        # للأرقام المصرية القصيرة
                        clean_phone = '2' + clean_phone
                        print(f"    🇪🇬 رقم مصري قصير، أضيف كود مصر: {original_clean} → {clean_phone}")
                    elif not clean_phone.startswith('2') and len(clean_phone) >= 1:
                        clean_phone = '2' + clean_phone
                        print(f"    🇪🇬 أضيف كود الدولة: {original_clean} → {clean_phone}")
                    else:
                        print(f"    ✅ الرقم يحتوي على كود الدولة بالفعل: {clean_phone}")

                    print(f"    ✅ الرقم النهائي: '{clean_phone}'")

                    # رسالة WhatsApp للمتأخرين (تشفير ذكي)
                    whatsapp_text = f"""السلام عليكم {customer_name}

نذكركم بوجود قسط متأخر:
💰 المبلغ: {amount:,.0f} جنيه
📅 تاريخ الاستحقاق: {due_date}
📋 العقد: {contract_code}

يرجى سداد المبلغ في أقرب وقت ممكن.

شكراً لكم"""

                    # تشفير ذكي - فقط الأشياء الضرورية
                    import urllib.parse
                    whatsapp_message = urllib.parse.quote(whatsapp_text, safe='أبتثجحخدذرزسشصضطظعغفقكلمنهويىءآإؤئة0123456789:💰📅📋')
                    whatsapp_link = f"https://wa.me/{clean_phone}?text={whatsapp_message}"
                    print(f"    🔗 رابط WhatsApp: {whatsapp_link[:80]}...")
                else:
                    print(f"    ❌ لا يوجد رقم تليفون متاح")

                overdue_msg += f"""🔴 **متأخر {days_overdue} يوم**
👤 العميل: {customer_name} ({customer_code})
{phone_display}
🏢 الفرع: {branch_name}
📋 العقد: {contract_code}
💰 المبلغ: {amount:,.0f} جنيه
📅 تاريخ الاستحقاق: {due_date}"""

                # إضافة رابط WhatsApp إذا كان متاح
                if whatsapp_link:
                    overdue_msg += f"\n💬 [إرسال رسالة واتساب]({whatsapp_link})"

                overdue_msg += "\n\n"

            if len(overdue_payments) > 10:
                overdue_msg += f"... و {len(overdue_payments) - 10} قسط متأخر آخر\n\n"

            overdue_msg += "⚠️ **يرجى المتابعة العاجلة مع العملاء**"

            send_notification_sync(overdue_msg)

            # تسجيل الإشعار في قاعدة البيانات
            log_notification_to_db(
                notification_type="due_payments_overdue",
                title="أقساط متأخرة - تنبيه عاجل",
                message=overdue_msg,
                count=len(overdue_payments)
            )

            notifications_sent += 1

        # إرسال تنبيهات الأقساط المستحقة قريباً
        if upcoming_payments:
            upcoming_msg = f"""🟡 **أقساط مستحقة خلال 3 أيام**
📅 تاريخ الفحص: {datetime.now().strftime('%Y-%m-%d %H:%M')}
📊 عدد الأقساط: {len(upcoming_payments)}

═══════════════════════════════════

"""

            for payment in upcoming_payments:  # جميع الأقساط القادمة
                customer_code = payment[0]
                customer_name = payment[1]
                contract_code = payment[2]
                amount = payment[3]
                due_date = safe_format_date(payment[4])
                days_until = payment[5]
                branch_name = payment[6]
                # payment[7] هو PaymentId
                main_phone = payment[8] or ''
                sub_phone = payment[9] or ''

                print(f"🔍 Debug القادمة - العميل: {customer_name}")
                print(f"    📊 عدد الحقول: {len(payment)}")
                print(f"    📊 جميع البيانات: {payment}")
                print(f"    📱 التليفون الأول (index 8): '{main_phone}' - نوع: {type(main_phone)}")
                print(f"    📞 التليفون الثاني (index 9): '{sub_phone}' - نوع: {type(sub_phone)}")

                if days_until == 0:
                    status = "🟡 مستحق اليوم"
                elif days_until == 1:
                    status = "🟢 مستحق غداً"
                else:
                    status = f"🟢 مستحق خلال {days_until} أيام"

                # تحديد رقم التليفون المتاح
                phone_display = ""
                whatsapp_link = ""
                phone_for_whatsapp = ""

                if main_phone:
                    phone_display = f"📱 التليفون: {main_phone}"
                    phone_for_whatsapp = main_phone
                    if sub_phone:
                        phone_display += f" / {sub_phone}"
                elif sub_phone:
                    phone_display = f"📱 التليفون: {sub_phone}"
                    phone_for_whatsapp = sub_phone
                else:
                    phone_display = "📱 التليفون: غير محدد"

                # إنشاء رابط WhatsApp إذا كان هناك رقم تليفون
                if phone_for_whatsapp:
                    print(f"    🔧 معالجة رقم التليفون: '{phone_for_whatsapp}' - نوع: {type(phone_for_whatsapp)}")

                    # تحويل إلى نص أولاً
                    phone_str = str(phone_for_whatsapp).strip()
                    print(f"    📝 كنص: '{phone_str}'")

                    # تنظيف رقم التليفون
                    clean_phone = ''.join(filter(str.isdigit, phone_str))
                    print(f"    🧹 الرقم بعد التنظيف: '{clean_phone}' - طول: {len(clean_phone)}")

                    # معالجة الرقم وإنشاء رابط WhatsApp في جميع الأحوال
                    if len(clean_phone) < 10:
                        print(f"    ⚠️ رقم التليفون قصير: {len(clean_phone)} أرقام - سيتم إنشاء رابط على أي حال")
                        phone_display += " ⚠️ (قد يحتاج تصحيح)"

                    # إنشاء رابط WhatsApp للجميع:
                    # إضافة كود مصر إذا لم يكن موجود
                    original_clean = clean_phone
                    if clean_phone.startswith('01') and len(clean_phone) == 11:
                        clean_phone = '2' + clean_phone
                        print(f"    🇪🇬 أضيف كود مصر للرقم المصري: {original_clean} → {clean_phone}")
                    elif clean_phone.startswith('1') and len(clean_phone) == 10:
                        clean_phone = '20' + clean_phone
                        print(f"    🇪🇬 أضيف كود مصر الكامل: {original_clean} → {clean_phone}")
                    elif clean_phone.startswith('01') and len(clean_phone) < 11:
                        # للأرقام المصرية القصيرة
                        clean_phone = '2' + clean_phone
                        print(f"    🇪🇬 رقم مصري قصير، أضيف كود مصر: {original_clean} → {clean_phone}")
                    elif not clean_phone.startswith('2') and len(clean_phone) >= 1:
                        clean_phone = '2' + clean_phone
                        print(f"    🇪🇬 أضيف كود الدولة: {original_clean} → {clean_phone}")
                    else:
                        print(f"    ✅ الرقم يحتوي على كود الدولة بالفعل: {clean_phone}")

                    print(f"    ✅ الرقم النهائي: '{clean_phone}'")

                    # رسالة WhatsApp للتذكير (تشفير ذكي)
                    whatsapp_text = f"""السلام عليكم {customer_name}

تذكير بموعد قسط قادم:
💰 المبلغ: {amount:,.0f} جنيه
📅 تاريخ الاستحقاق: {due_date}
📋 العقد: {contract_code}

نرجو الاستعداد لسداد القسط في الموعد المحدد.

شكراً لكم"""

                    # تشفير ذكي - فقط الأشياء الضرورية
                    import urllib.parse
                    whatsapp_message = urllib.parse.quote(whatsapp_text, safe='أبتثجحخدذرزسشصضطظعغفقكلمنهويىءآإؤئة0123456789:💰📅📋')
                    whatsapp_link = f"https://wa.me/{clean_phone}?text={whatsapp_message}"
                    print(f"    🔗 رابط WhatsApp: {whatsapp_link[:80]}...")
                else:
                    print(f"    ❌ لا يوجد رقم تليفون متاح")

                upcoming_msg += f"""{status}
👤 العميل: {customer_name} ({customer_code})
{phone_display}
🏢 الفرع: {branch_name}
📋 العقد: {contract_code}
💰 المبلغ: {amount:,.0f} جنيه
📅 تاريخ الاستحقاق: {due_date}"""

                # إضافة رابط WhatsApp إذا كان متاح
                if whatsapp_link:
                    upcoming_msg += f"\n💬 [إرسال تذكير واتساب]({whatsapp_link})"

                upcoming_msg += "\n\n"

            if len(upcoming_payments) > 10:
                upcoming_msg += f"... و {len(upcoming_payments) - 10} قسط آخر\n\n"

            upcoming_msg += "📞 **يرجى التواصل مع العملاء للتذكير**"

            send_notification_sync(upcoming_msg)

            # تسجيل الإشعار في قاعدة البيانات
            log_notification_to_db(
                notification_type="due_payments_upcoming",
                title="أقساط مستحقة خلال 3 أيام",
                message=upcoming_msg,
                count=len(upcoming_payments)
            )

            notifications_sent += 1

        if notifications_sent > 0:
            print(f"📅 تم إرسال {notifications_sent} تنبيه مواعيد استحقاق")
            if logger:
                logger.info(f"تم إرسال {notifications_sent} تنبيه مواعيد استحقاق")

        return {
            'overdue_count': len(overdue_payments),
            'upcoming_count': len(upcoming_payments),
            'notifications_sent': notifications_sent
        }

    except Exception as e:
        error_msg = f"❌ خطأ في فحص مواعيد الاستحقاق: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

def get_customer_due_payments_report(customer_code):
    """تقرير مواعيد الاستحقاق لعميل معين"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # الحصول على جميع الأقساط (مدفوعة وغير مدفوعة) للعميل
        cursor.execute("""
            SELECT
                C.CustomerCode,
                C.NameAr AS CustomerName,
                CT.ContractCode,
                P.Amount,
                P.PaymentDate,
                P.IsPaid,
                P.AddDate AS PaymentAddDate,
                B.NameAr AS BranchName,
                CASE
                    WHEN P.IsPaid = 1 THEN 'مدفوع'
                    WHEN P.PaymentDate < GETDATE() THEN 'متأخر'
                    WHEN P.PaymentDate = CAST(GETDATE() AS DATE) THEN 'مستحق اليوم'
                    ELSE 'قادم'
                END AS PaymentStatus,
                CASE
                    WHEN P.IsPaid = 1 THEN 0
                    WHEN P.PaymentDate < GETDATE() THEN DATEDIFF(DAY, P.PaymentDate, GETDATE())
                    ELSE DATEDIFF(DAY, GETDATE(), P.PaymentDate)
                END AS DaysDifference
            FROM Sys_Payments P
            INNER JOIN Acc_Contracts CT ON P.ContractId = CT.ContractId
            INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
            INNER JOIN Sys_Branches B ON C.BranchId = B.BranchId
            WHERE C.CustomerCode = ?
              AND (P.IsDeleted = 0 OR P.IsDeleted IS NULL)
              AND CT.IsDeleted = 0
              AND C.IsDeleted = 0
            ORDER BY P.PaymentDate ASC
        """, (customer_code,))

        payments = cursor.fetchall()
        conn.close()

        return payments

    except Exception as e:
        error_msg = f"❌ خطأ في الحصول على مواعيد استحقاق العميل {customer_code}: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

def send_customer_report_by_code(customer_code):
    """دالة مبسطة لإرسال تقرير عميل - للاستخدام كزر"""
    if not customer_code:
        print("❌ يجب تحديد كود العميل")
        return False

    print(f"🎯 طلب تقرير للعميل: {customer_code}")
    return send_customer_contracts_report(customer_code)

def check_new_customers():
    """فحص العملاء الجدد مع التفاصيل الشاملة والتحقق من قاعدة البيانات حسب الفرع"""

    # التحقق من تفعيل إشعارات العملاء
    if not is_notification_enabled('customers'):
        return None

    try:
        # الحصول على كل العملاء الجدد بعد آخر معرف
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # الحصول على آخر معرف تم إرساله لهذا الفرع
        def get_last_sent_customer_id(branch_id):
            try:
                notifications_conn = connect_to_notifications_db()
                if not notifications_conn:
                    return 0

                notifications_cursor = notifications_conn.cursor()
                # جلب آخر معرف عميل (CustomerId) من نوع العملاء فقط عالم
                notifications_cursor.execute("""
                    SELECT TOP 1 RecordUniqueId FROM Notifications_Log
                    WHERE DataType = 'عميل' AND IsActive = 1 AND RecordUniqueId IS NOT NULL
                    ORDER BY RecordUniqueId DESC
                """)

                result = notifications_cursor.fetchone()
                notifications_conn.close()

                last_id = result[0] if result else 0
                print(f"📊 آخر معرف عميل (CustomerId): {last_id}")
                return last_id
            except Exception as e:
                print(f"❌ خطأ في الحصول على آخر معرف عميل: {str(e)}")
                return 0

        # استعلام للحصول على العملاء الجدد بعد آخر معرف تم إرساله
        query = """
        SELECT
            c.CustomerId, c.CustomerCode, c.NameAr, c.NameEn, c.MainPhoneNo,
            c.SubMainPhoneNo, c.Email, c.Address, c.NationalId, c.Notes,
            c.AddDate, c.UpdateDate, c.BranchId,
            ISNULL(branch.NameAr, 'فرع غير محدد') AS BranchName,
            ISNULL(city.NameAr, 'غير محدد') AS CityName,
            ISNULL(pay.NameAr, 'غير محدد') AS PayTypeName,
            ISNULL(social.NameAr, 'غير محدد') AS SocialMediaName,
            ISNULL(addUser.UserName, 'غير محدد') AS AddedByUser,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            ISNULL(updateUser.UserName, '') AS UpdatedByUser,
            ISNULL(updateUser.FullName, '') AS UpdatedByFullName
        FROM Acc_Customers c
        LEFT JOIN Sys_Branches branch ON c.BranchId = branch.BranchId
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Acc_PayType pay ON c.PayTypeId = pay.PayTypeId
        LEFT JOIN Sys_SocialMedia social ON c.SocialMediaId = social.SocialMediaId
        LEFT JOIN Sys_Users addUser ON c.AddUser = addUser.UserId
        LEFT JOIN Sys_Users updateUser ON c.UpdateUser = updateUser.UserId
        WHERE c.IsDeleted = 0 AND c.BranchId = ? AND c.CustomerId > ?
        ORDER BY c.CustomerId ASC
        """

        # فحص كل فرع منفصل
        notifications_sent = 0
        # الحصول على جميع الفروع من قاعدة البيانات تلقائ
        cursor.execute("SELECT DISTINCT BranchId FROM Acc_Customers WHERE IsDeleted = 0 AND BranchId IS NOT NULL ORDER BY BranchId")
        branches = [row[0] for row in cursor.fetchall()]
        print(f"🏢 فحص العملاء في الفروع: {branches}")

        for branch_id in branches:
            # الحصول على آخر معرف تم إرساله لهذا الفرع
            last_sent_id = get_last_sent_customer_id(branch_id)
            print(f"📊 آخر معرف عميل تم إرساله للفرع {branch_id}: {last_sent_id}")

            # تنفيذ الاستعلام للحصول على العملاء الجدد بعد آخر معرف
            cursor.execute(query, (branch_id, last_sent_id))
            new_customers = cursor.fetchall()

            # معالجة كل عميل جديد في هذا الفرع
            for customer in new_customers:

                # تحديد أيقونة الفرع
                branch_icon = "🏢" if customer[12] == 2 else "🏬" if customer[12] == 3 else "🏪"

                message = f"""👤 **عميل جديد تم إضافته**

{branch_icon} **الفرع:** {customer[13]}

🔢 **الكود:** {customer[1]}
👤 **الاسم العربي:** {customer[2] or 'غير محدد'}
🔤 **الاسم الإنجليزي:** {customer[3] or 'غير محدد'}
📱 **الهاتف الأساسي:** {customer[4] or 'غير محدد'}
📞 **الهاتف الفرعي:** {customer[5] or 'غير محدد'}
📧 **الإيميل:** {customer[6] or 'غير محدد'}
🏠 **العنوان:** {customer[7] or 'غير محدد'}
🆔 **الرقم القومي:** {customer[8] or 'غير محدد'}
📝 **ملاحظات:** {customer[9] or 'لا توجد'}

🌍 **المدينة/المنطقة:** {customer[14]}
💳 **طريقة الدفع:** {customer[15]}
📱 **وسيلة التواصل:** {customer[16]}

👨‍💼 **تم الإنشاء بواسطة:** {customer[18]}
📅 **تاريخ الإضافة:** {customer[10].strftime('%Y-%m-%d %H:%M') if customer[10] else 'غير محدد'}"""

                if customer[11]:  # إذا كان هناك تاريخ تحديث
                    message += f"\n🔄 **آخر تحديث:** {customer[11].strftime('%Y-%m-%d %H:%M')}"
                    if customer[19]:  # إذا كان هناك من حدث
                        message += f"\n👨‍🔧 **تم التحديث بواسطة:** {customer[20]}"

                # حفظ في قاعدة البيانات
                save_notification_to_db(
                    'عميل',
                    customer[1],  # CustomerCode
                    customer[2] or 'غير محدد',  # NameAr
                    customer[12],  # BranchId
                    customer[13],  # BranchName
                    customer[0],   # CustomerId (المعرف الفريد)
                    customer[10],  # AddDate
                    customer[18]   # AddedByFullName
                )

                # إرسال الإشعار
                send_notification_sync(message)
                notifications_sent += 1
                print(f"✅ تم إرسال إشعار للعميل {customer[1]} (ID: {customer[0]})")

        conn.close()

        if notifications_sent > 0:
            print(f"📊 تم إرسال {notifications_sent} إشعار عميل جديد")
            return None  # لا نرسل رسالة إضافية لأن كل عميل أرسل إشعار منفصل

        return None

    except Exception as e:
        print(f"❌ خطأ في فحص العملاء الجدد: {str(e)}")
        return None

def check_new_previews():
    """فحص المعاينات الجديدة مع التفاصيل الشاملة والتحقق من قاعدة البيانات حسب الفرع"""

    # التحقق من تفعيل إشعارات المعاينات
    if not is_notification_enabled('previews'):
        return None

    try:
        # الحصول على كل المعاينات الجديدة بعد آخر معرف لكل فرع
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # الحصول على آخر معرف تم إرساله لهذا الفرع
        def get_last_sent_preview_id(branch_id):
            try:
                notifications_conn = connect_to_notifications_db()
                if not notifications_conn:
                    return 0

                notifications_cursor = notifications_conn.cursor()

                # جلب آخر معرف معاينة (PreviewId) من نوع المعاينات فقط عالم
                notifications_cursor.execute("""
                    SELECT TOP 1 RecordUniqueId FROM Notifications_Log
                    WHERE DataType = 'معاينة' AND IsActive = 1 AND RecordUniqueId IS NOT NULL
                    ORDER BY RecordUniqueId DESC
                """)

                result = notifications_cursor.fetchone()
                notifications_conn.close()

                last_id = result[0] if result else 0
                print(f"📊 آخر معرف معاينة (PreviewId): {last_id}")
                return last_id
            except Exception as e:
                print(f"❌ خطأ في الحصول على آخر معرف معاينة: {str(e)}")
                return 0

        # استعلام للحصول على المعاينات الجديدة بعد آخر معرف تم إرساله
        query = """
        SELECT
            p.PreviewId, p.PreviewCode, p.Date AS PreviewDate, p.Notes, p.BranchId,
            ISNULL(branch.NameAr, 'فرع غير محدد') AS BranchName,
            c.CustomerCode, c.NameAr AS CustomerName, c.MainPhoneNo, c.Address,
            ISNULL(city.NameAr, 'غير محدد') AS CityName,
            ISNULL(addUser.UserName, 'غير محدد') AS AddedByUser,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            p.AddDate
        FROM Sys_Previews p
        INNER JOIN Acc_Customers c ON p.CustomerId = c.CustomerId
        LEFT JOIN Sys_Branches branch ON p.BranchId = branch.BranchId
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON p.AddUser = addUser.UserId
        WHERE p.IsDeleted = 0 AND c.IsDeleted = 0 AND c.BranchId = ? AND p.PreviewId > ?
        ORDER BY p.PreviewId ASC
        """

        # فحص كل فرع منفصل
        notifications_sent = 0
        # الحصول على جميع الفروع من قاعدة البيانات تلقائ
        cursor.execute("SELECT DISTINCT BranchId FROM Acc_Customers WHERE IsDeleted = 0 AND BranchId IS NOT NULL ORDER BY BranchId")
        branches = [row[0] for row in cursor.fetchall()]
        print(f"🏢 فحص المعاينات في الفروع: {branches}")

        for branch_id in branches:
            # الحصول على آخر معرف تم إرساله لهذا الفرع
            last_sent_id = get_last_sent_preview_id(branch_id)
            print(f"📊 آخر معرف معاينة تم إرساله للفرع {branch_id}: {last_sent_id}")

            # تنفيذ الاستعلام للحصول على المعاينات الجديدة بعد آخر معرف
            cursor.execute(query, (branch_id, last_sent_id))
            new_previews = cursor.fetchall()

            # معالجة كل معاينة جديدة في هذا الفرع
            for preview in new_previews:

                # تحديد أيقونة الفرع
                branch_icon = "🏢" if preview[4] == 2 else "🏬" if preview[4] == 3 else "🏪"

                message = f"""👁️ **معاينة جديدة تم إضافتها**

{branch_icon} **الفرع:** {preview[5]}

🔢 **كود المعاينة:** {preview[1]}
📅 **تاريخ المعاينة:** {preview[2].strftime('%Y-%m-%d %H:%M') if preview[2] else 'غير محدد'}
📝 **ملاحظات المعاينة:** {preview[3] or 'لا توجد'}

👤 **بيانات العميل:**
🔢 **كود العميل:** {preview[6]}
👤 **اسم العميل:** {preview[7] or 'غير محدد'}
📱 **هاتف العميل:** {preview[8] or 'غير محدد'}
🏠 **عنوان العميل:** {preview[9] or 'غير محدد'}
🌍 **المدينة/المنطقة:** {preview[10] or 'غير محدد'}

👨‍💼 **تم الإنشاء بواسطة:** {preview[12] or 'غير محدد'}
📅 **تاريخ الإضافة:** {preview[13].strftime('%Y-%m-%d %H:%M') if preview[13] else 'غير محدد'}"""

                # حفظ في قاعدة البيانات
                save_notification_to_db(
                    'معاينة',
                    preview[1],  # PreviewCode
                    f"معاينة للعميل {preview[7] or 'غير محدد'}",
                    preview[4],  # BranchId
                    preview[5],  # BranchName
                    preview[0],  # PreviewId (المعرف الفريد)
                    preview[13], # AddDate
                    preview[12] or 'غير محدد'  # AddedByFullName
                )

                # إرسال الإشعار
                send_notification_sync(message)
                notifications_sent += 1
                print(f"✅ تم إرسال إشعار للمعاينة {preview[1]} (ID: {preview[0]})")

        conn.close()

        if notifications_sent > 0:
            print(f"📊 تم إرسال {notifications_sent} إشعار معاينة جديدة")
            return None  # لا نرسل رسالة إضافية لأن كل معاينة أرسلت إشعار منفصل

        return None

    except Exception as e:
        print(f"❌ خطأ في فحص المعاينات الجديدة: {str(e)}")
        return None

def check_new_meetings():
    """فحص الاجتماعات الجديدة مع التفاصيل الشاملة والتحقق من قاعدة البيانات حسب الفرع"""

    # التحقق من تفعيل إشعارات الاجتماعات
    if not is_notification_enabled('meetings'):
        return None

    try:
        # الحصول على كل الاجتماعات الجديدة بعد آخر معرف لكل فرع
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # الحصول على آخر معرف تم إرساله لهذا الفرع
        def get_last_sent_meeting_id(branch_id):
            try:
                notifications_conn = connect_to_notifications_db()
                if not notifications_conn:
                    return 0

                notifications_cursor = notifications_conn.cursor()
                # جلب آخر معرف اجتماع (MeetingId) من نوع الاجتماعات فقط عالم
                notifications_cursor.execute("""
                    SELECT TOP 1 RecordUniqueId FROM Notifications_Log
                    WHERE DataType = 'اجتماع' AND IsActive = 1 AND RecordUniqueId IS NOT NULL
                    ORDER BY RecordUniqueId DESC
                """)

                result = notifications_cursor.fetchone()
                notifications_conn.close()

                last_id = result[0] if result else 0
                print(f"📊 آخر معرف اجتماع (MeetingId): {last_id}")
                return last_id
            except Exception as e:
                print(f"❌ خطأ في الحصول على آخر معرف اجتماع: {str(e)}")
                return 0

        # استعلام للحصول على الاجتماعات الجديدة بعد آخر معرف تم إرساله
        query = """
        SELECT
            m.MeetingId, m.MeetingCode, m.Date AS MeetingDate, m.Notes, m.BranchId,
            ISNULL(branch.NameAr, 'فرع غير محدد') AS BranchName,
            c.CustomerCode, c.NameAr AS CustomerName, c.MainPhoneNo, c.Address,
            ISNULL(city.NameAr, 'غير محدد') AS CityName,
            ISNULL(addUser.UserName, 'غير محدد') AS AddedByUser,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            m.AddDate
        FROM Sys_Meetings m
        INNER JOIN Acc_Customers c ON m.CustomerId = c.CustomerId
        LEFT JOIN Sys_Branches branch ON m.BranchId = branch.BranchId
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON m.AddUser = addUser.UserId
        WHERE m.IsDeleted = 0 AND c.IsDeleted = 0 AND c.BranchId = ? AND m.MeetingId > ?
        ORDER BY m.MeetingId ASC
        """

        # فحص كل فرع منفصل
        notifications_sent = 0
        # الحصول على جميع الفروع من قاعدة البيانات تلقائ
        cursor.execute("SELECT DISTINCT BranchId FROM Acc_Customers WHERE IsDeleted = 0 AND BranchId IS NOT NULL ORDER BY BranchId")
        branches = [row[0] for row in cursor.fetchall()]
        print(f"🏢 فحص الاجتماعات في الفروع: {branches}")

        for branch_id in branches:
            # الحصول على آخر معرف تم إرساله لهذا الفرع
            last_sent_id = get_last_sent_meeting_id(branch_id)
            print(f"📊 آخر معرف اجتماع تم إرساله للفرع {branch_id}: {last_sent_id}")

            # تنفيذ الاستعلام للحصول على الاجتماعات الجديدة بعد آخر معرف
            cursor.execute(query, (branch_id, last_sent_id))
            new_meetings = cursor.fetchall()

            # معالجة كل اجتماع جديد في هذا الفرع
            for meeting in new_meetings:

                # تحديد أيقونة الفرع
                branch_icon = "🏢" if meeting[4] == 2 else "🏬" if meeting[4] == 3 else "🏪"

                message = f"""🤝 **اجتماع جديد تم إضافته**

{branch_icon} **الفرع:** {meeting[5]}

🔢 **كود الاجتماع:** {meeting[1]}
📅 **تاريخ الاجتماع:** {meeting[2].strftime('%Y-%m-%d %H:%M') if meeting[2] else 'غير محدد'}
📝 **ملاحظات الاجتماع:** {meeting[3] or 'لا توجد'}

👤 **بيانات العميل:**
🔢 **كود العميل:** {meeting[6]}
👤 **اسم العميل:** {meeting[7] or 'غير محدد'}
📱 **هاتف العميل:** {meeting[8] or 'غير محدد'}
🏠 **عنوان العميل:** {meeting[9] or 'غير محدد'}
🌍 **المدينة/المنطقة:** {meeting[10] or 'غير محدد'}

👨‍💼 **تم الإنشاء بواسطة:** {meeting[12] or 'غير محدد'}
📅 **تاريخ الإضافة:** {meeting[13].strftime('%Y-%m-%d %H:%M') if meeting[13] else 'غير محدد'}"""

                # حفظ في قاعدة البيانات
                save_notification_to_db(
                    'اجتماع',
                    meeting[1],  # MeetingCode
                    f"اجتماع مع العميل {meeting[7] or 'غير محدد'}",
                    meeting[4],  # BranchId
                    meeting[5],  # BranchName
                    meeting[0],  # MeetingId (المعرف الفريد)
                    meeting[13], # AddDate
                    meeting[12] or 'غير محدد'  # AddedByFullName
                )

                # إرسال الإشعار
                send_notification_sync(message)
                notifications_sent += 1
                print(f"✅ تم إرسال إشعار للاجتماع {meeting[1]} (ID: {meeting[0]})")

        conn.close()

        if notifications_sent > 0:
            print(f"📊 تم إرسال {notifications_sent} إشعار اجتماع جديد")
            return None  # لا نرسل رسالة إضافية لأن كل اجتماع أرسل إشعار منفصل

        return None

    except Exception as e:
        error_msg = f"❌ خطأ في فحص الاجتماعات الجديدة: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

def check_new_designs():
    """فحص التصميمات الجديدة مع التفاصيل الشاملة والتحقق من قاعدة البيانات حسب الفرع"""

    try:
        # الحصول على كل التصميمات الجديدة بعد آخر معرف لكل فرع
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # الحصول على آخر معرف تم إرساله لهذا الفرع
        def get_last_sent_design_id(branch_id):
            try:
                notifications_conn = connect_to_notifications_db()
                if not notifications_conn:
                    return 0

                notifications_cursor = notifications_conn.cursor()
                # جلب آخر معرف تصميم (DesignId) من نوع التصميمات فقط عالم
                notifications_cursor.execute("""
                    SELECT TOP 1 RecordUniqueId FROM Notifications_Log
                    WHERE DataType = 'تصميم' AND IsActive = 1 AND RecordUniqueId IS NOT NULL
                    ORDER BY RecordUniqueId DESC
                """)

                result = notifications_cursor.fetchone()
                notifications_conn.close()

                last_id = result[0] if result else 0
                print(f"📊 آخر معرف تصميم (DesignId): {last_id}")
                return last_id
            except Exception as e:
                print(f"❌ خطأ في الحصول على آخر معرف تصميم: {str(e)}")
                return 0

        # استعلام للحصول على التصميمات الجديدة بعد آخر معرف تم إرساله
        query = """
        SELECT
            d.DesignId, d.DesignCode, d.Date AS DesignDate, d.Notes, d.BranchId,
            ISNULL(branch.NameAr, 'فرع غير محدد') AS BranchName,
            c.CustomerCode, c.NameAr AS CustomerName, c.MainPhoneNo, c.Address,
            ISNULL(city.NameAr, 'غير محدد') AS CityName,
            ISNULL(addUser.UserName, 'غير محدد') AS AddedByUser,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            d.AddDate
        FROM Sys_Designs d
        INNER JOIN Acc_Customers c ON d.CustomerId = c.CustomerId
        LEFT JOIN Sys_Branches branch ON d.BranchId = branch.BranchId
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON d.AddUser = addUser.UserId
        WHERE d.IsDeleted = 0 AND c.IsDeleted = 0 AND c.BranchId = ? AND d.DesignId > ?
        ORDER BY d.DesignId ASC
        """

        # فحص كل فرع منفصل
        notifications_sent = 0
        # الحصول على جميع الفروع من قاعدة البيانات تلقائ
        cursor.execute("SELECT DISTINCT BranchId FROM Acc_Customers WHERE IsDeleted = 0 AND BranchId IS NOT NULL ORDER BY BranchId")
        branches = [row[0] for row in cursor.fetchall()]
        print(f"🏢 فحص التصميمات في الفروع: {branches}")

        for branch_id in branches:
            # الحصول على آخر معرف تم إرساله لهذا الفرع
            last_sent_id = get_last_sent_design_id(branch_id)
            print(f"📊 آخر معرف تصميم تم إرساله للفرع {branch_id}: {last_sent_id}")

            # تنفيذ الاستعلام للحصول على التصميمات الجديدة بعد آخر معرف
            cursor.execute(query, (branch_id, last_sent_id))
            new_designs = cursor.fetchall()

            # معالجة كل تصميم جديد في هذا الفرع
            for design in new_designs:

                # تحديد أيقونة الفرع
                branch_icon = "🏢" if design[4] == 2 else "🏬" if design[4] == 3 else "🏪"

                message = f"""🎨 **تصميم جديد تم إضافته**

{branch_icon} **الفرع:** {design[5]}

🔢 **كود التصميم:** {design[1]}
📅 **تاريخ التصميم:** {design[2].strftime('%Y-%m-%d %H:%M') if design[2] else 'غير محدد'}
📝 **ملاحظات التصميم:** {design[3] or 'لا توجد'}

👤 **بيانات العميل:**
🔢 **كود العميل:** {design[6]}
👤 **اسم العميل:** {design[7] or 'غير محدد'}
📱 **هاتف العميل:** {design[8] or 'غير محدد'}
🏠 **عنوان العميل:** {design[9] or 'غير محدد'}
🌍 **المدينة/المنطقة:** {design[10] or 'غير محدد'}

👨‍💼 **تم الإنشاء بواسطة:** {design[12] or 'غير محدد'}
📅 **تاريخ الإضافة:** {design[13].strftime('%Y-%m-%d %H:%M') if design[13] else 'غير محدد'}"""

                # حفظ في قاعدة البيانات
                save_notification_to_db(
                    'تصميم',
                    design[1],  # DesignCode
                    f"تصميم للعميل {design[7] or 'غير محدد'}",
                    design[4],  # BranchId
                    design[5],  # BranchName
                    design[0],  # DesignId (المعرف الفريد)
                    design[13], # AddDate
                    design[12] or 'غير محدد'  # AddedByFullName
                )

                # إرسال الإشعار
                send_notification_sync(message)
                notifications_sent += 1
                print(f"✅ تم إرسال إشعار للتصميم {design[1]} (ID: {design[0]})")

        conn.close()

        if notifications_sent > 0:
            print(f"📊 تم إرسال {notifications_sent} إشعار تصميم جديد")
            return None  # لا نرسل رسالة إضافية لأن كل تصميم أرسل إشعار منفصل

        return None

    except Exception as e:
        error_msg = f"❌ خطأ في فحص التصميمات الجديدة: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

def check_new_contracts():
    """فحص العقود الجديدة مع التفاصيل الشاملة والتحقق من قاعدة البيانات حسب الفرع"""

    try:
        # الحصول على كل العقود الجديدة بعد آخر معرف لكل فرع
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()

        # الحصول على آخر معرف تم إرساله لهذا الفرع
        def get_last_sent_contract_id(branch_id):
            try:
                notifications_conn = connect_to_notifications_db()
                if not notifications_conn:
                    return 0

                notifications_cursor = notifications_conn.cursor()
                # جلب آخر معرف عقد (ContractId) من نوع العقود فقط عالم
                notifications_cursor.execute("""
                    SELECT TOP 1 RecordUniqueId FROM Notifications_Log
                    WHERE DataType = 'عقد' AND IsActive = 1 AND RecordUniqueId IS NOT NULL
                    ORDER BY RecordUniqueId DESC
                """)

                result = notifications_cursor.fetchone()
                notifications_conn.close()

                last_id = result[0] if result else 0
                print(f"📊 آخر معرف عقد (ContractId): {last_id}")
                return last_id
            except Exception as e:
                print(f"❌ خطأ في الحصول على آخر معرف عقد: {str(e)}")
                return 0

        # استعلام للحصول على العقود الجديدة بعد آخر معرف تم إرساله
        query = """
        SELECT
            ct.ContractId, ct.ContractCode, ct.Date AS ContractDate, ct.Notes, ct.BranchId,
            ISNULL(branch.NameAr, 'فرع غير محدد') AS BranchName,
            c.CustomerCode, c.NameAr AS CustomerName, c.MainPhoneNo, c.Address,
            ISNULL(city.NameAr, 'غير محدد') AS CityName,
            ISNULL(addUser.UserName, 'غير محدد') AS AddedByUser,
            ISNULL(addUser.FullName, 'غير محدد') AS AddedByFullName,
            ct.AddDate
        FROM Acc_Contracts ct
        INNER JOIN Acc_Customers c ON ct.CustomerId = c.CustomerId
        LEFT JOIN Sys_Branches branch ON ct.BranchId = branch.BranchId
        LEFT JOIN Sys_City city ON c.CityId = city.CityId
        LEFT JOIN Sys_Users addUser ON ct.AddUser = addUser.UserId
        WHERE ct.IsDeleted = 0 AND c.IsDeleted = 0 AND c.BranchId = ? AND ct.ContractId > ?
        ORDER BY ct.ContractId ASC
        """

        # فحص كل فرع منفصل
        notifications_sent = 0
        # الحصول على جميع الفروع من قاعدة البيانات تلقائ
        cursor.execute("SELECT DISTINCT BranchId FROM Acc_Customers WHERE IsDeleted = 0 AND BranchId IS NOT NULL ORDER BY BranchId")
        branches = [row[0] for row in cursor.fetchall()]
        print(f"🏢 فحص العقود في الفروع: {branches}")

        for branch_id in branches:
            # الحصول على آخر معرف تم إرساله لهذا الفرع
            last_sent_id = get_last_sent_contract_id(branch_id)
            print(f"📊 آخر معرف عقد تم إرساله للفرع {branch_id}: {last_sent_id}")

            # تنفيذ الاستعلام للحصول على العقود الجديدة بعد آخر معرف
            cursor.execute(query, (branch_id, last_sent_id))
            new_contracts = cursor.fetchall()

            # معالجة كل عقد جديد في هذا الفرع
            for contract in new_contracts:

                # تحديد أيقونة الفرع
                branch_icon = "🏢" if contract[4] == 2 else "🏬" if contract[4] == 3 else "🏪"

                message = f"""📄 **عقد جديد تم إضافته**

{branch_icon} **الفرع:** {contract[5]}

🔢 **كود العقد:** {contract[1]}
📅 **تاريخ العقد:** {contract[2].strftime('%Y-%m-%d %H:%M') if contract[2] else 'غير محدد'}
📝 **ملاحظات العقد:** {contract[3] or 'لا توجد'}

👤 **بيانات العميل:**
🔢 **كود العميل:** {contract[6]}
👤 **اسم العميل:** {contract[7] or 'غير محدد'}
📱 **هاتف العميل:** {contract[8] or 'غير محدد'}
🏠 **عنوان العميل:** {contract[9] or 'غير محدد'}
🌍 **المدينة/المنطقة:** {contract[10] or 'غير محدد'}

👨‍💼 **تم الإنشاء بواسطة:** {contract[12] or 'غير محدد'}
📅 **تاريخ الإضافة:** {contract[13].strftime('%Y-%m-%d %H:%M') if contract[13] else 'غير محدد'}"""

                # حفظ في قاعدة البيانات
                save_notification_to_db(
                    'عقد',
                    contract[1],  # ContractCode
                    f"عقد للعميل {contract[7] or 'غير محدد'}",
                    contract[4],  # BranchId
                    contract[5],  # BranchName
                    contract[0],  # ContractId (المعرف الفريد)
                    contract[13], # AddDate
                    contract[12] or 'غير محدد'  # AddedByFullName
                )

                # إرسال الإشعار
                send_notification_sync(message)
                notifications_sent += 1
                print(f"✅ تم إرسال إشعار للعقد {contract[1]} (ID: {contract[0]})")

        conn.close()

        if notifications_sent > 0:
            print(f"📊 تم إرسال {notifications_sent} إشعار عقد جديد")
            return None  # لا نرسل رسالة إضافية لأن كل عقد أرسل إشعار منفصل

        return None

    except Exception as e:
        error_msg = f"❌ خطأ في فحص العقود الجديدة: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)
        return None

# تم حذف دالة initialize_last_ids() غير المستخدمة

async def monitor_all_data():
    """مراقبة جميع البيانات مع التفاصيل الشاملة"""
    start_msg = "🔍 بدء مراقبة شاملة لجميع البيانات..."
    print(start_msg)
    print("📊 سيتم عرض تفاصيل كاملة مع الربط بجميع الجداول")
    print("⚠️ قراءة فقط - لا يتم كتابة أي بيانات")

    if logger:
        logger.info(start_msg)
        logger.info("📊 سيتم عرض تفاصيل كاملة مع الربط بجميع الجداول")

    # تهيئة قاعدة بيانات الإشعارات
    init_notifications_db()

    # 🧪 اختبار تقرير العميل عند بدء المراقبة (معطل)
    # print("🧪 اختبار تقرير العقود والمدفوعات...")
    # try:
    #     test_customer_code = "1213"  # يمكن تغيير كود العميل هنا
    #     print(f"📋 إرسال تقرير تجريبي للعميل: {test_customer_code}")
    #
    #     # إرسال التقرير
    #     result = send_customer_contracts_report(test_customer_code)
    #
    #     if result:
    #         print("✅ تم إرسال تقرير العميل التجريبي بنجاح")
    #         if logger:
    #             logger.info(f"تم إرسال تقرير العميل التجريبي {test_customer_code}")
    #     else:
    #         print("⚠️ لم يتم إرسال تقرير العميل التجريبي")
    #
    # except Exception as e:
    #     error_msg = f"❌ خطأ في اختبار تقرير العميل: {str(e)}"
    #     print(error_msg)
    #     if logger:
    #         logger.error(error_msg)

    print("🚫 تم تعطيل الاختبار التلقائي لتقرير العقود والمدفوعات")

    cycle = 0

    while True:
        try:
            cycle += 1
            current_time = datetime.now().strftime('%H:%M:%S')
            cycle_msg = f"🔄 دورة مراقبة شاملة #{cycle} - {current_time}"
            print(f"\n{cycle_msg}")

            if logger and cycle % 10 == 1:  # كل 10 دورات
                logger.info(cycle_msg)

            # فحص العملاء الجدد
            new_customer = check_new_customers()
            if new_customer:
                discovery_msg = "🔔 تم اكتشاف عميل جديد!"
                print(discovery_msg)
                if logger:
                    logger.info(discovery_msg)
                await send_notification(new_customer)

            # فحص المعاينات الجديدة
            new_preview = check_new_previews()
            if new_preview:
                discovery_msg = "🔔 تم اكتشاف معاينة جديدة!"
                print(discovery_msg)
                if logger:
                    logger.info(discovery_msg)
                await send_notification(new_preview)

            # فحص الاجتماعات الجديدة
            new_meeting = check_new_meetings()
            if new_meeting:
                discovery_msg = "🔔 تم اكتشاف اجتماع جديد!"
                print(discovery_msg)
                if logger:
                    logger.info(discovery_msg)
                await send_notification(new_meeting)

            # فحص التصميمات الجديدة
            new_design = check_new_designs()
            if new_design:
                discovery_msg = "🔔 تم اكتشاف تصميم جديد!"
                print(discovery_msg)
                if logger:
                    logger.info(discovery_msg)
                await send_notification(new_design)

            # فحص العقود الجديدة
            new_contract = check_new_contracts()
            if new_contract:
                discovery_msg = "🔔 تم اكتشاف عقد جديد!"
                print(discovery_msg)
                if logger:
                    logger.info(discovery_msg)
                await send_notification(new_contract)

            # فحص التقارير المجدولة (كل دورة للدقة في التوقيت)
            try:
                check_scheduled_reports()
                # فحص التقارير الإحصائية المجدولة الجديدة
                statistical_reports_sent = check_scheduled_statistical_reports()
                if statistical_reports_sent > 0:
                    print(f"📊 تم إرسال {statistical_reports_sent} تقرير إحصائي مجدول")

                # إضافة فحص تقارير التصميمات
                check_and_send_designs_without_files_report()

                # فحص تحديثات العقود والمدفوعات (مع آلية منع التكرار)
                updates_count = check_contracts_and_payments_updates_smart()
                if updates_count and updates_count > 0:
                    print(f"📊 تم إرسال {updates_count} تحديث جديد للعقود والمدفوعات")

                # فحص مواعيد الاستحقاق تم نقله إلى جدول الإشعارات المجدولة
                # لا نحتاج لفحص مباشر هنا بعد الآن
                pass

            except Exception as e:
                error_msg = f"❌ خطأ في فحص التقارير المجدولة: {str(e)}"
                print(error_msg)
                if logger:
                    logger.error(error_msg)

            if not any([new_customer, new_preview, new_meeting, new_design, new_contract]):
                no_data_msg = "📊 لا توجد بيانات جديدة"
                print(no_data_msg)
                if logger and cycle % 20 == 0:  # كل 20 دورة
                    logger.debug(no_data_msg)

            # انتظار 30 ثانية
            await asyncio.sleep(30)

        except KeyboardInterrupt:
            stop_msg = "\n⏹️ تم إيقاف المراقبة"
            print(stop_msg)
            if logger:
                logger.info(stop_msg)
            break
        except Exception as e:
            error_msg = f"❌ خطأ في المراقبة: {str(e)}"
            print(error_msg)
            if logger:
                logger.error(error_msg)
            await asyncio.sleep(60)

# دوال البوت
async def start(update, context):
    """دالة البداية"""
    if not update.effective_user or not update.message:
        return

    user_id = update.effective_user.id
    user_name = update.effective_user.first_name or "مستخدم"

    # إنشاء الكيبورد الثابت
    from telegram import ReplyKeyboardMarkup, KeyboardButton

    keyboard = [
        [KeyboardButton("📋 تقرير عميل")],
        [KeyboardButton("📅 مواعيد الاستحقاق"), KeyboardButton("👥 تقارير الفروع")],
        [KeyboardButton("🆔 معرفي")]
    ]

    reply_markup = ReplyKeyboardMarkup(
        keyboard,
        resize_keyboard=True,
        one_time_keyboard=False,
        input_field_placeholder="اختر من الأزرار أو اكتب كود العميل..."
    )

    welcome_text = f"""🌟 مرحباً بك في Terra Bot المحسن 🌟

👋 أهلاً {user_name}!

🔔 نظام الإشعارات التلقائية نشط
📊 مراقبة قاعدة البيانات مستمرة
📈 نظام التقارير المجدولة نشط

🎯 استخدم الأزرار أدناه أو:
• اكتب كود العميل مباشرة (مثل: 1213)
• اكتب رقم التليفون (مثل: 01234567890)

💡 الأوامر المتاحة:
/start - العودة للقائمة الرئيسية
/customer - تقارير العقود
/due - مواعيد الاستحقاق
/test - اختبار قاعدة البيانات
/id - عرض معرف المستخدم/الجروب

🆔 معرفك: {user_id}
⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""

    await update.message.reply_text(
        welcome_text,
        reply_markup=reply_markup
    )

async def get_id(update, context):
    """دالة عرض معرف المستخدم أو الجروب"""
    if not update.effective_user or not update.message:
        return

    try:
        user_id = update.effective_user.id
        chat_id = update.effective_chat.id
        chat_type = update.effective_chat.type

        # معلومات المستخدم
        user_info = f"👤 **معلومات المستخدم:**\n"
        user_info += f"🆔 **معرف المستخدم:** `{user_id}`\n"
        user_info += f"👤 **الاسم:** {update.effective_user.first_name or 'غير محدد'}"

        if update.effective_user.last_name:
            user_info += f" {update.effective_user.last_name}"

        if update.effective_user.username:
            user_info += f"\n📝 **اسم المستخدم:** @{update.effective_user.username}"

        # معلومات المحادثة
        chat_info = f"\n\n💬 **معلومات المحادثة:**\n"
        chat_info += f"🆔 **معرف المحادثة:** `{chat_id}`\n"

        if chat_type == "private":
            chat_info += f"📱 **نوع المحادثة:** محادثة خاصة"
        elif chat_type == "group":
            chat_info += f"👥 **نوع المحادثة:** جروب"
            if update.effective_chat.title:
                chat_info += f"\n🏷️ **اسم الجروب:** {update.effective_chat.title}"
        elif chat_type == "supergroup":
            chat_info += f"👥 **نوع المحادثة:** سوبر جروب"
            if update.effective_chat.title:
                chat_info += f"\n🏷️ **اسم الجروب:** {update.effective_chat.title}"
        elif chat_type == "channel":
            chat_info += f"📢 **نوع المحادثة:** قناة"
            if update.effective_chat.title:
                chat_info += f"\n🏷️ **اسم القناة:** {update.effective_chat.title}"

        # ملاحظة للإشعارات
        notification_note = f"\n\n🔔 **للإشعارات التلقائية:**\n"
        if chat_type == "private":
            notification_note += f"استخدم هذا المعرف: `{user_id}`"
        else:
            notification_note += f"استخدم هذا المعرف: `{chat_id}`"

        notification_note += f"\n\n💡 **ملاحظة:** أرسل هذا المعرف للمطور لإضافتك لقائمة الإشعارات التلقائية"

        full_message = user_info + chat_info + notification_note

        await update.message.reply_text(
            full_message,
            parse_mode='Markdown'
        )

        print(f"📨 طلب معرف من: {update.effective_user.first_name or 'مستخدم'} - معرف المستخدم: {user_id}, معرف المحادثة: {chat_id}")

    except Exception as e:
        print(f"❌ خطأ في دالة get_id: {str(e)}")
        await update.message.reply_text(
            "❌ حدث خطأ في الحصول على المعرف",
            parse_mode='Markdown'
        )

async def test_db(update, context):
    """اختبار قاعدة البيانات"""
    if not update.effective_user or not update.message:
        return

    await update.message.reply_text("🔄 جاري اختبار قاعدة البيانات...")

    try:
        conn = connect_to_db()
        if not conn:
            await update.message.reply_text("❌ فشل الاتصال بقاعدة البيانات")
            return

        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM Acc_Customers WHERE IsDeleted=0")
        result = cursor.fetchone()
        total_customers = result[0] if result else 0
        conn.close()

        report = f"""🔧 **معلومات قاعدة البيانات:**

🖥️ **الخادم:** `{MAIN_DB_CONFIG['server']}`
🗄️ **قاعدة البيانات الأصلية:** `{MAIN_DB_CONFIG['database']}`
🗄️ **قاعدة بيانات الإشعارات:** `{NOTIFICATIONS_DB_CONFIG['database']}`
👥 **إجمالي العملاء:** {total_customers:,} عميل

✅ **الاتصال سليم**"""

        await update.message.reply_text(report, parse_mode='Markdown')

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في فحص قاعدة البيانات: {str(e)}")

async def test_phones(update, context):
    """اختبار أرقام التليفون"""
    if not update.effective_user or not update.message:
        return

    user_id = update.effective_user.id
    if user_id not in AUTHORIZED_USERS:
        await update.message.reply_text("❌ غير مصرح لك باستخدام هذا الأمر")
        return

    await update.message.reply_text("🔄 جاري اختبار أرقام التليفون...")

    try:
        conn = connect_to_main_db()
        if not conn:
            await update.message.reply_text("❌ فشل الاتصال بقاعدة البيانات")
            return

        cursor = conn.cursor()

        # فحص أرقام التليفون للعملاء
        cursor.execute("""
            SELECT TOP 3
                CustomerCode,
                NameAr,
                MainPhoneNo,
                SubMainPhoneNo
            FROM Acc_Customers
            WHERE IsDeleted = 0
              AND (MainPhoneNo IS NOT NULL OR SubMainPhoneNo IS NOT NULL)
            ORDER BY CustomerId DESC
        """)

        customers = cursor.fetchall()
        conn.close()

        if customers:
            message = "📱 **اختبار أرقام التليفون:**\n\n"

            for customer in customers:
                code = customer[0]
                name = customer[1]
                main_phone = customer[2]
                sub_phone = customer[3]

                message += f"👤 **{name}** ({code})\n"
                message += f"📱 التليفون الأول: `{main_phone}`\n"
                message += f"📞 التليفون الثاني: `{sub_phone}`\n"

                # اختبار معالجة الرقم
                phone_for_whatsapp = ""
                if main_phone:
                    phone_for_whatsapp = str(main_phone)
                elif sub_phone:
                    phone_for_whatsapp = str(sub_phone)

                if phone_for_whatsapp:
                    clean_phone = ''.join(filter(str.isdigit, phone_for_whatsapp))
                    message += f"🧹 بعد التنظيف: `{clean_phone}`\n"

                    if len(clean_phone) >= 10:
                        if clean_phone.startswith('01') and len(clean_phone) == 11:
                            final_phone = '2' + clean_phone
                        elif clean_phone.startswith('1') and len(clean_phone) == 10:
                            final_phone = '20' + clean_phone
                        elif not clean_phone.startswith('2') and len(clean_phone) >= 10:
                            final_phone = '2' + clean_phone
                        else:
                            final_phone = clean_phone

                        message += f"✅ الرقم النهائي: `{final_phone}`\n"
                        test_message = "مرحباً، هذه رسالة اختبار من نظام إدارة العقود"
                        import urllib.parse
                        encoded_message = urllib.parse.quote(test_message, safe='')
                        whatsapp_link = f"https://wa.me/{final_phone}?text={encoded_message}"
                        message += f"🔗 [رابط WhatsApp]({whatsapp_link})\n"
                    else:
                        message += f"❌ رقم قصير: {len(clean_phone)} أرقام\n"
                else:
                    message += "❌ لا يوجد رقم\n"

                message += "\n"

            await update.message.reply_text(message, parse_mode='Markdown')
        else:
            await update.message.reply_text("❌ لا توجد أرقام تليفون")

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ: {str(e)}")

async def manage_recipients(update, context):
    """إدارة قائمة المستقبلين للإشعارات"""
    if not update.effective_user or not update.message:
        return

    try:
        conn = connect_to_notifications_db()
        if not conn:
            await update.message.reply_text("❌ لا يمكن الاتصال بقاعدة بيانات الإشعارات")
            return

        cursor = conn.cursor()
        cursor.execute("""
            SELECT ChatId, RecipientName, RecipientType, IsActive
            FROM Notification_Recipients
            ORDER BY RecipientType, RecipientName
        """)

        results = cursor.fetchall()
        conn.close()

        if results:
            report = "📋 **قائمة مستقبلي الإشعارات:**\n\n"

            active_count = 0
            inactive_count = 0

            for row in results:
                chat_id, name, recipient_type, is_active = row
                status = "✅ نشط" if is_active else "❌ معطل"
                icon = "👤" if recipient_type == 'User' else "👥"

                report += f"{icon} **{name}**\n"
                report += f"🆔 المعرف: `{chat_id}`\n"
                report += f"📊 الحالة: {status}\n\n"

                if is_active:
                    active_count += 1
                else:
                    inactive_count += 1

            report += f"📊 **الإحصائيات:**\n"
            report += f"✅ نشط: {active_count}\n"
            report += f"❌ معطل: {inactive_count}\n"
            report += f"📋 الإجمالي: {len(results)}"

        else:
            report = "⚠️ لا توجد مستقبلين مسجلين في قاعدة البيانات"

        await update.message.reply_text(report, parse_mode='Markdown')

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في عرض المستقبلين: {str(e)}")

async def show_scheduled_reports(update, context):
    """عرض التقارير المجدولة"""
    if not update.effective_user or not update.message:
        return

    try:
        conn = connect_to_notifications_db()
        if not conn:
            await update.message.reply_text("❌ لا يمكن الاتصال بقاعدة بيانات الإشعارات")
            return

        cursor = conn.cursor()

        # التحقق من وجود الجداول الجديدة
        try:
            cursor.execute("SELECT COUNT(*) FROM Report_Schedules WHERE 1=0")
            use_new_structure = True
        except:
            use_new_structure = False

        if use_new_structure:
            cursor.execute("""
                SELECT
                    s.ScheduleId,
                    s.ScheduleName,
                    t.ReportNameAr,
                    s.ScheduleType,
                    s.IntervalValue,
                    s.DaysToInclude,
                    s.BranchIds,
                    s.IsActive,
                    s.LastRunTime,
                    s.NextRunTime,
                    s.Notes,
                    t.ReportCode
                FROM Report_Schedules s
                INNER JOIN Report_Types t ON s.ReportTypeId = t.ReportTypeId
                ORDER BY s.NextRunTime
            """)
        else:
            cursor.execute("""
                SELECT ReportId, ReportName, ReportType, ScheduleType, IntervalValue,
                       DaysToInclude, BranchIds, IsActive, LastRunTime, NextRunTime, Notes, ReportType
                FROM Scheduled_Reports
                ORDER BY NextRunTime
            """)

        results = cursor.fetchall()
        conn.close()

        if results:
            report = "📊 **التقارير المجدولة:**\n\n"

            for row in results:
                if use_new_structure:
                    schedule_id, schedule_name, report_type_name, schedule_type, interval_value = row[0:5]
                    days_include, branch_ids, is_active, last_run, next_run, notes, report_code = row[5:12]
                    display_name = schedule_name
                    type_info = f"{report_type_name} ({report_code})"
                else:
                    report_id, report_name, report_type, schedule_type, interval_value = row[0:5]
                    days_include, branch_ids, is_active, last_run, next_run, notes, _ = row[5:12]
                    schedule_id = report_id
                    display_name = report_name
                    type_info = report_type

                status = "✅ نشط" if is_active else "❌ معطل"

                # تنسيق نوع الجدولة
                if schedule_type == 'hourly':
                    schedule_text = f"كل {interval_value} ساعة"
                elif schedule_type == 'daily':
                    schedule_text = f"كل {interval_value} يوم"
                elif schedule_type == 'weekly':
                    schedule_text = f"كل {interval_value} أسبوع"
                elif schedule_type == 'monthly':
                    schedule_text = f"كل {interval_value} شهر"
                else:
                    schedule_text = schedule_type

                # تنسيق الفروع
                if branch_ids:
                    branches_text = f"الفروع: {branch_ids}"
                else:
                    branches_text = "جميع الفروع"

                # تنسيق آخر تشغيل
                last_run_text = last_run.strftime('%Y-%m-%d %H:%M') if last_run else "لم يتم التشغيل بعد"
                next_run_text = next_run.strftime('%Y-%m-%d %H:%M') if next_run else "غير محدد"

                report += f"""🔹 **{display_name}** (ID: {schedule_id})
├─ 📊 النوع: {type_info}
├─ ⏰ الجدولة: {schedule_text}
├─ 📅 فترة البيانات: آخر {days_include} أيام
├─ 🏢 {branches_text}
├─ 📊 الحالة: {status}
├─ ⏮️ آخر تشغيل: {last_run_text}
└─ ⏭️ التشغيل التالي: {next_run_text}

"""

            report += f"📋 **الإجمالي:** {len(results)} تقرير مجدول"

        else:
            report = "⚠️ لا توجد تقارير مجدولة"

        await update.message.reply_text(report, parse_mode='Markdown')

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في عرض التقارير المجدولة: {str(e)}")

async def test_report(update, context):
    """اختبار إرسال تقرير إحصائي"""
    if not update.effective_user or not update.message:
        return

    await update.message.reply_text("🔄 جاري إنشاء تقرير تجريبي...")

    try:
        # إنشاء تقرير تجريبي لآخر يوم
        data = get_statistics_report(days=1)
        if data:
            formatted_report = format_statistics_report(data, 1, "تقرير تجريبي - آخر 24 ساعة")
            await update.message.reply_text(formatted_report, parse_mode='Markdown')
        else:
            await update.message.reply_text("❌ لا توجد بيانات للتقرير التجريبي")

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في إنشاء التقرير التجريبي: {str(e)}")

async def statistics_report(update, context):
    """إرسال تقرير إحصائي - الأمر /e"""
    if not update.effective_user or not update.message:
        return

    await update.message.reply_text("📊 جاري البحث عن التقارير المتاحة...")

    try:
        # الحصول على التقارير المتاحة من قاعدة البيانات
        conn = connect_to_notifications_db()
        if not conn:
            await update.message.reply_text("❌ خطأ في الاتصال بقاعدة البيانات")
            return

        cursor = conn.cursor()

        # إذا لم يتم تحديد معاملات، عرض قائمة التقارير المتاحة
        if not context.args:
            cursor.execute("""
                SELECT ReportId, ReportName, StatisticsPeriodDays,
                       CASE WHEN BranchIds IS NULL THEN N'جميع الفروع' ELSE BranchIds END as Branches,
                       CASE WHEN IsActive = 1 THEN N'نشط' ELSE N'معطل' END as Status
                FROM Statistical_Reports_Config
                ORDER BY ReportId
            """)

            reports = cursor.fetchall()
            if reports:
                report_list = "📋 **التقارير الإحصائية المتاحة:**\n\n"
                for report in reports:
                    report_list += f"🔹 **{report[0]}** - {report[1]}\n"
                    report_list += f"   📅 فترة: {report[2]} أيام | 🏢 فروع: {report[3]} | ⚡ {report[4]}\n\n"

                report_list += "💡 **لتشغيل تقرير:** `/e [رقم_التقرير]`\n"
                report_list += "مثال: `/e 1` أو `/e 2`"

                await update.message.reply_text(report_list, parse_mode='Markdown')
            else:
                await update.message.reply_text("❌ لا توجد تقارير محفوظة في النظام")

            conn.close()
            return

        # إذا تم تحديد رقم التقرير
        try:
            report_id = int(context.args[0])
        except ValueError:
            await update.message.reply_text("⚠️ يرجى إدخال رقم التقرير صحيح\n\nمثال: `/e 1`")
            conn.close()
            return

        # البحث عن التقرير المحدد
        cursor.execute("""
            SELECT ReportName, StatisticsPeriodDays, BranchIds, IsActive
            FROM Statistical_Reports_Config
            WHERE ReportId = ? AND IsActive = 1
        """, (report_id,))

        report_config = cursor.fetchone()
        conn.close()

        if not report_config:
            await update.message.reply_text(f"❌ التقرير رقم {report_id} غير موجود أو معطل")
            return

        report_name = report_config[0]
        days = report_config[1]
        branch_ids = report_config[2]

        await update.message.reply_text(f"📊 جاري إنشاء {report_name}...")

        # إنشاء التقرير
        data = get_statistics_report(days=days, branch_ids=branch_ids)
        if data:
            formatted_report = format_statistics_report(data, days, report_name, branch_ids)

            # تقسيم الرسالة إذا كانت طويلة
            if len(formatted_report) > 4000:
                # تقسيم الرسالة
                parts = []
                current_part = ""
                lines = formatted_report.split('\n')

                for line in lines:
                    if len(current_part + line + '\n') > 4000:
                        if current_part:
                            parts.append(current_part)
                            current_part = line + '\n'
                        else:
                            parts.append(line)
                    else:
                        current_part += line + '\n'

                if current_part:
                    parts.append(current_part)

                # إرسال الأجزاء
                for i, part in enumerate(parts):
                    if i == 0:
                        await update.message.reply_text(part, parse_mode='Markdown')
                    else:
                        await update.message.reply_text(f"📊 **تكملة التقرير ({i+1}/{len(parts)}):**\n\n{part}", parse_mode='Markdown')
            else:
                await update.message.reply_text(formatted_report, parse_mode='Markdown')
        else:
            await update.message.reply_text("❌ لا توجد بيانات للتقرير الإحصائي")

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في إنشاء التقرير الإحصائي: {str(e)}")

async def manage_reports(update, context):
    """إدارة التقارير الإحصائية - الأمر /manage_reports"""
    if not update.effective_user or not update.message:
        return

    try:
        conn = connect_to_notifications_db()
        if not conn:
            await update.message.reply_text("❌ خطأ في الاتصال بقاعدة البيانات")
            return

        cursor = conn.cursor()

        if not context.args:
            # عرض قائمة الإدارة
            help_text = """🔧 **إدارة التقارير الإحصائية:**

📋 **عرض جميع التقارير:**
`/manage_reports list`

➕ **إضافة تقرير جديد:**
`/manage_reports add "اسم التقرير" كود_التقرير أيام_الإحصائيات ساعة:دقيقة [فروع]`
مثال: `/manage_reports add "تقرير يومي مخصص" CUSTOM_DAILY 5 14:30 2,3`

✏️ **تعديل تقرير:**
`/manage_reports edit رقم_التقرير "الاسم_الجديد" أيام_جديدة ساعة:دقيقة [فروع]`

🗑️ **حذف تقرير:**
`/manage_reports delete رقم_التقرير`

⚡ **تفعيل/تعطيل تقرير:**
`/manage_reports toggle رقم_التقرير`

🔄 **تشغيل تقرير فوري:**
`/manage_reports run رقم_التقرير`"""

            await update.message.reply_text(help_text, parse_mode='Markdown')
            conn.close()
            return

        command = context.args[0].lower()

        if command == "list":
            # عرض جميع التقارير
            cursor.execute("""
                SELECT ReportId, ReportName, ReportCode, StatisticsPeriodDays,
                       SendTime, BranchIds, IsActive, LastSentTime, NextSendTime
                FROM Statistical_Reports_Config
                ORDER BY ReportId
            """)

            reports = cursor.fetchall()
            if reports:
                report_text = "📋 **جميع التقارير الإحصائية:**\n\n"
                for report in reports:
                    status = "🟢 نشط" if report[6] else "🔴 معطل"
                    branches = report[5] if report[5] else "جميع الفروع"
                    last_sent = report[7].strftime('%Y-%m-%d %H:%M') if report[7] else "لم يرسل بعد"
                    next_send = report[8].strftime('%Y-%m-%d %H:%M') if report[8] else "غير محدد"

                    report_text += f"🔹 **{report[0]}** - {report[1]}\n"
                    report_text += f"   📝 كود: {report[2]} | 📅 فترة: {report[3]} أيام\n"
                    report_text += f"   ⏰ وقت الإرسال: {report[4]} | 🏢 فروع: {branches}\n"
                    report_text += f"   {status} | 📤 آخر إرسال: {last_sent}\n"
                    report_text += f"   🔄 الإرسال القادم: {next_send}\n\n"

                await update.message.reply_text(report_text, parse_mode='Markdown')
            else:
                await update.message.reply_text("❌ لا توجد تقارير في النظام")

        conn.close()

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في إدارة التقارير: {str(e)}")


def get_designs_without_files_report():
    """الحصول على تقرير التصميمات الغير مرفوعة"""
    try:
        conn = connect_to_main_db()
        if not conn:
            return None

        cursor = conn.cursor()
        query = """
        SELECT 
            b.NameAr AS BranchName,
            c.NameAr AS CustomerName,
            e.NameAr AS EngineerName,
            c.CustomerCode,
            CAST(DATEDIFF(DAY, d.Date, GETDATE()) AS NVARCHAR) + N' يوم' AS DaysSinceDesign
        FROM Sys_Designs d
        LEFT JOIN Acc_Customers c ON d.CustomerId = c.CustomerId
        LEFT JOIN Emp_Employees e ON d.DesignChairperson = e.EmployeeId
        LEFT JOIN Sys_Branches b ON d.BranchId = b.BranchId
        LEFT JOIN (
            SELECT DISTINCT DesignId
            FROM Sys_Files
            WHERE IsDeleted = 0 AND DesignId IS NOT NULL
        ) f ON d.DesignId = f.DesignId
        WHERE d.IsDeleted = 0
          AND f.DesignId IS NULL
          AND b.BranchId IN (2, 3)
        ORDER BY b.BranchId, d.Date
        """
        cursor.execute(query)
        results = cursor.fetchall()
        conn.close()
        return results
    except Exception as e:
        print(f"❌ خطأ في الحصول على تقرير التصميمات الغير مرفوعة: {str(e)}")
        if logger:
            logger.error(f"خطأ في الحصول على تقرير التصميمات الغير مرفوعة: {str(e)}")
        return None

def format_designs_without_files_report(data):
    """تنسيق تقرير التصميمات الغير مرفوعة للإرسال مرتب حسب المهندسين"""
    if not data:
        return "ℹ️ لا توجد تصميمات غير مرفوعة"
    
    # تجميع البيانات حسب المهندسين
    engineers_data = {}
    branch_stats = {}
    total_count = 0
    
    for row in data:
        branch_name = row[0] or 'فرع غير محدد'
        customer_name = row[1] or 'غير محدد'
        engineer_name = row[2] or 'غير محدد'
        customer_code = row[3] or 'غير محدد'
        days_since = row[4] or 'غير محدد'
        
        # مفتاح فريد للمهندس (فرع + اسم)
        engineer_key = f"{branch_name}|{engineer_name}"
        
        if engineer_key not in engineers_data:
            engineers_data[engineer_key] = {
                'branch_name': branch_name,
                'engineer_name': engineer_name,
                'designs': []
            }
        
        engineers_data[engineer_key]['designs'].append({
            'customer_name': customer_name,
            'customer_code': customer_code,
            'days_since': days_since
        })
        
        # إحصائيات الفروع
        if branch_name not in branch_stats:
            branch_stats[branch_name] = 0
        branch_stats[branch_name] += 1
        total_count += 1
    
    # ترتيب المهندسين حسب عدد التصميمات (الأكثر أولاً)
    sorted_engineers = sorted(engineers_data.values(), 
                            key=lambda x: len(x['designs']), reverse=True)
    
    report = f"""🎨 *تقرير التصميمات الغير مرفوعة*
📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}
📊 العدد الإجمالي: {total_count} تصميم

═══════════════════════════════════

📈 *ملخص سريع:*
"""
    
    # عرض ملخص الفروع
    for branch_name, count in sorted(branch_stats.items()):
        branch_icon = "🏢" if "نصر" in branch_name else "🏬" if "تجمع" in branch_name else "🏪"
        report += f"{branch_icon} {branch_name}: {count} تصميم\n"
    
    report += f"""
═══════════════════════════════════

📋 *التفاصيل مرتبة حسب المهندسين:*

"""
    
    # عرض كل مهندس وتصاميمه
    for engineer_data in sorted_engineers:
        branch_name = engineer_data['branch_name']
        engineer_name = engineer_data['engineer_name']
        designs = engineer_data['designs']
        
        branch_icon = "🏢" if "نصر" in branch_name else "🏬" if "تجمع" in branch_name else "🏪"
        
        report += f"{branch_icon} *{branch_name}*\n"
        report += f"👨‍💼 *{engineer_name}* - {len(designs)} تصميم:\n"
        report += "─────────────────────────────────\n"
        
        # عرض كل تصميمات هذا المهندس
        for i, design in enumerate(designs, 1):
            report += f"{i}. 👤 *{design['customer_name']}*\n"
            report += f"   🔢 الكود: {design['customer_code']}\n"
            report += f"   ⏰ منذ: {design['days_since']}\n\n"
        
        report += "═══════════════════════════════════\n\n"
    
    report += f"""📊 *الإجمالي العام:* {total_count} تصميم غير مرفوع
⚠️ *يرجى متابعة رفع الملفات للتصميمات المذكورة*"""

    return report
def check_and_send_designs_without_files_report():
    """فحص وإرسال تقرير التصميمات الغير مرفوعة حسب الجدولة"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return

        cursor = conn.cursor()
        current_time = datetime.now()

        # البحث عن تقارير التصميمات المجدولة والمستحقة من الجدول الصحيح
        cursor.execute("""
            SELECT ReportId, ReportName, ReportCode, ScheduleType, IntervalDays, 
                   NextSendTime, BranchIds, StatisticsPeriodDays, IsActive
            FROM Statistical_Reports_Config 
            WHERE IsActive = 1 
            AND (ReportCode = 'DESIGNS_WITHOUT_FILES' OR ReportCode = 'DESIGNS_WITHOUT_FILES_3DAYS')
            AND NextSendTime <= ?
            ORDER BY NextSendTime
        """, (current_time,))

        scheduled_reports = cursor.fetchall()

        for report in scheduled_reports:
            report_id, report_name, report_code, schedule_type, interval_days, next_send_time, branch_ids_str, statistics_period_days, is_active = report

            try:
                print(f"🎨 تنفيذ تقرير التصميمات المجدول: {report_name}")
                
                # الحصول على بيانات التصميمات الغير مرفوعة
                data = get_designs_without_files_report()
                if data:
                    # تنسيق التقرير مع إضافة نوع التقرير
                    if interval_days == 3:
                        title_suffix = " (تقرير كل 3 أيام)"
                    else:
                        title_suffix = " (تقرير يومي)"
                    
                    formatted_report = format_designs_statistics_only(data)
                    formatted_report = formatted_report.replace("إحصائيات التصميمات", f"إحصائيات التصميمات{title_suffix}")
                    
                    # إرسال التقرير
                    send_notification_sync(formatted_report)
                    
                    print(f"✅ تم إرسال تقرير التصميمات: {report_name}")
                    if logger:
                        logger.info(f"تم إرسال تقرير التصميمات المجدول: {report_name}")
                else:
                    print(f"ℹ️ لا توجد تصميمات غير مرفوعة للتقرير: {report_name}")

                # تحديث موعد التشغيل التالي باستخدام الدالة الموجودة
                update_statistical_report_status(report_id)

            except Exception as e:
                error_msg = f"❌ خطأ في تنفيذ تقرير التصميمات {report_name}: {str(e)}"
                print(error_msg)
                if logger:
                    logger.error(error_msg)

        conn.close()

    except Exception as e:
        error_msg = f"❌ خطأ في فحص تقارير التصميمات المجدولة: {str(e)}"
        print(error_msg)
        if logger:
            logger.error(error_msg)






async def designs_without_files_command(update, context):
    """أمر لإرسال إحصائيات التصميمات الغير مرفوعة"""
    try:
        if not update.effective_user or not update.message:
            return
            
        user_id = update.effective_user.id
        
        await update.message.reply_text("🔄 جاري إعداد إحصائيات التصميمات الغير مرفوعة...")

        data = get_designs_without_files_report()
        if data:
            formatted_report = format_designs_statistics_only(data)
            await update.message.reply_text(formatted_report, parse_mode='Markdown')
            print(f"✅ تم إرسال إحصائيات التصميمات للمستخدم {user_id}")
            if logger:
                logger.info(f"تم إرسال إحصائيات التصميمات للمستخدم {user_id}")
        else:
            await update.message.reply_text("ℹ️ لا توجد تصميمات غير مرفوعة")
    except Exception as e:
        error_msg = f"❌ خطأ في إرسال الإحصائيات: {str(e)}"
        await update.message.reply_text(error_msg)
        print(error_msg)
        if logger:
            logger.error(error_msg)

async def designs_without_files_detailed_command(update, context):
    """أمر لإرسال تفاصيل التصميمات الغير مرفوعة"""
    try:
        if not update.effective_user or not update.message:
            return
            
        user_id = update.effective_user.id
        
        await update.message.reply_text("🔄 جاري إعداد تفاصيل التصميمات الغير مرفوعة...")

        data = get_designs_without_files_report()
        if data:
            formatted_report = format_designs_without_files_report(data)
            
            # تقسيم الرسالة إذا كانت طويلة
            if len(formatted_report) > 4000:
                parts = []
                current_part = ""
                lines = formatted_report.split('\n')

                for line in lines:
                    if len(current_part + line + '\n') > 4000:
                        if current_part:
                            parts.append(current_part)
                            current_part = line + '\n'
                        else:
                            parts.append(line)
                    else:
                        current_part += line + '\n'

                if current_part:
                    parts.append(current_part)

                # إرسال الأجزاء
                for i, part in enumerate(parts):
                    if i == 0:
                        await update.message.reply_text(part, parse_mode='Markdown')
                    else:
                        await update.message.reply_text(f"🎨 **تكملة التقرير ({i+1}/{len(parts)}):**\n\n{part}", parse_mode='Markdown')
            else:
                await update.message.reply_text(formatted_report, parse_mode='Markdown')
                
            print(f"✅ تم إرسال تفاصيل التصميمات للمستخدم {user_id}")
            if logger:
                logger.info(f"تم إرسال تفاصيل التصميمات للمستخدم {user_id}")
        else:
            await update.message.reply_text("ℹ️ لا توجد تصميمات غير مرفوعة")
    except Exception as e:
        error_msg = f"❌ خطأ في إرسال التفاصيل: {str(e)}"
        await update.message.reply_text(error_msg)
        print(error_msg)
        if logger:
            logger.error(error_msg)

def format_designs_statistics_only(data):
    """تنسيق إحصائيات التصميمات الغير مرفوعة فقط"""
    if not data:
        return "ℹ️ لا توجد تصميمات غير مرفوعة"
    
    # حساب إحصائيات المهندسين
    engineers_stats = {}
    branch_stats = {}
    
    for row in data:
        branch_name = row[0] or 'فرع غير محدد'
        engineer_name = row[2] or 'غير محدد'
        
        # إحصائيات المهندسين حسب الفرع
        if branch_name not in engineers_stats:
            engineers_stats[branch_name] = {}
        
        if engineer_name not in engineers_stats[branch_name]:
            engineers_stats[branch_name][engineer_name] = 0
        
        engineers_stats[branch_name][engineer_name] += 1
        
        # إحصائيات الفروع
        if branch_name not in branch_stats:
            branch_stats[branch_name] = 0
        branch_stats[branch_name] += 1
    
    report = f"""🎨 *إحصائيات التصميمات الغير مرفوعة*
📅 التاريخ: {datetime.now().strftime('%Y-%m-%d %H:%M')}
📊 العدد الإجمالي: {len(data)} تصميم

═══════════════════════════════════

📈 *إحصائيات المهندسين:*

"""

    # عرض إحصائيات المهندسين حسب الفرع
    for branch_name, engineers in engineers_stats.items():
        branch_icon = "🏢" if "نصر" in branch_name else "🏬" if "تجمع" in branch_name else "🏪"
        report += f"{branch_icon} *{branch_name}*\n"
        
        # ترتيب المهندسين حسب عدد التصميمات (الأكثر أولاً)
        sorted_engineers = sorted(engineers.items(), key=lambda x: x[1], reverse=True)
        
        for engineer_name, count in sorted_engineers:
            report += f"👨‍💼 {engineer_name}: {count} تصميم\n"
        
        report += f"📊 إجمالي الفرع: {branch_stats[branch_name]} تصميم\n\n"

    report += f"""═══════════════════════════════════
📊 *الإجمالي العام:* {len(data)} تصميم غير مرفوع

💡 *للحصول على التفاصيل الكاملة استخدم:* /de"""

    return report

async def customer_report_command(update, context):
    """أمر إرسال تقرير العقود والمدفوعات مع أزرار تفاعلية"""
    if not update.effective_user or not update.message:
        return

    try:
        # التحقق من وجود معامل (كود العميل)
        if not context.args:
            # إرسال أزرار تفاعلية
            from telegram import InlineKeyboardButton, InlineKeyboardMarkup

            keyboard = [
                [InlineKeyboardButton("👤 عميل معين", callback_data="report_single_customer")],
                [InlineKeyboardButton("👥 كل عملاء فرع", callback_data="report_all_customers")],
                [InlineKeyboardButton("⚡ عميل 1213 (سريع)", callback_data="customer_quick_1213")],
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            help_text = """📋 **تقارير العقود والمدفوعات**

🎯 **اختر نوع التقرير:**

👤 **عميل معين**: تقرير مفصل لعميل واحد
👥 **كل عملاء فرع**: تقارير جميع العملاء في فرع محدد
⚡ **سريع**: تقرير العميل 1213 مباشرة

📝 **أو استخدم الأمر مباشرة:**
`/customer [كود العميل]`

📊 **محتوى التقرير:**
• ملخص جميع العقود
• تفاصيل المدفوعات مع الرصيد التراكمي
• الأقساط المستحقة مع حالة التأخير
• إحصائيات عامة ونسبة السداد"""

            await update.message.reply_text(
                help_text,
                parse_mode='Markdown',
                reply_markup=reply_markup
            )
            return

        # الحصول على كود العميل أو رقم التليفون
        search_term = context.args[0].strip()

        if not search_term:
            await update.message.reply_text("❌ يجب تحديد كود العميل أو رقم التليفون")
            return

        # إرسال رسالة تأكيد بدء العملية
        await update.message.reply_text(f"🔍 جاري البحث عن: `{search_term}`", parse_mode='Markdown')

        # البحث الذكي والحصول على التقرير
        report_text = search_and_get_customer_report(search_term)

        # إرسال التقرير في المحادثة الحالية فقط
        await update.message.reply_text(report_text, parse_mode='Markdown')

    except Exception as e:
        error_msg = f"❌ خطأ في أمر تقرير العميل: {str(e)}"
        await update.message.reply_text(error_msg)
        if logger:
            logger.error(error_msg)

async def customer_button_callback(update, context):
    """معالج أزرار تقرير العميل"""
    query = update.callback_query
    await query.answer()

    try:
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup

        if query.data == "report_single_customer":
            # طلب كود العميل أو رقم التليفون
            keyboard = [
                [InlineKeyboardButton("عميل 1213", callback_data="customer_quick_1213")],
                [InlineKeyboardButton("عميل 1000", callback_data="customer_quick_1000")],
                [InlineKeyboardButton("🔙 رجوع", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                "👤 **تقرير عميل معين**\n\n"
                "🔍 **أرسل كود العميل أو رقم التليفون:**\n"
                "• كود العميل: `1213`\n"
                "• رقم التليفون: `01234567890`\n\n"
                "📝 **أو استخدم الأمر:**\n"
                "`/customer [كود أو تليفون]`\n\n"
                "⚡ **أو اختر من الأزرار السريعة:**",
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        elif query.data == "report_all_customers":
            # اختيار الفرع
            keyboard = [
                [InlineKeyboardButton("🏢 فرع مدينة نصر", callback_data="branch_2")],
                [InlineKeyboardButton("🏬 فرع التجمع", callback_data="branch_3")],
                [InlineKeyboardButton("🔙 رجوع", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                "👥 **تقارير كل عملاء فرع**\n\n"
                "🏢 اختر الفرع المطلوب:\n\n"
                "⚠️ **تنبيه**: سيتم إرسال تقرير منفصل لكل عميل",
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        elif query.data.startswith("customer_quick_"):
            # تقرير سريع لعميل معين
            customer_code = query.data.replace("customer_quick_", "")

            await query.edit_message_text(f"🔍 جاري إنشاء تقرير للعميل: {customer_code}")

            # الحصول على التقرير
            report_text = get_customer_contracts_report_text(customer_code)

            # إرسال التقرير في المحادثة الحالية فقط
            await query.message.reply_text(report_text, parse_mode='Markdown')

        elif query.data == "branch_customer_report":
            # تقرير عميل معين - اختيار الفرع
            keyboard = [
                [InlineKeyboardButton("🏢 التجمع", callback_data="select_branch_3")],
                [InlineKeyboardButton("🏢 مدينة نصر", callback_data="select_branch_2")],
                [InlineKeyboardButton("🔙 رجوع", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                "🏢 **تقرير عميل معين**\n\n"
                "📍 فرع إيه؟",
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        elif query.data == "branch_full_reports":
            # تقارير شاملة للفروع
            keyboard = [
                [InlineKeyboardButton("🏢 التجمع", callback_data="branch_3")],
                [InlineKeyboardButton("🏢 مدينة نصر", callback_data="branch_2")],
                [InlineKeyboardButton("🔙 رجوع", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                "🏢 **تقارير شاملة للفروع**\n\n"
                "📍 اختر الفرع:",
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        elif query.data.startswith("branch_") and query.data.replace("branch_", "").isdigit():
            # تقارير جميع عملاء فرع (فقط للأرقام)
            branch_id = int(query.data.replace("branch_", ""))
            branch_name = "مدينة نصر" if branch_id == 2 else "التجمع"

            # تأكيد الإرسال
            keyboard = [
                [InlineKeyboardButton("✅ نعم، أرسل التقارير", callback_data=f"confirm_branch_{branch_id}")],
                [InlineKeyboardButton("❌ إلغاء", callback_data="back_to_main")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                f"⚠️ **تأكيد إرسال تقارير فرع {branch_name}**\n\n"
                "سيتم إرسال تقرير منفصل لكل عميل في الفرع\n"
                "قد يستغرق هذا عدة دقائق\n\n"
                "هل تريد المتابعة؟",
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

        elif query.data.startswith("select_branch_"):
            # تم اختيار الفرع - طلب بيانات العميل
            branch_id = int(query.data.replace("select_branch_", ""))
            branch_name = "التجمع" if branch_id == 3 else "مدينة نصر"

            # حفظ معرف الفرع في context للاستخدام لاحقاً
            context.user_data['selected_branch_id'] = branch_id
            context.user_data['selected_branch_name'] = branch_name

            await query.edit_message_text(
                f"🏢 **الفرع المختار: {branch_name}**\n\n"
                "🔍 **الآن أدخل بيانات العميل:**\n\n"
                "📝 يمكنك إدخال:\n"
                "• كود العميل (مثال: 1135)\n"
                "• رقم التليفون (مثال: 01234567890)\n\n"
                f"💡 **ملاحظة:** سيتم البحث في فرع {branch_name} فقط\n"
                "🔄 **يمكنك البحث عن عملاء متعددين** في نفس الفرع\n"
                "🔙 **لتغيير الفرع:** اضغط 'تقارير الفروع' مرة أخرى",
                parse_mode='Markdown'
            )

        elif query.data.startswith("confirm_branch_"):
            # تأكيد إرسال تقارير الفرع
            branch_id = int(query.data.replace("confirm_branch_", ""))
            branch_name = "مدينة نصر" if branch_id == 2 else "التجمع"

            await query.edit_message_text(f"🔄 جاري إرسال تقارير فرع {branch_name}...")

            # إرسال التقارير في المحادثة الحالية
            result = await send_all_customers_reports_by_branch_to_chat(branch_id, query)

        elif query.data == "back_to_main":
            # العودة للقائمة الرئيسية
            keyboard = [
                [InlineKeyboardButton("👤 عميل معين", callback_data="report_single_customer")],
                [InlineKeyboardButton("👥 كل عملاء فرع", callback_data="report_all_customers")],
                [InlineKeyboardButton("⚡ عميل 1213 (سريع)", callback_data="customer_quick_1213")],
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                "📋 **تقارير العقود والمدفوعات**\n\n"
                "🎯 اختر نوع التقرير:",
                parse_mode='Markdown',
                reply_markup=reply_markup
            )

    except Exception as e:
        error_msg = f"❌ خطأ في معالج أزرار العميل: {str(e)}"
        await query.edit_message_text(error_msg)
        if logger:
            logger.error(error_msg)

async def handle_keyboard_buttons(update, context):
    """معالج الأزرار الثابتة في الكيبورد"""
    if not update.effective_user or not update.message:
        return

    try:
        text = update.message.text.strip()

        if text == "📋 تقرير عميل":
            # طلب كود العميل أو رقم التليفون
            await update.message.reply_text(
                "📋 تقرير عميل\n\n"
                "🔍 برجاء إدخال كود العميل أو رقم الهاتف:\n\n"
                "📝 أمثلة:\n"
                "• كود العميل: 1213\n"
                "• رقم التليفون: 01234567890\n"
                "• جزء من الرقم: 010\n\n"
                "✍️ اكتب الكود أو الرقم في الرسالة التالية..."
            )

        elif text == "📅 مواعيد الاستحقاق":
            # عرض أزرار مواعيد الاستحقاق
            await due_payments_command(update, context)

        elif text == "👥 تقارير الفروع":
            # مسح تحديد الفرع السابق (إن وجد) عند الدخول لقائمة تقارير الفروع
            context.user_data.pop('selected_branch_id', None)
            context.user_data.pop('selected_branch_name', None)

            # عرض أزرار تقارير الفروع
            await branches_reports_command(update, context)

        elif text == "🆔 معرفي":
            # عرض معرف المستخدم
            await get_id(update, context)



        elif text.startswith("فحص:"):
            # فحص رقم تليفون
            phone = text.replace("فحص:", "").strip()
            if phone:
                result = debug_phone_number(phone)
                await update.message.reply_text(result, parse_mode='Markdown')

        else:
            # إذا لم يكن زر، تحقق من أنه كود عميل أو رقم تليفون
            await handle_text_message(update, context)

    except Exception as e:
        error_msg = f"❌ خطأ في معالج الأزرار: {str(e)}"
        await update.message.reply_text("❌ خطأ في معالجة الطلب")
        if logger:
            logger.error(error_msg)

async def handle_text_message(update, context):
    """معالج الرسائل النصية للبحث الذكي عن العملاء"""
    if not update.effective_user or not update.message:
        return

    try:
        text = update.message.text.strip()

        # تجاهل الأوامر
        if text.startswith('/'):
            return

        # التحقق من نظام سداد الدفعات أولاً
        if context.user_data.get('waiting_for_customer_code'):
            await handle_customer_code_for_payment(update, context)
            return

        # تجاهل الرسائل القصيرة جداً أو الطويلة جداً
        if len(text) < 3 or len(text) > 20:
            return

        # التحقق من أن النص يحتوي على أرقام (كود عميل أو تليفون)
        if not any(char.isdigit() for char in text):
            return

        # التحقق من وجود فرع محدد في context
        selected_branch_id = context.user_data.get('selected_branch_id')
        selected_branch_name = context.user_data.get('selected_branch_name')

        if selected_branch_id:
            # البحث في الفرع المحدد
            await update.message.reply_text(
                f"🔍 جاري البحث عن: `{text}` في فرع {selected_branch_name}",
                parse_mode='Markdown'
            )

            # البحث الذكي مع تحديد الفرع
            result = search_and_get_customer_report_by_branch(text, selected_branch_id, selected_branch_name)

            # ملاحظة: لا نمسح بيانات الفرع المحدد حتى يمكن البحث عن عملاء آخرين في نفس الفرع
            # يمكن للمستخدم إلغاء تحديد الفرع بالضغط على "رجوع" أو اختيار فرع جديد
        else:
            # البحث العام في جميع الفروع
            await update.message.reply_text(f"🔍 جاري البحث عن: `{text}`", parse_mode='Markdown')
            result = search_and_get_customer_report(text)

        # إرسال النتيجة
        await update.message.reply_text(result, parse_mode='Markdown')

    except Exception as e:
        error_msg = f"❌ خطأ في معالج البحث النصي: {str(e)}"
        await update.message.reply_text("❌ خطأ في البحث")
        if logger:
            logger.error(error_msg)

async def handle_all_callbacks(update, context):
    """معالج موحد لجميع أزرار الـ callback"""
    query = update.callback_query
    await query.answer()

    try:
        print(f"🔍 تم الضغط على زر: {query.data}")
        # أزرار تقارير العملاء
        if (query.data.startswith("report_") or
            query.data.startswith("customer_") or
            query.data.startswith("branch_") or
            query.data.startswith("select_branch_") or
            query.data.startswith("confirm_branch_") or
            query.data == "back_to_main"):
            await customer_button_callback(update, context)

        # أزرار مواعيد الاستحقاق
        elif query.data.startswith("due_") or query.data == "customer_contracts_report":
            await due_payments_callback(update, context)

        # أزرار نظام سداد الدفعات
        elif (query.data == "payment_system" or
              query.data.startswith("pay_branch_") or
              query.data == "back_to_due_menu"):
            await payment_system_callback(update, context)

        # أزرار تأكيد السداد
        elif (query.data.startswith("pay_") or
              query.data.startswith("confirm_pay_") or
              query.data == "cancel_payment"):
            await handle_payment_confirmation(update, context)

        else:
            print(f"❌ زر غير معروف: {query.data}")
            await query.edit_message_text(f"❌ زر غير معروف: {query.data}\n\n💡 استخدم الأزرار من الكيبورد أسفل الشاشة")

    except Exception as e:
        error_msg = f"❌ خطأ في معالج الأزرار: {str(e)}"
        try:
            await query.edit_message_text("❌ خطأ في معالجة الطلب")
        except:
            await query.message.reply_text("❌ خطأ في معالجة الطلب")
        if logger:
            logger.error(error_msg)

async def due_payments_command(update, context):
    """أمر عرض مواعيد الاستحقاق"""
    if not update.effective_user or not update.message:
        return

    try:
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup

        # أزرار خيارات مواعيد الاستحقاق
        keyboard = [
            [InlineKeyboardButton("🔴 الأقساط المتأخرة", callback_data="due_overdue")],
            [InlineKeyboardButton("🟡 المستحقة اليوم", callback_data="due_today")],
            [InlineKeyboardButton("🟢 المستحقة خلال 3 أيام", callback_data="due_upcoming")],
            [InlineKeyboardButton("💰 سداد دفعات", callback_data="payment_system")],
            [InlineKeyboardButton("👤 مواعيد عميل معين", callback_data="due_customer")],
            [InlineKeyboardButton("📋 تقرير العقود والمدفوعات لعميل", callback_data="customer_contracts_report")],
            [InlineKeyboardButton("📊 تقرير شامل", callback_data="due_full_report")],
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        help_text = """📅 **مواعيد الاستحقاق**

🎯 **اختر نوع التقرير:**

🔴 **الأقساط المتأخرة**: الأقساط التي تجاوزت موعد الاستحقاق
🟡 **المستحقة اليوم**: الأقساط المستحقة اليوم
🟢 **المستحقة خلال 3 أيام**: الأقساط القادمة
👤 **مواعيد عميل معين**: جدول مواعيد عميل محدد
📊 **تقرير شامل**: جميع المواعيد مجمعة

📝 **أو استخدم الأمر مباشرة:**
`/due [كود العميل]` - لعميل معين"""

        await update.message.reply_text(
            help_text,
            parse_mode='Markdown',
            reply_markup=reply_markup
        )

    except Exception as e:
        error_msg = f"❌ خطأ في أمر مواعيد الاستحقاق: {str(e)}"
        await update.message.reply_text(error_msg)
        if logger:
            logger.error(error_msg)

async def due_payments_callback(update, context):
    """معالج أزرار مواعيد الاستحقاق"""
    query = update.callback_query
    await query.answer()

    try:
        if query.data == "due_overdue":
            # الأقساط المتأخرة
            await query.edit_message_text("🔍 جاري البحث عن الأقساط المتأخرة...")

            # تشغيل فحص مواعيد الاستحقاق
            result = check_due_payments()

            if result and result.get('overdue_count', 0) > 0:
                await query.message.reply_text(f"✅ تم إرسال تقرير الأقساط المتأخرة ({result['overdue_count']} قسط)")
            else:
                await query.message.reply_text("✅ لا توجد أقساط متأخرة حالياً")

        elif query.data == "due_today":
            # المستحقة اليوم
            await query.edit_message_text("🔍 جاري البحث عن الأقساط المستحقة اليوم...")

            conn = connect_to_main_db()
            if conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT COUNT(*)
                    FROM Sys_Payments P
                    INNER JOIN Acc_Contracts CT ON P.ContractId = CT.ContractId
                    INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
                    WHERE P.PaymentDate = CAST(GETDATE() AS DATE)
                      AND (P.IsPaid = 0 OR P.IsPaid IS NULL)
                      AND (P.IsDeleted = 0 OR P.IsDeleted IS NULL)
                      AND CT.IsDeleted = 0
                """)
                count = cursor.fetchone()[0]
                conn.close()

                if count > 0:
                    # تشغيل فحص للحصول على التفاصيل
                    check_due_payments()
                    await query.message.reply_text(f"🟡 تم إرسال تقرير الأقساط المستحقة اليوم ({count} قسط)")
                else:
                    await query.message.reply_text("✅ لا توجد أقساط مستحقة اليوم")
            else:
                await query.message.reply_text("❌ خطأ في الاتصال بقاعدة البيانات")

        elif query.data == "due_upcoming":
            # المستحقة خلال 3 أيام
            await query.edit_message_text("🔍 جاري البحث عن الأقساط المستحقة خلال 3 أيام...")

            result = check_due_payments()

            if result and result.get('upcoming_count', 0) > 0:
                await query.message.reply_text(f"🟢 تم إرسال تقرير الأقساط القادمة ({result['upcoming_count']} قسط)")
            else:
                await query.message.reply_text("✅ لا توجد أقساط مستحقة خلال الأيام القادمة")

        elif query.data == "customer_contracts_report":
            # تقرير العقود والمدفوعات لعميل
            await query.edit_message_text(
                "📋 تقرير العقود والمدفوعات لعميل\n\n"
                "🔍 برجاء إدخال كود العميل أو رقم الهاتف:\n\n"
                "📝 أمثلة:\n"
                "• كود العميل: 1213\n"
                "• رقم التليفون الأول: 01234567890\n"
                "• رقم التليفون الثاني: 01098765432\n\n"
                "✍️ اكتب الكود أو الرقم في الرسالة التالية...\n"
                "سيتم عرض التقرير الكامل للعميل مع العقود والمدفوعات والمواعيد"
            )

        elif query.data == "due_customer":
            # مواعيد عميل معين - طلب كود العميل
            await query.edit_message_text(
                "👤 مواعيد عميل معين\n\n"
                "🔍 برجاء إدخال كود العميل أو رقم الهاتف:\n\n"
                "📝 أمثلة:\n"
                "• كود العميل: 1213\n"
                "• رقم التليفون الأول: 01234567890\n"
                "• رقم التليفون الثاني: 01098765432\n\n"
                "✍️ اكتب الكود أو الرقم في الرسالة التالية...\n"
                "سيتم عرض التقرير الكامل للعميل مع العقود والمدفوعات والمواعيد"
            )

        elif query.data == "due_full_report":
            # تقرير شامل محسن
            await query.edit_message_text("📊 جاري إنشاء التقرير الشامل...")

            try:
                # الحصول على إحصائيات مفصلة
                conn = connect_to_main_db()
                if conn:
                    cursor = conn.cursor()

                    # إحصائيات الأقساط المتأخرة
                    cursor.execute("""
                        SELECT
                            COUNT(*) as OverdueCount,
                            ISNULL(SUM(P.Amount), 0) as OverdueAmount
                        FROM Sys_Payments P
                        INNER JOIN Acc_Contracts CT ON P.ContractId = CT.ContractId
                        WHERE P.PaymentDate < CAST(GETDATE() AS DATE)
                          AND (P.IsPaid = 0 OR P.IsPaid IS NULL)
                          AND (P.IsDeleted = 0 OR P.IsDeleted IS NULL)
                          AND CT.IsDeleted = 0
                    """)
                    overdue_stats = cursor.fetchone()
                    overdue_count = overdue_stats[0] if overdue_stats else 0
                    overdue_amount = overdue_stats[1] if overdue_stats else 0

                    # إحصائيات الأقساط المستحقة اليوم
                    cursor.execute("""
                        SELECT
                            COUNT(*) as TodayCount,
                            ISNULL(SUM(P.Amount), 0) as TodayAmount
                        FROM Sys_Payments P
                        INNER JOIN Acc_Contracts CT ON P.ContractId = CT.ContractId
                        WHERE P.PaymentDate = CAST(GETDATE() AS DATE)
                          AND (P.IsPaid = 0 OR P.IsPaid IS NULL)
                          AND (P.IsDeleted = 0 OR P.IsDeleted IS NULL)
                          AND CT.IsDeleted = 0
                    """)
                    today_stats = cursor.fetchone()
                    today_count = today_stats[0] if today_stats else 0
                    today_amount = today_stats[1] if today_stats else 0

                    # إحصائيات الأقساط القادمة (خلال 3 أيام)
                    cursor.execute("""
                        SELECT
                            COUNT(*) as UpcomingCount,
                            ISNULL(SUM(P.Amount), 0) as UpcomingAmount
                        FROM Sys_Payments P
                        INNER JOIN Acc_Contracts CT ON P.ContractId = CT.ContractId
                        WHERE P.PaymentDate > CAST(GETDATE() AS DATE)
                          AND P.PaymentDate <= DATEADD(day, 3, CAST(GETDATE() AS DATE))
                          AND (P.IsPaid = 0 OR P.IsPaid IS NULL)
                          AND (P.IsDeleted = 0 OR P.IsDeleted IS NULL)
                          AND CT.IsDeleted = 0
                    """)
                    upcoming_stats = cursor.fetchone()
                    upcoming_count = upcoming_stats[0] if upcoming_stats else 0
                    upcoming_amount = upcoming_stats[1] if upcoming_stats else 0

                    # إحصائيات جميع الأقساط المستحقة (بالكامل)
                    cursor.execute("""
                        SELECT
                            COUNT(*) as AllDueCount,
                            ISNULL(SUM(P.Amount), 0) as AllDueAmount
                        FROM Sys_Payments P
                        INNER JOIN Acc_Contracts CT ON P.ContractId = CT.ContractId
                        WHERE (P.IsPaid = 0 OR P.IsPaid IS NULL)
                          AND (P.IsDeleted = 0 OR P.IsDeleted IS NULL)
                          AND CT.IsDeleted = 0
                    """)
                    all_due_stats = cursor.fetchone()
                    all_due_count = all_due_stats[0] if all_due_stats else 0
                    all_due_amount = all_due_stats[1] if all_due_stats else 0

                    conn.close()

                    total_count = overdue_count + today_count + upcoming_count
                    total_amount = overdue_amount + today_amount + upcoming_amount

                    summary = f"""📊 تقرير مواعيد الاستحقاق الشامل

🔴 الأقساط المتأخرة:
   📋 العدد: {overdue_count:,} قسط
   💰 المبلغ: {overdue_amount:,.0f} جنيه
   
   للاطلاع على العملاء اضغط امر /q1

🟡 المستحقة اليوم:
   📋 العدد: {today_count:,} قسط
   💰 المبلغ: {today_amount:,.0f} جنيه

   للاطلاع على العملاء اضغط امر /q2

🟢 المستحقة خلال 3 أيام:
   📋 العدد: {upcoming_count:,} قسط
   💰 المبلغ: {upcoming_amount:,.0f} جنيه

   للاطلاع على العملاء اضغط امر /q3

🔵 الأقساط المستحقة بالكامل:
   📋 العدد: {all_due_count:,} قسط
   💰 المبلغ: {all_due_amount:,.0f} جنيه

   للاطلاع على العملاء اضغط امر /q4
────────────────────────────────────────

📊 الإجمالي:
   📋 إجمالي الأقساط: {total_count:,} قسط
   💰 إجمالي المبلغ: {total_amount:,.0f} جنيه

📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}

⚠️ الأولويات:
{"🔴 متابعة عاجلة للأقساط المتأخرة" if overdue_count > 0 else "✅ لا توجد أقساط متأخرة"}
{"🟡 تذكير بالأقساط المستحقة اليوم" if today_count > 0 else ""}
{"🟢 تنبيه مسبق للأقساط القادمة" if upcoming_count > 0 else ""}

✅ تم إرسال التفاصيل الكاملة للأقساط المتأخرة"""

                    await query.message.reply_text(summary)

                    # إرسال تفاصيل الأقساط المتأخرة إذا وجدت
                    if overdue_count > 0:
                        check_due_payments()

                else:
                    await query.message.reply_text("❌ خطأ في الاتصال بقاعدة البيانات")

            except Exception as e:
                await query.message.reply_text(f"❌ خطأ في إنشاء التقرير الشامل: {str(e)}")

    except Exception as e:
        error_msg = f"❌ خطأ في معالج مواعيد الاستحقاق: {str(e)}"
        await query.edit_message_text(error_msg)
        if logger:
            logger.error(error_msg)

async def q1_overdue_command(update, context):
    """أمر /q1 - عرض الأقساط المتأخرة"""
    if not update.effective_user or not update.message:
        return

    try:
        print("📋 تنفيذ أمر /q1 - الأقساط المتأخرة")
        await update.message.reply_text("🔍 جاري البحث عن الأقساط المتأخرة...")

        # تشغيل فحص مواعيد الاستحقاق
        result = check_due_payments()

        if result and result.get('overdue_count', 0) > 0:
            await update.message.reply_text(f"🔴 تم إرسال تقرير الأقساط المتأخرة ({result['overdue_count']} قسط)")
        else:
            await update.message.reply_text("✅ لا توجد أقساط متأخرة حالياً")

    except Exception as e:
        error_msg = f"❌ خطأ في أمر /q1: {str(e)}"
        print(error_msg)
        await update.message.reply_text("❌ حدث خطأ في جلب الأقساط المتأخرة")

async def q2_today_command(update, context):
    """أمر /q2 - عرض الأقساط المستحقة اليوم"""
    if not update.effective_user or not update.message:
        return

    try:
        print("📋 تنفيذ أمر /q2 - الأقساط المستحقة اليوم")
        await update.message.reply_text("🔍 جاري البحث عن الأقساط المستحقة اليوم...")

        # فحص الأقساط المستحقة اليوم
        conn = connect_to_main_db()
        if conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT COUNT(*)
                FROM Sys_Payments P
                INNER JOIN Acc_Contracts CT ON P.ContractId = CT.ContractId
                INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
                WHERE P.PaymentDate = CAST(GETDATE() AS DATE)
                  AND (P.IsPaid = 0 OR P.IsPaid IS NULL)
                  AND (P.IsDeleted = 0 OR P.IsDeleted IS NULL)
                  AND CT.IsDeleted = 0
            """)
            count = cursor.fetchone()[0]
            conn.close()

            if count > 0:
                # تشغيل فحص للحصول على التفاصيل
                check_due_payments()
                await update.message.reply_text(f"🟡 تم إرسال تقرير الأقساط المستحقة اليوم ({count} قسط)")
            else:
                await update.message.reply_text("✅ لا توجد أقساط مستحقة اليوم")
        else:
            await update.message.reply_text("❌ خطأ في الاتصال بقاعدة البيانات")

    except Exception as e:
        error_msg = f"❌ خطأ في أمر /q2: {str(e)}"
        print(error_msg)
        await update.message.reply_text("❌ حدث خطأ في جلب الأقساط المستحقة اليوم")

async def q3_upcoming_command(update, context):
    """أمر /q3 - عرض الأقساط المستحقة خلال 3 أيام"""
    if not update.effective_user or not update.message:
        return

    try:
        print("📋 تنفيذ أمر /q3 - الأقساط المستحقة خلال 3 أيام")
        await update.message.reply_text("🔍 جاري البحث عن الأقساط المستحقة خلال 3 أيام...")

        # فحص الأقساط المستحقة خلال 3 أيام
        conn = connect_to_main_db()
        if conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT COUNT(*)
                FROM Sys_Payments P
                INNER JOIN Acc_Contracts CT ON P.ContractId = CT.ContractId
                INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
                WHERE P.PaymentDate BETWEEN CAST(GETDATE() AS DATE) AND DATEADD(day, 3, CAST(GETDATE() AS DATE))
                  AND (P.IsPaid = 0 OR P.IsPaid IS NULL)
                  AND (P.IsDeleted = 0 OR P.IsDeleted IS NULL)
                  AND CT.IsDeleted = 0
            """)
            count = cursor.fetchone()[0]
            conn.close()

            if count > 0:
                # تشغيل فحص للحصول على التفاصيل
                check_due_payments()
                await update.message.reply_text(f"🟢 تم إرسال تقرير الأقساط القادمة ({count} قسط)")
            else:
                await update.message.reply_text("✅ لا توجد أقساط مستحقة خلال الأيام القادمة")
        else:
            await update.message.reply_text("❌ خطأ في الاتصال بقاعدة البيانات")

    except Exception as e:
        error_msg = f"❌ خطأ في أمر /q3: {str(e)}"
        print(error_msg)
        await update.message.reply_text("❌ حدث خطأ في جلب الأقساط القادمة")

async def q4_all_due_command(update, context):
    """أمر /q4 - عرض جميع الأقساط المستحقة"""
    if not update.effective_user or not update.message:
        return

    try:
        conn = connect_to_main_db()
        if not conn:
            await update.message.reply_text("❌ خطأ في الاتصال بقاعدة البيانات")
            return

        cursor = conn.cursor()

        # جلب جميع الأقساط المستحقة
        cursor.execute("""
            SELECT
                C.CustomerCode,
                C.NameAr AS CustomerName,
                CT.ContractCode,
                P.Amount,
                P.PaymentDate,
                CASE
                    WHEN P.PaymentDate < CAST(GETDATE() AS DATE) THEN DATEDIFF(DAY, P.PaymentDate, GETDATE())
                    WHEN P.PaymentDate = CAST(GETDATE() AS DATE) THEN 0
                    ELSE -DATEDIFF(DAY, GETDATE(), P.PaymentDate)
                END AS DaysStatus,
                B.NameAr AS BranchName,
                P.PaymentId,
                C.MainPhoneNo,
                C.SubMainPhoneNo
            FROM Sys_Payments P
            INNER JOIN Acc_Contracts CT ON P.ContractId = CT.ContractId
            INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
            INNER JOIN Sys_Branches B ON C.BranchId = B.BranchId
            WHERE (P.IsPaid = 0 OR P.IsPaid IS NULL)
              AND (P.IsDeleted = 0 OR P.IsDeleted IS NULL)
              AND CT.IsDeleted = 0
              AND C.IsDeleted = 0
            ORDER BY P.PaymentDate ASC
        """)

        all_due_payments = cursor.fetchall()
        conn.close()

        if not all_due_payments:
            await update.message.reply_text("✅ لا توجد أقساط مستحقة حالياً")
            return

        # تجميع البيانات حسب الحالة
        overdue_payments = []
        today_payments = []
        upcoming_payments = []

        for payment in all_due_payments:
            days_status = payment[5]
            if days_status > 0:  # متأخر
                overdue_payments.append(payment)
            elif days_status == 0:  # مستحق اليوم
                today_payments.append(payment)
            else:  # قادم
                upcoming_payments.append(payment)

        # إنشاء التقرير
        report = f"""🔵 **جميع الأقساط المستحقة**
📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}
📊 إجمالي الأقساط: {len(all_due_payments)} قسط

═══════════════════════════════════

"""

        # الأقساط المتأخرة - عرض جميع الأقساط
        if overdue_payments:
            report += f"🔴 **الأقساط المتأخرة ({len(overdue_payments)} قسط):**\n\n"
            for payment in overdue_payments:  # جميع الأقساط المتأخرة بدون تحديد
                customer_code, customer_name, contract_code, amount, payment_date, days_status, branch_name = payment[:7]
                report += f"👤 {customer_name}\n"
                report += f"🆔 كود: {customer_code} | 📋 عقد: {contract_code}\n"
                report += f"💰 المبلغ: {amount:,.0f} جنيه\n"
                report += f"📅 تاريخ الاستحقاق: {payment_date.strftime('%Y-%m-%d') if hasattr(payment_date, 'strftime') else payment_date}\n"
                report += f"⏰ متأخر: {days_status} يوم\n"
                report += f"🏢 الفرع: {branch_name}\n"
                report += "─" * 30 + "\n"

        # الأقساط المستحقة اليوم - عرض جميع الأقساط
        if today_payments:
            report += f"🟡 **المستحقة اليوم ({len(today_payments)} قسط):**\n\n"
            for payment in today_payments:  # جميع الأقساط المستحقة اليوم بدون تحديد
                customer_code, customer_name, contract_code, amount, payment_date, days_status, branch_name = payment[:7]
                report += f"👤 {customer_name} | 🆔 {customer_code}\n"
                report += f"💰 {amount:,.0f} جنيه | 📋 {contract_code} | 🏢 {branch_name}\n\n"

        # الأقساط القادمة - عرض جميع الأقساط
        if upcoming_payments:
            report += f"🟢 **الأقساط القادمة ({len(upcoming_payments)} قسط):**\n\n"
            for payment in upcoming_payments:  # جميع الأقساط القادمة بدون تحديد
                customer_code, customer_name, contract_code, amount, payment_date, days_status, branch_name = payment[:7]
                report += f"👤 {customer_name} | 🆔 {customer_code}\n"
                report += f"💰 {amount:,.0f} جنيه | 📅 باقي {abs(days_status)} يوم\n\n"

        # الملخص
        total_amount = sum(payment[3] for payment in all_due_payments)
        report += f"""📊 **الملخص:**
🔴 متأخرة: {len(overdue_payments)} قسط
🟡 اليوم: {len(today_payments)} قسط
🟢 قادمة: {len(upcoming_payments)} قسط
💰 إجمالي المبلغ: {total_amount:,.0f} جنيه

📊 **تم عرض جميع الأقساط المستحقة**"""

        await update.message.reply_text(report)

    except Exception as e:
        error_msg = f"❌ خطأ في جلب الأقساط المستحقة: {str(e)}"
        print(error_msg)
        await update.message.reply_text("❌ حدث خطأ في جلب الأقساط المستحقة")

async def payment_system_callback(update, context):
    """معالج نظام سداد الدفعات"""
    query = update.callback_query
    await query.answer()

    try:
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup

        if query.data == "payment_system":
            # اختيار الفرع
            keyboard = [
                [InlineKeyboardButton("🏢 فرع التجمع", callback_data="pay_branch_3")],
                [InlineKeyboardButton("🏢 فرع مدينة نصر", callback_data="pay_branch_2")],
                [InlineKeyboardButton("🔙 رجوع", callback_data="back_to_due_menu")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                "💰 **نظام سداد الدفعات**\n\n"
                "🏢 اختر الفرع أولاً:",
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )

        elif query.data.startswith("pay_branch_"):
            # حفظ الفرع المختار واطلب كود العميل
            branch_id = int(query.data.replace("pay_branch_", ""))
            branch_name = "التجمع" if branch_id == 3 else "مدينة نصر"

            # حفظ الفرع في context
            context.user_data['payment_branch_id'] = branch_id
            context.user_data['payment_branch_name'] = branch_name

            await query.edit_message_text(
                f"💰 **سداد دفعات - فرع {branch_name}**\n\n"
                "👤 أرسل كود العميل الآن:",
                parse_mode='Markdown'
            )

            # تعيين حالة انتظار كود العميل
            context.user_data['waiting_for_customer_code'] = True

        elif query.data == "back_to_due_menu":
            # العودة لقائمة مواعيد الاستحقاق
            keyboard = [
                [InlineKeyboardButton("🔴 الأقساط المتأخرة", callback_data="due_overdue")],
                [InlineKeyboardButton("🟡 المستحقة اليوم", callback_data="due_today")],
                [InlineKeyboardButton("🟢 المستحقة خلال 3 أيام", callback_data="due_upcoming")],
                [InlineKeyboardButton("💰 سداد دفعات", callback_data="payment_system")],
                [InlineKeyboardButton("👤 مواعيد عميل معين", callback_data="due_customer")],
                [InlineKeyboardButton("📋 تقرير العقود والمدفوعات لعميل", callback_data="customer_contracts_report")],
                [InlineKeyboardButton("📊 تقرير شامل", callback_data="due_full_report")],
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                "📅 **مواعيد الاستحقاق**\n\n"
                "اختر نوع التقرير المطلوب:",
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )

    except Exception as e:
        error_msg = f"❌ خطأ في نظام سداد الدفعات: {str(e)}"
        print(error_msg)
        await query.edit_message_text("❌ حدث خطأ في نظام سداد الدفعات")

async def handle_customer_code_for_payment(update, context):
    """التعامل مع كود العميل لنظام السداد"""
    if not context.user_data.get('waiting_for_customer_code'):
        return

    try:
        customer_code = update.message.text.strip()
        branch_id = context.user_data.get('payment_branch_id')
        branch_name = context.user_data.get('payment_branch_name')

        if not customer_code.isdigit():
            await update.message.reply_text("❌ يرجى إدخال كود العميل (أرقام فقط)")
            return

        # البحث عن العميل والدفعات المستحقة عليه
        conn = connect_to_main_db()
        if not conn:
            await update.message.reply_text("❌ خطأ في الاتصال بقاعدة البيانات")
            return

        cursor = conn.cursor()

        # جلب بيانات العميل والدفعات المستحقة
        cursor.execute("""
            SELECT
                C.CustomerCode,
                C.NameAr AS CustomerName,
                P.PaymentId,
                P.Amount,
                P.PaymentDate,
                CT.ContractCode,
                CASE
                    WHEN P.PaymentDate < CAST(GETDATE() AS DATE) THEN DATEDIFF(DAY, P.PaymentDate, GETDATE())
                    WHEN P.PaymentDate = CAST(GETDATE() AS DATE) THEN 0
                    ELSE -DATEDIFF(DAY, GETDATE(), P.PaymentDate)
                END AS DaysStatus
            FROM Sys_Payments P
            INNER JOIN Acc_Contracts CT ON P.ContractId = CT.ContractId
            INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
            WHERE C.CustomerCode = ?
              AND C.BranchId = ?
              AND (P.IsPaid = 0 OR P.IsPaid IS NULL)
              AND (P.IsDeleted = 0 OR P.IsDeleted IS NULL)
              AND CT.IsDeleted = 0
              AND C.IsDeleted = 0
            ORDER BY P.PaymentDate ASC
        """, (customer_code, branch_id))

        payments = cursor.fetchall()
        conn.close()

        if not payments:
            await update.message.reply_text(
                f"❌ لم يتم العثور على دفعات مستحقة للعميل {customer_code} في فرع {branch_name}"
            )
            context.user_data.clear()
            return

        # عرض الدفعات المستحقة مع أزرار للاختيار
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup

        customer_name = payments[0][1]

        message_text = f"💰 **دفعات العميل {customer_name}**\n"
        message_text += f"🆔 كود العميل: {customer_code}\n"
        message_text += f"🏢 الفرع: {branch_name}\n\n"
        message_text += "📋 **الدفعات المستحقة:**\n\n"

        keyboard = []

        for payment in payments:
            customer_code, customer_name, payment_id, amount, payment_date, contract_code, days_status = payment

            # تحديد حالة الدفعة
            if days_status > 0:
                status = f"🔴 متأخرة {days_status} يوم"
            elif days_status == 0:
                status = "🟡 مستحقة اليوم"
            else:
                status = f"🟢 باقي {abs(days_status)} يوم"

            message_text += f"💰 {amount:,.0f} جنيه | 📋 عقد {contract_code}\n"
            message_text += f"📅 {payment_date.strftime('%Y-%m-%d') if hasattr(payment_date, 'strftime') else payment_date} | {status}\n"
            message_text += "─" * 30 + "\n"

            # إضافة زرار للدفعة
            keyboard.append([InlineKeyboardButton(
                f"💰 سداد {amount:,.0f} جنيه - عقد {contract_code}",
                callback_data=f"pay_{payment_id}"
            )])

        # إضافة زرار إلغاء
        keyboard.append([InlineKeyboardButton("❌ إلغاء", callback_data="cancel_payment")])

        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(
            message_text,
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )

        # مسح حالة انتظار كود العميل
        context.user_data['waiting_for_customer_code'] = False

    except Exception as e:
        error_msg = f"❌ خطأ في جلب دفعات العميل: {str(e)}"
        print(error_msg)
        await update.message.reply_text("❌ حدث خطأ في جلب دفعات العميل")
        context.user_data.clear()

async def handle_payment_confirmation(update, context):
    """التعامل مع تأكيد السداد"""
    query = update.callback_query
    await query.answer()

    try:
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup

        if query.data.startswith("pay_"):
            # استخراج معرف الدفعة
            payment_id = int(query.data.replace("pay_", ""))

            # جلب تفاصيل الدفعة
            conn = connect_to_main_db()
            if not conn:
                await query.edit_message_text("❌ خطأ في الاتصال بقاعدة البيانات")
                return

            cursor = conn.cursor()

            cursor.execute("""
                SELECT
                    P.PaymentId,
                    P.Amount,
                    P.PaymentDate,
                    C.CustomerCode,
                    C.NameAr AS CustomerName,
                    CT.ContractCode,
                    B.NameAr AS BranchName
                FROM Sys_Payments P
                INNER JOIN Acc_Contracts CT ON P.ContractId = CT.ContractId
                INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
                INNER JOIN Sys_Branches B ON C.BranchId = B.BranchId
                WHERE P.PaymentId = ?
                  AND (P.IsPaid = 0 OR P.IsPaid IS NULL)
                  AND (P.IsDeleted = 0 OR P.IsDeleted IS NULL)
            """, (payment_id,))

            payment_details = cursor.fetchone()
            conn.close()

            if not payment_details:
                await query.edit_message_text("❌ لم يتم العثور على الدفعة أو تم سدادها مسبقاً")
                return

            payment_id, amount, payment_date, customer_code, customer_name, contract_code, branch_name = payment_details

            # عرض تأكيد السداد
            keyboard = [
                [InlineKeyboardButton("✅ تأكيد السداد", callback_data=f"confirm_pay_{payment_id}")],
                [InlineKeyboardButton("❌ إلغاء", callback_data="cancel_payment")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            confirmation_text = f"⚠️ **تأكيد سداد الدفعة**\n\n"
            confirmation_text += f"👤 العميل: {customer_name}\n"
            confirmation_text += f"🆔 كود العميل: {customer_code}\n"
            confirmation_text += f"📋 رقم العقد: {contract_code}\n"
            confirmation_text += f"💰 المبلغ: {amount:,.0f} جنيه\n"
            confirmation_text += f"📅 تاريخ الاستحقاق: {payment_date.strftime('%Y-%m-%d') if hasattr(payment_date, 'strftime') else payment_date}\n"
            confirmation_text += f"🏢 الفرع: {branch_name}\n\n"
            confirmation_text += "هل تريد تأكيد سداد هذه الدفعة؟"

            await query.edit_message_text(
                confirmation_text,
                reply_markup=reply_markup,
                parse_mode='Markdown'
            )

        elif query.data.startswith("confirm_pay_"):
            # تنفيذ السداد
            payment_id = int(query.data.replace("confirm_pay_", ""))

            conn = connect_to_main_db()
            if not conn:
                await query.edit_message_text("❌ خطأ في الاتصال بقاعدة البيانات")
                return

            cursor = conn.cursor()

            try:
                # بدء المعاملة (Transaction) لضمان تنفيذ جميع العمليات معاً
                cursor.execute("BEGIN TRANSACTION")

                # جلب تفاصيل الدفعة قبل التحديث
                cursor.execute("""
                    SELECT
                        P.Amount,
                        P.ContractId,
                        C.CustomerCode,
                        C.NameAr AS CustomerName,
                        C.CustomerId,
                        CT.ContractCode,
                        B.NameAr AS BranchName,
                        B.BranchId
                    FROM Sys_Payments P
                    INNER JOIN Acc_Contracts CT ON P.ContractId = CT.ContractId
                    INNER JOIN Acc_Customers C ON CT.CustomerId = C.CustomerId
                    INNER JOIN Sys_Branches B ON C.BranchId = B.BranchId
                    WHERE P.PaymentId = ? AND (P.IsPaid = 0 OR P.IsPaid IS NULL)
                """, (payment_id,))

                payment_info = cursor.fetchone()

                if not payment_info:
                    cursor.execute("ROLLBACK TRANSACTION")
                    conn.close()
                    await query.edit_message_text("❌ الدفعة غير موجودة أو مسددة مسبقاً")
                    return

                amount, contract_id, customer_code, customer_name, customer_id, contract_code, branch_name, branch_id = payment_info

                # تحديث حالة الدفعة إلى مسددة
                cursor.execute("""
                    UPDATE Sys_Payments
                    SET IsPaid = 1, UpdateDate = GETDATE()
                    WHERE PaymentId = ?
                """, (payment_id,))

                # الحصول على رقم سند جديد
                cursor.execute("""
                    SELECT ISNULL(MAX(VoucherId), 0) + 1
                    FROM Acc_Transactions
                    WHERE BranchId = ?
                """, (branch_id,))
                voucher_id = cursor.fetchone()[0]

                # إنشاء القيود المحاسبية
                transaction_date = datetime.now().strftime('%Y-%m-%d')

                # القيد الأول: مدين نقدية الصندوق (حساب 8)
                cursor.execute("""
                    INSERT INTO Acc_Transactions (
                        TransactionDate, VoucherId, Type, LineNumber,
                        MainAccountId, SubAccountId, Credit, Depit,
                        BranchId, ContractId, Notes, AddDate, AddUser, IsDeleted
                    ) VALUES (?, ?, 6, 1, 8, ?, 0.00, ?, ?, ?, ?, GETDATE(), 1, 0)
                """, (
                    transaction_date, voucher_id,
                    4551 if branch_id == 2 else 8,  # حساب فرعي للخزينة حسب الفرع
                    amount, branch_id, contract_id,
                    f"تحصيل قسط من العميل {customer_name} - عقد {contract_code}"
                ))

                # القيد الثاني: دائن حساب العملاء (حساب 9)
                # البحث عن حساب العميل الفرعي
                cursor.execute("""
                    SELECT AccountId
                    FROM Acc_Accounts
                    WHERE NameAr LIKE ? AND BranchId = ?
                """, (f"%{customer_name}%", branch_id))

                customer_account_result = cursor.fetchone()
                customer_account_id = customer_account_result[0] if customer_account_result else 9

                cursor.execute("""
                    INSERT INTO Acc_Transactions (
                        TransactionDate, VoucherId, Type, LineNumber,
                        MainAccountId, SubAccountId, Credit, Depit,
                        BranchId, ContractId, Notes, AddDate, AddUser, IsDeleted
                    ) VALUES (?, ?, 6, 2, 9, ?, ?, 0.00, ?, ?, ?, GETDATE(), 1, 0)
                """, (
                    transaction_date, voucher_id,
                    customer_account_id, amount, branch_id, contract_id,
                    f"تحصيل قسط من العميل {customer_name} - عقد {contract_code}"
                ))

                # تأكيد المعاملة
                cursor.execute("COMMIT TRANSACTION")
                conn.close()

                success_text = f"✅ **تم السداد بنجاح!**\n\n"
                success_text += f"👤 العميل: {customer_name}\n"
                success_text += f"🆔 كود العميل: {customer_code}\n"
                success_text += f"📋 رقم العقد: {contract_code}\n"
                success_text += f"💰 المبلغ المسدد: {amount:,.0f} جنيه\n"
                success_text += f"🏢 الفرع: {branch_name}\n"
                success_text += f"🕐 وقت السداد: {datetime.now().strftime('%Y-%m-%d %H:%M')}\n"
                success_text += f"📄 رقم السند: {voucher_id}\n\n"
                success_text += "💚 تم تسجيل السداد والقيود المحاسبية في النظام بنجاح"

                await query.edit_message_text(success_text, parse_mode='Markdown')

            except Exception as e:
                # في حالة حدوث خطأ، إلغاء جميع التغييرات
                cursor.execute("ROLLBACK TRANSACTION")
                conn.close()
                error_msg = f"❌ خطأ في تسجيل السداد: {str(e)}"
                print(error_msg)
                await query.edit_message_text("❌ حدث خطأ في تسجيل السداد والقيود المحاسبية")

        elif query.data == "cancel_payment":
            await query.edit_message_text("❌ تم إلغاء عملية السداد")

    except Exception as e:
        error_msg = f"❌ خطأ في تأكيد السداد: {str(e)}"
        print(error_msg)
        await query.edit_message_text("❌ حدث خطأ في عملية السداد")

def create_new_schedule_tables():
    """إنشاء الجداول الجديدة للنظام المحسن"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            print("❌ خطأ في الاتصال بقاعدة البيانات")
            return False

        cursor = conn.cursor()

        # جدول أنواع التوقيتات
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ScheduleTypes' AND xtype='U')
            CREATE TABLE ScheduleTypes (
                TypeId INT PRIMARY KEY,
                TypeName NVARCHAR(50) NOT NULL,
                IntervalMinutes INT NOT NULL,
                Description NVARCHAR(100)
            )
        """)

        # جدول الأوامر المجدولة
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ScheduledCommands' AND xtype='U')
            CREATE TABLE ScheduledCommands (
                CommandId INT IDENTITY(1,1) PRIMARY KEY,
                Command NVARCHAR(10) NOT NULL,
                TypeId INT NOT NULL,
                StartTime DATETIME NOT NULL,
                NextSendTime DATETIME NOT NULL,
                LastSentTime DATETIME NULL,
                IsActive BIT DEFAULT 1,
                CreatedDate DATETIME DEFAULT GETDATE(),
                FOREIGN KEY (TypeId) REFERENCES ScheduleTypes(TypeId)
            )
        """)

        # إدراج أنواع التوقيتات الأساسية
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM ScheduleTypes WHERE TypeId = 1)
            INSERT INTO ScheduleTypes (TypeId, TypeName, IntervalMinutes, Description) VALUES
            (1, N'بالدقائق', 5, N'كل 5 دقائق'),
            (2, N'يومي', 1440, N'كل يوم'),
            (3, N'أسبوعي', 10080, N'كل أسبوع'),
            (4, N'شهري', 43200, N'كل شهر')
        """)

        # إدراج الأوامر الأساسية
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM ScheduledCommands WHERE Command = 'q')
            INSERT INTO ScheduledCommands (Command, TypeId, StartTime, NextSendTime) VALUES
            ('q', 1, GETDATE(), DATEADD(MINUTE, 5, GETDATE())),
            ('q1', 2, GETDATE(), DATEADD(DAY, 1, GETDATE())),
            ('q2', 2, GETDATE(), DATEADD(DAY, 1, GETDATE())),
            ('q3', 2, GETDATE(), DATEADD(DAY, 1, GETDATE())),
            ('q4', 3, GETDATE(), DATEADD(WEEK, 1, GETDATE()))
        """)

        conn.commit()
        conn.close()

        print("✅ تم إنشاء الجداول الجديدة بنجاح")
        return True

    except Exception as e:
        print(f"❌ خطأ في إنشاء الجداول: {str(e)}")
        return False

async def q_command(update, context):
    """أمر /q - التقرير الشامل اليدوي"""
    if not update.effective_user or not update.message:
        return

    try:
        await update.message.reply_text("📊 جاري إنشاء التقرير الشامل...")

        # الحصول على التقرير الشامل
        result = get_due_payments_summary()
        if result:
            summary_msg = f"""📊 تقرير مواعيد الاستحقاق الشامل

🔴 الأقساط المتأخرة:
   📋 العدد: {result.get('overdue_count', 0)} قسط
   💰 المبلغ: {result.get('overdue_amount', 0):,.0f} جنيه
   للاطلاع على العملاء اضغط امر /q1

🟡 المستحقة اليوم:
   📋 العدد: {result.get('today_count', 0)} قسط
   💰 المبلغ: {result.get('today_amount', 0):,.0f} جنيه
   للاطلاع على العملاء اضغط امر /q2

🟢 المستحقة خلال 3 أيام:
   📋 العدد: {result.get('upcoming_count', 0)} قسط
   💰 المبلغ: {result.get('upcoming_amount', 0):,.0f} جنيه
   للاطلاع على العملاء اضغط امر /q3

🔵 الأقساط المستحقة بالكامل:
   📋 العدد: {result.get('all_due_count', 0)} قسط
   💰 المبلغ: {result.get('all_due_amount', 0):,.0f} جنيه
   للاطلاع على العملاء اضغط امر /q4

────────────────────────────────────────

📊 الإجمالي:
   📋 إجمالي الأقساط: {result.get('total_count', 0)} قسط
   💰 إجمالي المبلغ: {result.get('total_amount', 0):,.0f} جنيه

📅 تاريخ التقرير: {datetime.now().strftime('%Y-%m-%d %H:%M')}

⚠️ الأولويات:
{"🔴 متابعة عاجلة للأقساط المتأخرة" if result.get('overdue_count', 0) > 0 else "✅ لا توجد أقساط متأخرة"}"""

            await update.message.reply_text(summary_msg)
        else:
            await update.message.reply_text("❌ خطأ في جلب بيانات التقرير")

    except Exception as e:
        error_msg = f"❌ خطأ في التقرير الشامل: {str(e)}"
        print(error_msg)
        await update.message.reply_text("❌ حدث خطأ في إنشاء التقرير")

def get_scheduled_commands():
    """جلب الأوامر المجدولة المستحقة للتنفيذ"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return []

        cursor = conn.cursor()

        cursor.execute("""
            SELECT
                sc.CommandId,
                sc.Command,
                st.TypeName,
                sc.NextSendTime,
                st.IntervalMinutes,
                sc.CustomMessage,
                sc.LastSentTime
            FROM ScheduledCommands sc
            INNER JOIN ScheduleTypes st ON sc.TypeId = st.TypeId
            WHERE sc.IsActive = 1
              AND sc.NextSendTime <= GETDATE()
              AND (
                  sc.LastSentTime IS NULL
                  OR sc.LastSentTime < sc.NextSendTime
                  OR DATEDIFF(MINUTE, sc.LastSentTime, GETDATE()) >= st.IntervalMinutes
              )
            ORDER BY sc.NextSendTime
        """)

        commands = cursor.fetchall()
        conn.close()

        return commands

    except Exception as e:
        print(f"❌ خطأ في جلب الأوامر المجدولة: {str(e)}")
        return []

def update_next_send_time(command_id, interval_minutes):
    """تحديث موعد الإرسال التالي"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return False

        cursor = conn.cursor()

        cursor.execute("""
            UPDATE ScheduledCommands
            SET NextSendTime = DATEADD(MINUTE, ?, GETDATE()),
                LastSentTime = GETDATE()
            WHERE CommandId = ?
        """, (interval_minutes, command_id))

        conn.commit()
        conn.close()

        return True

    except Exception as e:
        print(f"❌ خطأ في تحديث موعد الإرسال: {str(e)}")
        return False

def update_command_status(command_id, status, note):
    """تحديث حالة الأمر في قاعدة البيانات"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return False

        cursor = conn.cursor()

        cursor.execute("""
            UPDATE [Terra_Notifications_DB1].[dbo].[ScheduledCommands]
            SET Status = ?,
                Note = ?,
                UpdatedDate = GETDATE()
            WHERE CommandId = ?
        """, (status, note, command_id))

        conn.commit()
        conn.close()

        print(f"📝 تم تحديث حالة الأمر {command_id} إلى: {status}")
        return True

    except Exception as e:
        print(f"❌ خطأ في تحديث حالة الأمر: {str(e)}")
        return False

def monitor_payment_changes():
    """مراقب شامل لتتبع جميع تغييرات السداد"""
    try:
        conn = connect_to_main_db()
        if not conn:
            print("❌ خطأ في الاتصال بقاعدة البيانات للمراقبة")
            return

        cursor = conn.cursor()

        print("🔍 بدء مراقبة شاملة لتغييرات السداد...")
        print("=" * 80)

        # 1. مراقبة جدول Sys_Payments (التركيز على السداد)
        print("💳 جدول Sys_Payments - عمليات السداد الحديثة (آخر 5 دقائق):")

        # أولاً: الدفعات التي تم سدادها حديثاً
        cursor.execute("""
            SELECT TOP 10
                P.PaymentId, P.ContractId, P.Amount, P.PaymentDate, P.IsPaid,
                P.AddDate, P.UpdateDate, C.ContractCode, CU.NameAr, B.NameAr
            FROM Sys_Payments P
            INNER JOIN Acc_Contracts C ON P.ContractId = C.ContractId
            INNER JOIN Acc_Customers CU ON C.CustomerId = CU.CustomerId
            INNER JOIN Sys_Branches B ON CU.BranchId = B.BranchId
            WHERE P.IsPaid = 1
              AND P.UpdateDate >= DATEADD(MINUTE, -5, GETDATE())
            ORDER BY P.UpdateDate DESC
        """)

        paid_payments = cursor.fetchall()
        if paid_payments:
            print("  ✅ دفعات تم سدادها حديثاً:")
            for payment in paid_payments:
                payment_id, contract_id, amount, payment_date, is_paid, add_date, update_date, contract_code, customer_name, branch_name = payment
                print(f"    💰 دفعة {payment_id} | {customer_name} | عقد {contract_code} | {amount:,.0f} جنيه")
                print(f"        📅 تاريخ الاستحقاق: {payment_date} | تم السداد: {update_date}")

        # ثانياً: الدفعات الجديدة المضافة
        cursor.execute("""
            SELECT TOP 5
                P.PaymentId, P.Amount, P.PaymentDate, P.IsPaid,
                C.ContractCode, CU.NameAr
            FROM Sys_Payments P
            INNER JOIN Acc_Contracts C ON P.ContractId = C.ContractId
            INNER JOIN Acc_Customers CU ON C.CustomerId = CU.CustomerId
            WHERE P.AddDate >= DATEADD(MINUTE, -5, GETDATE())
            ORDER BY P.AddDate DESC
        """)

        new_payments = cursor.fetchall()
        if new_payments:
            print("  🆕 دفعات جديدة مضافة:")
            for payment in new_payments:
                payment_id, amount, payment_date, is_paid, contract_code, customer_name = payment
                status = "✅ مدفوع" if is_paid else "❌ غير مدفوع"
                print(f"    📋 دفعة {payment_id} | {customer_name} | عقد {contract_code} | {amount:,.0f} جنيه | {status}")

        if not paid_payments and not new_payments:
            print("  📝 لا توجد تغييرات حديثة في الدفعات")

        # 2. مراقبة جدول Acc_Transactions (الأهم للسداد)
        print("\n💰 جدول Acc_Transactions (آخر 5 دقائق):")
        cursor.execute("""
            SELECT TOP 10
                T.TransactionId, T.TransactionDate, T.VoucherId, T.Type,
                T.MainAccountId, T.SubAccountId, T.Credit, T.Depit,
                T.BranchId, T.ContractId, T.AddDate, T.Notes
            FROM Acc_Transactions T
            WHERE T.AddDate >= DATEADD(MINUTE, -5, GETDATE())
            ORDER BY T.AddDate DESC
        """)

        recent_transactions = cursor.fetchall()
        if recent_transactions:
            for transaction in recent_transactions:
                trans_id, trans_date, voucher_id, trans_type, main_acc, sub_acc, credit, depit, branch_id, contract_id, add_date, notes = transaction
                direction = f"دائن {credit:,.0f}" if credit else f"مدين {depit:,.0f}"
                print(f"  💳 معاملة {trans_id} | سند {voucher_id} | {direction} | فرع {branch_id} | عقد {contract_id}")
                print(f"      📅 تاريخ المعاملة: {trans_date} | تاريخ الإضافة: {add_date}")
                if notes:
                    print(f"      📝 ملاحظات: {notes}")
        else:
            print("  📝 لا توجد معاملات حديثة")

        # 3. البحث عن جداول الحسابات المالية الأخرى
        print("\n💰 البحث عن جداول الحسابات المالية الأخرى:")
        cursor.execute("""
            SELECT TABLE_NAME
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_TYPE = 'BASE TABLE'
            AND (TABLE_NAME LIKE '%Account%'
                 OR TABLE_NAME LIKE '%Treasury%'
                 OR TABLE_NAME LIKE '%Cash%'
                 OR TABLE_NAME LIKE '%Financial%'
                 OR TABLE_NAME LIKE '%Money%'
                 OR TABLE_NAME LIKE '%Balance%'
                 OR TABLE_NAME LIKE '%Journal%'
                 OR TABLE_NAME LIKE '%Ledger%')
            AND TABLE_NAME != 'Acc_Transactions'  -- استبعاد الجدول اللي عرضناه
            ORDER BY TABLE_NAME
        """)

        financial_tables = cursor.fetchall()
        if financial_tables:
            print("  📊 جداول مالية موجودة:")
            for table in financial_tables:
                print(f"    • {table[0]}")

                # فحص التغييرات الحديثة في كل جدول
                try:
                    cursor.execute(f"""
                        SELECT TOP 5 *
                        FROM {table[0]}
                        WHERE (UpdateDate >= DATEADD(MINUTE, -5, GETDATE())
                               OR AddDate >= DATEADD(MINUTE, -5, GETDATE())
                               OR CreateDate >= DATEADD(MINUTE, -5, GETDATE())
                               OR ModifyDate >= DATEADD(MINUTE, -5, GETDATE()))
                    """)
                    recent_records = cursor.fetchall()
                    if recent_records:
                        print(f"      🔄 {len(recent_records)} تغيير حديث")
                except:
                    # الجدول قد لا يحتوي على أعمدة التاريخ المتوقعة
                    pass
        else:
            print("  📝 لا توجد جداول مالية واضحة")

        # 3. البحث عن جداول أخرى مشتبه بها
        print("\n🔍 البحث عن جداول أخرى محتملة:")
        cursor.execute("""
            SELECT TABLE_NAME
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_TYPE = 'BASE TABLE'
            AND (TABLE_NAME LIKE '%Acc_%'
                 OR TABLE_NAME LIKE '%Fin_%'
                 OR TABLE_NAME LIKE '%Pay_%'
                 OR TABLE_NAME LIKE '%Receipt%'
                 OR TABLE_NAME LIKE '%Invoice%')
            ORDER BY TABLE_NAME
        """)

        other_tables = cursor.fetchall()
        if other_tables:
            print("  📊 جداول أخرى محتملة:")
            for table in other_tables:
                print(f"    • {table[0]}")

        # 4. فحص جدول العملاء للتغييرات
        print("\n👥 جدول Acc_Customers (آخر 5 دقائق):")
        try:
            cursor.execute("""
                SELECT TOP 5 CustomerId, NameAr, UpdateDate, AddDate
                FROM Acc_Customers
                WHERE UpdateDate >= DATEADD(MINUTE, -5, GETDATE())
                   OR AddDate >= DATEADD(MINUTE, -5, GETDATE())
                ORDER BY COALESCE(UpdateDate, AddDate) DESC
            """)
        except:
            # في حالة عدم وجود UpdateDate أو AddDate
            cursor.execute("""
                SELECT TOP 5 CustomerId, NameAr, NULL as UpdateDate, NULL as AddDate
                FROM Acc_Customers
                ORDER BY CustomerId DESC
            """)

        recent_customers = cursor.fetchall()
        if recent_customers:
            for customer in recent_customers:
                print(f"  👤 عميل {customer[0]} | {customer[1]} | آخر تحديث: {customer[2] or customer[3] or 'غير محدد'}")
        else:
            print("  📝 لا توجد تغييرات حديثة")

        # 5. فحص جدول العقود للتغييرات
        print("\n📋 جدول Acc_Contracts (آخر 5 دقائق):")
        cursor.execute("""
            SELECT TOP 5 ContractCode, TotalValue, UpdateDate, AddDate
            FROM Acc_Contracts
            WHERE UpdateDate >= DATEADD(MINUTE, -5, GETDATE())
               OR AddDate >= DATEADD(MINUTE, -5, GETDATE())
            ORDER BY COALESCE(UpdateDate, AddDate) DESC
        """)

        recent_contracts = cursor.fetchall()
        if recent_contracts:
            for contract in recent_contracts:
                print(f"  📋 عقد {contract[0]} | {contract[1]:,.0f} جنيه | آخر تحديث: {contract[2] or contract[3]}")
        else:
            print("  📝 لا توجد تغييرات حديثة")

        print("=" * 80)
        print("🎯 جاهز لمراقبة عملية السداد الشاملة!")
        print("📋 سأراقب جميع الجداول المالية والمحاسبية")
        print("=" * 80)

        conn.close()

    except Exception as e:
        print(f"❌ خطأ في مراقب السداد: {str(e)}")
        import traceback
        print(f"❌ تفاصيل الخطأ: {traceback.format_exc()}")

async def start_payment_monitoring(update, context):
    """أمر بدء مراقبة السداد"""
    if not update.effective_user or not update.message:
        return

    try:
        await update.message.reply_text("🔍 بدء مراقبة تغييرات السداد...")

        # تشغيل المراقب
        monitor_payment_changes()

        await update.message.reply_text("""✅ تم تشغيل مراقب السداد!

🎯 **الآن يمكنك عمل عملية سداد وسأراقب التغييرات**

📋 سأراقب:
• تحديثات جدول Sys_Payments
• تغييرات حالة IsPaid
• أي عمليات مالية أخرى

⏰ **اعمل عملية السداد الآن وسأعرض لك التغييرات!**""")

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في بدء المراقبة: {str(e)}")

async def check_payment_changes(update, context):
    """فحص شامل للتغييرات بعد السداد"""
    if not update.effective_user or not update.message:
        return

    try:
        await update.message.reply_text("🔍 فحص شامل للتغييرات بعد السداد...")

        conn = connect_to_main_db()
        if not conn:
            await update.message.reply_text("❌ خطأ في الاتصال بقاعدة البيانات")
            return

        cursor = conn.cursor()

        message = "🔍 **التغييرات المكتشفة (آخر دقيقة):**\n\n"
        changes_found = False

        # 1. فحص عمليات السداد الحديثة (الأهم)
        cursor.execute("""
            SELECT TOP 10
                P.PaymentId, P.Amount, P.PaymentDate, P.UpdateDate,
                C.ContractCode, CU.NameAr, B.NameAr
            FROM Sys_Payments P
            INNER JOIN Acc_Contracts C ON P.ContractId = C.ContractId
            INNER JOIN Acc_Customers CU ON C.CustomerId = CU.CustomerId
            INNER JOIN Sys_Branches B ON CU.BranchId = B.BranchId
            WHERE P.IsPaid = 1
              AND P.UpdateDate >= DATEADD(MINUTE, -1, GETDATE())
            ORDER BY P.UpdateDate DESC
        """)

        paid_payments = cursor.fetchall()
        if paid_payments:
            message += "💰 **عمليات سداد حديثة:**\n"
            for payment in paid_payments:
                payment_id, amount, payment_date, update_date, contract_code, customer_name, branch_name = payment
                message += f"  ✅ دفعة {payment_id} | {customer_name} | عقد {contract_code} | {amount:,.0f} جنيه\n"
                message += f"      📅 استحقاق: {payment_date} | تم السداد: {update_date}\n"
            message += "\n"
            changes_found = True

        # 2. فحص الدفعات الجديدة المضافة
        cursor.execute("""
            SELECT TOP 5
                P.PaymentId, P.Amount, P.PaymentDate, P.IsPaid,
                C.ContractCode, CU.NameAr
            FROM Sys_Payments P
            INNER JOIN Acc_Contracts C ON P.ContractId = C.ContractId
            INNER JOIN Acc_Customers CU ON C.CustomerId = CU.CustomerId
            WHERE P.AddDate >= DATEADD(MINUTE, -1, GETDATE())
            ORDER BY P.AddDate DESC
        """)

        new_payments = cursor.fetchall()
        if new_payments:
            message += "🆕 **دفعات جديدة مضافة:**\n"
            for payment in new_payments:
                payment_id, amount, payment_date, is_paid, contract_code, customer_name = payment
                status = "✅ مدفوع" if is_paid else "❌ غير مدفوع"
                message += f"  • دفعة {payment_id} | {customer_name} | عقد {contract_code} | {amount:,.0f} جنيه | {status}\n"
            message += "\n"
            changes_found = True

        # 2. فحص تغييرات Acc_Transactions (المعاملات المالية)
        cursor.execute("""
            SELECT TOP 10
                T.TransactionId, T.VoucherId, T.Credit, T.Depit,
                T.BranchId, T.ContractId, T.AddDate, T.Notes
            FROM Acc_Transactions T
            WHERE T.AddDate >= DATEADD(MINUTE, -1, GETDATE())
            ORDER BY T.AddDate DESC
        """)

        recent_transactions = cursor.fetchall()
        if recent_transactions:
            message += "💰 **المعاملات المالية الجديدة:**\n"
            for transaction in recent_transactions:
                trans_id, voucher_id, credit, depit, branch_id, contract_id, add_date, notes = transaction
                direction = f"دائن {credit:,.0f}" if credit else f"مدين {depit:,.0f}"
                message += f"  • معاملة {trans_id} | سند {voucher_id} | {direction}"
                if contract_id:
                    message += f" | عقد {contract_id}"
                if branch_id:
                    message += f" | فرع {branch_id}"
                message += f" | {add_date}\n"
                if notes:
                    message += f"    📝 {notes}\n"
            message += "\n"
            changes_found = True

        # 2. فحص الجداول المالية المكتشفة
        cursor.execute("""
            SELECT TABLE_NAME
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_TYPE = 'BASE TABLE'
            AND (TABLE_NAME LIKE '%Account%'
                 OR TABLE_NAME LIKE '%Treasury%'
                 OR TABLE_NAME LIKE '%Transaction%'
                 OR TABLE_NAME LIKE '%Journal%')
            ORDER BY TABLE_NAME
        """)

        financial_tables = cursor.fetchall()
        for table in financial_tables:
            table_name = table[0]
            try:
                cursor.execute(f"""
                    SELECT COUNT(*)
                    FROM {table_name}
                    WHERE (UpdateDate >= DATEADD(MINUTE, -1, GETDATE())
                           OR AddDate >= DATEADD(MINUTE, -1, GETDATE())
                           OR CreateDate >= DATEADD(MINUTE, -1, GETDATE()))
                """)
                count = cursor.fetchone()[0]
                if count > 0:
                    message += f"💰 **{table_name}:** {count} تغيير حديث\n"
                    changes_found = True
            except:
                pass

        # 3. فحص تغييرات العملاء
        cursor.execute("""
            SELECT COUNT(*) FROM Acc_Customers
            WHERE UpdateDate >= DATEADD(MINUTE, -1, GETDATE())
               OR AddDate >= DATEADD(MINUTE, -1, GETDATE())
        """)
        customer_changes = cursor.fetchone()[0]
        if customer_changes > 0:
            message += f"👥 **تغييرات العملاء:** {customer_changes} تحديث\n"
            changes_found = True

        # 4. فحص تغييرات العقود (مفصل)
        cursor.execute("""
            SELECT TOP 10
                C.ContractId, C.ContractCode, C.TotalValue, C.DiscountValue,
                C.AddDate, C.UpdateDate, CU.NameAr, B.NameAr as BranchName
            FROM Acc_Contracts C
            INNER JOIN Acc_Customers CU ON C.CustomerId = CU.CustomerId
            LEFT JOIN Sys_Branches B ON CU.BranchId = B.BranchId
            WHERE C.UpdateDate >= DATEADD(MINUTE, -1, GETDATE())
               OR C.AddDate >= DATEADD(MINUTE, -1, GETDATE())
            ORDER BY COALESCE(C.UpdateDate, C.AddDate) DESC
        """)

        contract_changes = cursor.fetchall()
        if contract_changes:
            message += "📋 **تغييرات العقود:**\n"
            for contract in contract_changes:
                contract_id, contract_code, total_value, discount_value, add_date, update_date, customer_name, branch_name = contract

                if update_date and update_date >= add_date:
                    change_type = "🔄 تحديث"
                    change_date = update_date
                else:
                    change_type = "🆕 جديد"
                    change_date = add_date

                total_with_discount = (total_value or 0) + (discount_value or 0)
                message += f"  {change_type} عقد {contract_code} | {customer_name}\n"
                message += f"      💰 صافي: {total_value:,.0f} جنيه"
                if discount_value:
                    message += f" | إجمالي: {total_with_discount:,.0f} جنيه"
                message += f" | فرع: {branch_name or 'غير محدد'}\n"
                message += f"      📅 {change_date}\n"
            message += "\n"
            changes_found = True

        if not changes_found:
            message = "📝 لا توجد تغييرات حديثة في آخر دقيقة في أي من الجداول المراقبة"

        await update.message.reply_text(message)
        conn.close()

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في فحص التغييرات: {str(e)}")
        import traceback
        print(f"❌ تفاصيل الخطأ: {traceback.format_exc()}")

async def help_command(update, context):
    """أمر المساعدة"""
    help_text = """🤖 **أوامر البوت المتاحة:**

📊 **تقارير مواعيد الاستحقاق:**
• `/q` - التقرير الشامل لجميع الأقساط
• `/q1` - الأقساط المتأخرة فقط
• `/q2` - الأقساط المستحقة اليوم
• `/q3` - الأقساط المستحقة خلال 3 أيام
• `/q4` - جميع الأقساط المستحقة

📈 **تقارير إحصائية:**
• `/e [رقم]` - تقرير إحصائي (مثال: `/e 3`)
• `/testreport` - تقرير تجريبي

👤 **تقارير العملاء:**
• `/customer [كود]` - تقرير عميل (مثال: `/customer 1213`)

🔧 **أوامر النظام:**
• `/start` - بدء البوت
• `/id` - عرض معرف المحادثة
• `/test` - اختبار قاعدة البيانات
• `/help` - عرض هذه المساعدة

📊 **أوامر متقدمة:**
• `/due` - تقرير مواعيد الاستحقاق التفصيلي
• `/reports` - التقارير المجدولة
• `/branches` - تقارير الفروع

🔍 **مراقبة السداد:**
• `/monitor_pay` - بدء مراقبة السداد
• `/check_pay` - فحص تغييرات السداد
• `/payment_status` - إحصائيات وحالة السداد

🔧 **صيانة وفحص:**
• `/cleanup` - حذف الأوامر المكررة
• `/check_columns` - فحص أعمدة جدول العملاء
• `/check_notifications` - فحص جدول الإشعارات
• `/add_chat` - إضافة المحادثة الحالية للإشعارات

💡 **ملاحظة:** جميع التقارير تُرسل تلقائياً حسب الجدولة المحددة"""

    await update.message.reply_text(help_text, parse_mode='Markdown')

async def cleanup_duplicate_commands(update, context):
    """حذف الأوامر المكررة من قاعدة البيانات"""
    if not update.effective_user or not update.message:
        return

    try:
        await update.message.reply_text("🔧 بدء تنظيف الأوامر المكررة...")

        conn = connect_to_notifications_db()
        if not conn:
            await update.message.reply_text("❌ خطأ في الاتصال بقاعدة البيانات")
            return

        cursor = conn.cursor()

        # عرض الأوامر المكررة
        cursor.execute("""
            SELECT Command, COUNT(*) as Count
            FROM ScheduledCommands
            WHERE IsActive = 1
            GROUP BY Command
            HAVING COUNT(*) > 1
            ORDER BY COUNT(*) DESC
        """)

        duplicates = cursor.fetchall()

        if not duplicates:
            await update.message.reply_text("✅ لا توجد أوامر مكررة")
            conn.close()
            return

        message = "🔍 **الأوامر المكررة المكتشفة:**\n\n"
        for duplicate in duplicates:
            command, count = duplicate
            message += f"• `{command}`: {count} مرة\n"

        message += "\n🗑️ **سيتم حذف النسخ الإضافية...**\n\n"

        # حذف النسخ المكررة (الاحتفاظ بأقدم نسخة فقط)
        deleted_count = 0
        for duplicate in duplicates:
            command = duplicate[0]

            # الحصول على جميع النسخ مرتبة بالتاريخ
            cursor.execute("""
                SELECT CommandId, StartTime
                FROM ScheduledCommands
                WHERE Command = ? AND IsActive = 1
                ORDER BY StartTime ASC
            """, (command,))

            copies = cursor.fetchall()

            # حذف جميع النسخ عدا الأولى
            for i in range(1, len(copies)):
                command_id = copies[i][0]
                cursor.execute("""
                    DELETE FROM ScheduledCommands
                    WHERE CommandId = ?
                """, (command_id,))
                deleted_count += 1
                message += f"🗑️ تم حذف نسخة مكررة من `{command}` (ID: {command_id})\n"

        conn.commit()
        conn.close()

        message += f"\n✅ **تم حذف {deleted_count} أمر مكرر بنجاح!**"
        await update.message.reply_text(message, parse_mode='Markdown')

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في تنظيف الأوامر: {str(e)}")

async def payment_status_command(update, context):
    """أمر فحص حالة السداد للدفعات"""
    if not update.effective_user or not update.message:
        return

    try:
        await update.message.reply_text("🔍 فحص حالة السداد...")

        conn = connect_to_main_db()
        if not conn:
            await update.message.reply_text("❌ خطأ في الاتصال بقاعدة البيانات")
            return

        cursor = conn.cursor()

        # إحصائيات عامة
        cursor.execute("""
            SELECT
                COUNT(*) as TotalPayments,
                SUM(CASE WHEN IsPaid = 1 THEN 1 ELSE 0 END) as PaidCount,
                SUM(CASE WHEN IsPaid = 0 THEN 1 ELSE 0 END) as UnpaidCount,
                SUM(CASE WHEN IsPaid = 1 THEN Amount ELSE 0 END) as PaidAmount,
                SUM(CASE WHEN IsPaid = 0 THEN Amount ELSE 0 END) as UnpaidAmount
            FROM Sys_Payments
            WHERE PaymentDate >= DATEADD(MONTH, -1, GETDATE())
        """)

        stats = cursor.fetchone()
        total_payments, paid_count, unpaid_count, paid_amount, unpaid_amount = stats

        message = f"""📊 **إحصائيات السداد (آخر شهر):**

📈 **الإجمالي:** {total_payments:,} دفعة
✅ **مدفوع:** {paid_count:,} دفعة ({paid_amount:,.0f} جنيه)
❌ **غير مدفوع:** {unpaid_count:,} دفعة ({unpaid_amount:,.0f} جنيه)

"""

        # آخر عمليات السداد
        cursor.execute("""
            SELECT TOP 10
                P.PaymentId, P.Amount, P.PaymentDate, P.UpdateDate,
                C.ContractCode, CU.NameAr
            FROM Sys_Payments P
            INNER JOIN Acc_Contracts C ON P.ContractId = C.ContractId
            INNER JOIN Acc_Customers CU ON C.CustomerId = CU.CustomerId
            WHERE P.IsPaid = 1
              AND P.UpdateDate >= DATEADD(HOUR, -24, GETDATE())
            ORDER BY P.UpdateDate DESC
        """)

        recent_payments = cursor.fetchall()
        if recent_payments:
            message += "💰 **آخر عمليات السداد (24 ساعة):**\n\n"
            for payment in recent_payments:
                payment_id, amount, payment_date, update_date, contract_code, customer_name = payment
                message += f"✅ دفعة {payment_id} | {customer_name}\n"
                message += f"   💰 {amount:,.0f} جنيه | عقد {contract_code}\n"
                message += f"   📅 استحقاق: {payment_date.strftime('%Y-%m-%d')}\n"
                message += f"   ⏰ تم السداد: {update_date.strftime('%Y-%m-%d %H:%M')}\n\n"
        else:
            message += "📝 لا توجد عمليات سداد في آخر 24 ساعة\n\n"

        # الدفعات المتأخرة
        cursor.execute("""
            SELECT TOP 5
                P.PaymentId, P.Amount, P.PaymentDate,
                C.ContractCode, CU.NameAr,
                DATEDIFF(DAY, P.PaymentDate, GETDATE()) as DaysOverdue
            FROM Sys_Payments P
            INNER JOIN Acc_Contracts C ON P.ContractId = C.ContractId
            INNER JOIN Acc_Customers CU ON C.CustomerId = CU.CustomerId
            WHERE P.IsPaid = 0
              AND P.PaymentDate < GETDATE()
            ORDER BY P.PaymentDate ASC
        """)

        overdue_payments = cursor.fetchall()
        if overdue_payments:
            message += "⚠️ **أقدم الدفعات المتأخرة:**\n\n"
            for payment in overdue_payments:
                payment_id, amount, payment_date, contract_code, customer_name, days_overdue = payment
                message += f"❌ دفعة {payment_id} | {customer_name}\n"
                message += f"   💰 {amount:,.0f} جنيه | عقد {contract_code}\n"
                message += f"   📅 متأخرة {days_overdue} يوم (استحقاق: {payment_date.strftime('%Y-%m-%d')})\n\n"

        await update.message.reply_text(message, parse_mode='Markdown')
        conn.close()

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في فحص حالة السداد: {str(e)}")

async def check_customer_columns_command(update, context):
    """فحص أعمدة جدول العملاء"""
    if not update.effective_user or not update.message:
        return

    try:
        await update.message.reply_text("🔍 فحص أعمدة جدول العملاء...")

        conn = connect_to_main_db()
        if not conn:
            await update.message.reply_text("❌ خطأ في الاتصال بقاعدة البيانات")
            return

        cursor = conn.cursor()

        # فحص أعمدة جدول Acc_Customers
        cursor.execute("""
            SELECT
                COLUMN_NAME,
                DATA_TYPE,
                IS_NULLABLE,
                CHARACTER_MAXIMUM_LENGTH
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'Acc_Customers'
            ORDER BY ORDINAL_POSITION
        """)

        columns = cursor.fetchall()

        message = "📋 **أعمدة جدول Acc_Customers:**\n\n"

        phone_columns = []
        for column in columns:
            col_name, data_type, is_nullable, max_length = column
            nullable = "✅" if is_nullable == "YES" else "❌"
            length_info = f" ({max_length})" if max_length else ""

            message += f"• `{col_name}` | {data_type}{length_info} | Null: {nullable}\n"

            # البحث عن أعمدة الهاتف
            if any(phone_word in col_name.lower() for phone_word in ['phone', 'mobile', 'tel', 'هاتف']):
                phone_columns.append(col_name)

        if phone_columns:
            message += f"\n📞 **أعمدة الهاتف المكتشفة:** {', '.join(phone_columns)}\n"
        else:
            message += "\n❌ **لم يتم العثور على أعمدة هاتف واضحة**\n"

        # عينة من البيانات
        cursor.execute("""
            SELECT TOP 3 * FROM Acc_Customers
            ORDER BY CustomerId DESC
        """)

        sample_data = cursor.fetchall()
        if sample_data:
            message += "\n📊 **عينة من البيانات:**\n"
            for i, row in enumerate(sample_data, 1):
                message += f"\n**العميل {i}:**\n"
                for j, value in enumerate(row):
                    col_name = columns[j][0] if j < len(columns) else f"Col{j}"
                    message += f"  • {col_name}: {value}\n"

        await update.message.reply_text(message, parse_mode='Markdown')
        conn.close()

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في فحص أعمدة العملاء: {str(e)}")

async def check_notification_table_command(update, context):
    """فحص جدول حفظ الإشعارات"""
    if not update.effective_user or not update.message:
        return

    try:
        await update.message.reply_text("🔍 فحص جدول Customer_Last_Notification...")

        conn = connect_to_main_db()
        if not conn:
            await update.message.reply_text("❌ خطأ في الاتصال بقاعدة البيانات")
            return

        cursor = conn.cursor()

        # فحص وجود الجدول
        cursor.execute("""
            SELECT COUNT(*)
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_NAME = 'Customer_Last_Notification'
        """)

        table_exists = cursor.fetchone()[0] > 0

        message = f"📋 **حالة جدول Customer_Last_Notification:**\n\n"
        message += f"🗃️ **الجدول موجود:** {'✅ نعم' if table_exists else '❌ لا'}\n\n"

        if table_exists:
            # عرض محتويات الجدول
            cursor.execute("""
                SELECT TOP 10
                    CLN.CustomerId, CU.NameAr, CLN.LastNotificationDate,
                    CLN.LastContractUpdate, CLN.LastPaymentUpdate
                FROM Customer_Last_Notification CLN
                LEFT JOIN Acc_Customers CU ON CLN.CustomerId = CU.CustomerId
                ORDER BY CLN.LastNotificationDate DESC
            """)

            records = cursor.fetchall()

            if records:
                message += f"📊 **آخر 10 إشعارات محفوظة:**\n\n"
                for record in records:
                    customer_id, customer_name, last_notification, last_contract, last_payment = record
                    message += f"👤 **{customer_name or 'غير معروف'}** (ID: {customer_id})\n"
                    message += f"   📤 آخر إشعار: {last_notification}\n"
                    message += f"   📋 آخر عقد: {last_contract or 'لا يوجد'}\n"
                    message += f"   💳 آخر دفعة: {last_payment or 'لا يوجد'}\n\n"
            else:
                message += "📝 **الجدول فارغ** - لم يتم حفظ أي إشعارات بعد\n\n"

            # إحصائيات
            cursor.execute("SELECT COUNT(*) FROM Customer_Last_Notification")
            total_records = cursor.fetchone()[0]
            message += f"📈 **إجمالي السجلات:** {total_records}\n"

        # فحص العميل عبد الرحمن محسن 2 تحديداً
        cursor.execute("""
            SELECT
                CU.CustomerId, CU.NameAr, CU.MainPhoneNo,
                CLN.LastNotificationDate, CLN.LastContractUpdate, CLN.LastPaymentUpdate
            FROM Acc_Customers CU
            LEFT JOIN Customer_Last_Notification CLN ON CU.CustomerId = CLN.CustomerId
            WHERE CU.NameAr LIKE '%عبد الرحمن%' OR CU.NameAr LIKE '%محسن%'
            ORDER BY CU.CustomerId
        """)

        abdulrahman_records = cursor.fetchall()
        if abdulrahman_records:
            message += f"\n🔍 **بحث عن عبد الرحمن محسن:**\n\n"
            for record in abdulrahman_records:
                customer_id, name, phone, last_notif, last_contract, last_payment = record
                message += f"👤 **{name}** (ID: {customer_id})\n"
                message += f"   📞 الهاتف: {phone or 'غير محدد'}\n"
                message += f"   📤 آخر إشعار: {last_notif or 'لم يتم إرسال إشعارات'}\n"
                message += f"   📋 آخر عقد: {last_contract or 'لا يوجد'}\n"
                message += f"   💳 آخر دفعة: {last_payment or 'لا يوجد'}\n\n"

        await update.message.reply_text(message, parse_mode='Markdown')
        conn.close()

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في فحص جدول الإشعارات: {str(e)}")

async def add_chat_command(update, context):
    """إضافة المحادثة الحالية لقائمة الإشعارات"""
    if not update.effective_user or not update.message:
        return

    try:
        chat_id = update.effective_chat.id
        await update.message.reply_text(f"🔄 إضافة المحادثة {chat_id} لقائمة الإشعارات...")

        conn = connect_to_notifications_db()
        if not conn:
            await update.message.reply_text("❌ خطأ في الاتصال بقاعدة بيانات الإشعارات")
            return

        cursor = conn.cursor()

        # إضافة أو تحديث المحادثة
        cursor.execute("""
            IF EXISTS (SELECT 1 FROM ChatSettings WHERE ChatId = ?)
                UPDATE ChatSettings SET IsActive = 1, UpdatedDate = GETDATE() WHERE ChatId = ?
            ELSE
                INSERT INTO ChatSettings (ChatId, IsActive) VALUES (?, 1)
        """, (chat_id, chat_id, chat_id))

        conn.commit()
        conn.close()

        await update.message.reply_text(f"✅ تم إضافة المحادثة {chat_id} بنجاح!\n\n🔔 ستصلك الآن إشعارات تحديثات العملاء والدفعات")

    except Exception as e:
        await update.message.reply_text(f"❌ خطأ في إضافة المحادثة: {str(e)}")

def check_contracts_and_payments_updates_smart():
    """فحص ذكي لتحديثات العقود والمدفوعات مع منع التكرار"""
    try:
        conn = connect_to_main_db()
        if not conn:
            print("❌ خطأ في الاتصال بقاعدة البيانات")
            return 0

        cursor = conn.cursor()

        # إنشاء جدول لتتبع آخر إرسال (إذا لم يكن موجود)
        cursor.execute("""
            IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Customer_Last_Notification' AND xtype='U')
            CREATE TABLE Customer_Last_Notification (
                CustomerId INT PRIMARY KEY,
                LastNotificationDate DATETIME,
                LastContractUpdate DATETIME,
                LastPaymentUpdate DATETIME
            )
        """)

        # البحث عن العملاء الذين لديهم تحديثات جديدة
        cursor.execute("""
            WITH CustomerUpdates AS (
                -- آخر تحديث للعقود لكل عميل
                SELECT
                    C.CustomerId,
                    MAX(C.UpdateDate) as LastContractUpdate,
                    NULL as LastPaymentUpdate
                FROM Acc_Contracts C
                WHERE C.UpdateDate >= DATEADD(HOUR, -2, GETDATE())  -- آخر ساعتين
                GROUP BY C.CustomerId

                UNION ALL

                -- آخر تحديث للدفعات لكل عميل
                SELECT
                    C.CustomerId,
                    NULL as LastContractUpdate,
                    MAX(P.UpdateDate) as LastPaymentUpdate
                FROM Sys_Payments P
                INNER JOIN Acc_Contracts C ON P.ContractId = C.ContractId
                WHERE P.UpdateDate >= DATEADD(HOUR, -2, GETDATE())  -- آخر ساعتين
                GROUP BY C.CustomerId
            ),
            CustomerSummary AS (
                SELECT
                    CustomerId,
                    MAX(LastContractUpdate) as LastContractUpdate,
                    MAX(LastPaymentUpdate) as LastPaymentUpdate
                FROM CustomerUpdates
                GROUP BY CustomerId
            )
            SELECT
                CS.CustomerId,
                CU.NameAr,
                COALESCE(CU.MainPhoneNo, CU.SubMainPhoneNo, 'غير محدد') as Phone,
                CS.LastContractUpdate,
                CS.LastPaymentUpdate,
                CLN.LastNotificationDate,
                CLN.LastContractUpdate as PrevContractUpdate,
                CLN.LastPaymentUpdate as PrevPaymentUpdate
            FROM CustomerSummary CS
            INNER JOIN Acc_Customers CU ON CS.CustomerId = CU.CustomerId
            LEFT JOIN Customer_Last_Notification CLN ON CS.CustomerId = CLN.CustomerId
            WHERE (
                -- لم يتم إرسال إشعار من قبل
                CLN.CustomerId IS NULL
                -- أو هناك تحديث جديد في العقود
                OR (CS.LastContractUpdate IS NOT NULL AND
                    (CLN.LastContractUpdate IS NULL OR CS.LastContractUpdate > CLN.LastContractUpdate))
                -- أو هناك تحديث جديد في الدفعات
                OR (CS.LastPaymentUpdate IS NOT NULL AND
                    (CLN.LastPaymentUpdate IS NULL OR CS.LastPaymentUpdate > CLN.LastPaymentUpdate))
            )
            ORDER BY CU.NameAr
        """)

        customers_with_updates = cursor.fetchall()

        if not customers_with_updates:
            print("📝 لا توجد تحديثات جديدة للعملاء")
            conn.close()
            return 0

        sent_count = 0

        for customer in customers_with_updates:
            customer_id, name, phone, last_contract_update, last_payment_update, last_notification, prev_contract_update, prev_payment_update = customer

            print(f"🔍 فحص العميل: {name} ({customer_id})")
            print(f"   📋 آخر تحديث عقد: {last_contract_update}")
            print(f"   💳 آخر تحديث دفعة: {last_payment_update}")
            print(f"   📤 آخر إشعار: {last_notification}")

            # تحديد نوع التحديث
            update_types = []
            if last_contract_update and (not prev_contract_update or last_contract_update > prev_contract_update):
                update_types.append("تحديث عقد")
            if last_payment_update and (not prev_payment_update or last_payment_update > prev_payment_update):
                update_types.append("دفعة جديدة")

            if update_types:
                # إرسال التقرير
                success = send_customer_update_notification(customer_id, name, phone, update_types)

                if success:
                    # تحديث سجل آخر إرسال
                    cursor.execute("""
                        MERGE Customer_Last_Notification AS target
                        USING (SELECT ? as CustomerId) AS source
                        ON target.CustomerId = source.CustomerId
                        WHEN MATCHED THEN
                            UPDATE SET
                                LastNotificationDate = GETDATE(),
                                LastContractUpdate = ?,
                                LastPaymentUpdate = ?
                        WHEN NOT MATCHED THEN
                            INSERT (CustomerId, LastNotificationDate, LastContractUpdate, LastPaymentUpdate)
                            VALUES (?, GETDATE(), ?, ?);
                    """, (customer_id, last_contract_update, last_payment_update,
                          customer_id, last_contract_update, last_payment_update))

                    sent_count += 1
                    print(f"✅ تم إرسال إشعار للعميل {name} - أنواع التحديث: {', '.join(update_types)}")
                else:
                    print(f"❌ فشل إرسال إشعار للعميل {name}")

        conn.commit()
        conn.close()

        if sent_count > 0:
            print(f"📊 تم إرسال {sent_count} إشعار جديد للعملاء")
        else:
            print("📝 لا توجد تحديثات جديدة تستدعي إرسال إشعارات")

        return sent_count

    except Exception as e:
        print(f"❌ خطأ في فحص تحديثات العملاء الذكي: {str(e)}")
        import traceback
        print(f"❌ تفاصيل الخطأ: {traceback.format_exc()}")
        return 0

def send_customer_update_notification(customer_id, customer_name, phone, update_types):
    """إرسال إشعار تحديث العميل مع تفاصيل كاملة"""
    try:
        conn = connect_to_main_db()
        if not conn:
            print(f"❌ خطأ في الاتصال بقاعدة البيانات للعميل {customer_id}")
            return False

        cursor = conn.cursor()

        # الحصول على تفاصيل العميل
        cursor.execute("""
            SELECT
                CU.CustomerId, CU.NameAr, CU.NameEn,
                COALESCE(CU.MainPhoneNo, CU.SubMainPhoneNo, 'غير محدد') as Phone,
                B.NameAr as BranchName,
                COUNT(C.ContractId) as ContractsCount
            FROM Acc_Customers CU
            LEFT JOIN Sys_Branches B ON CU.BranchId = B.BranchId
            LEFT JOIN Acc_Contracts C ON CU.CustomerId = C.CustomerId
            WHERE CU.CustomerId = ?
            GROUP BY CU.CustomerId, CU.NameAr, CU.NameEn, CU.MainPhoneNo, CU.SubMainPhoneNo, B.NameAr
        """, (customer_id,))

        customer_details = cursor.fetchone()
        if not customer_details:
            print(f"❌ لم يتم العثور على تفاصيل العميل {customer_id}")
            conn.close()
            return False

        cust_id, name_ar, name_en, phone_num, branch_name, contracts_count = customer_details

        # الحصول على آخر العقود المحدثة
        if "تحديث عقد" in update_types:
            cursor.execute("""
                SELECT TOP 3
                    C.ContractId, C.ContractCode, C.TotalValue, C.DiscountValue,
                    C.UpdateDate, C.Date, C.AddDate
                FROM Acc_Contracts C
                WHERE C.CustomerId = ?
                  AND C.UpdateDate >= DATEADD(HOUR, -2, GETDATE())
                ORDER BY C.UpdateDate DESC
            """, (customer_id,))

            updated_contracts = cursor.fetchall()
            if updated_contracts:
                print(f"📋 **تحديثات العقود للعميل {name_ar} ({cust_id}):**")
                for contract in updated_contracts:
                    contract_id, contract_code, total_value, discount_value, update_date, contract_date, add_date = contract
                    total_with_discount = (total_value or 0) + (discount_value or 0)
                    print(f"   • عقد {contract_code} (ID: {contract_id})")
                    print(f"     💰 المبلغ الصافي: {total_value:,.0f} جنيه")
                    if discount_value:
                        print(f"     💰 إجمالي مع الخصم: {total_with_discount:,.0f} جنيه (خصم: {discount_value:,.0f})")
                    print(f"     📅 تاريخ العقد: {contract_date}")
                    print(f"     🕐 آخر تحديث: {update_date}")

        # الحصول على آخر الدفعات المحدثة
        if "دفعة جديدة" in update_types:
            cursor.execute("""
                SELECT TOP 3
                    P.PaymentId, P.Amount, P.PaymentDate, P.IsPaid, P.UpdateDate,
                    C.ContractCode
                FROM Sys_Payments P
                INNER JOIN Acc_Contracts C ON P.ContractId = C.ContractId
                WHERE C.CustomerId = ?
                  AND P.UpdateDate >= DATEADD(HOUR, -2, GETDATE())
                ORDER BY P.UpdateDate DESC
            """, (customer_id,))

            updated_payments = cursor.fetchall()
            if updated_payments:
                print(f"💳 **تحديثات الدفعات للعميل {name_ar} ({cust_id}):**")
                for payment in updated_payments:
                    payment_id, amount, payment_date, is_paid, update_date, contract_code = payment
                    status = "✅ مدفوع" if is_paid else "❌ غير مدفوع"
                    print(f"   • دفعة {payment_id} | عقد {contract_code}")
                    print(f"     💰 المبلغ: {amount:,.0f} جنيه | {status}")
                    print(f"     📅 استحقاق: {payment_date} | تحديث: {update_date}")

        # إنشاء رسالة التليجرام
        telegram_message = f"🔔 **تحديث عميل جديد**\n\n"
        telegram_message += f"👤 **العميل:** {name_ar}\n"
        telegram_message += f"📞 **الهاتف:** {phone_num}\n"
        telegram_message += f"🏢 **الفرع:** {branch_name or 'غير محدد'}\n"
        telegram_message += f"📋 **عدد العقود:** {contracts_count}\n"
        telegram_message += f"🔄 **نوع التحديث:** {', '.join(update_types)}\n\n"

        # إضافة تفاصيل العقود إذا كانت موجودة
        if "تحديث عقد" in update_types and updated_contracts:
            telegram_message += "📋 **تفاصيل العقود المحدثة:**\n"
            for contract in updated_contracts[:2]:  # أول عقدين فقط
                contract_id, contract_code, total_value, discount_value, update_date, contract_date, add_date = contract
                total_with_discount = (total_value or 0) + (discount_value or 0)
                telegram_message += f"• عقد {contract_code}\n"
                telegram_message += f"  💰 {total_value:,.0f} جنيه"
                if discount_value:
                    telegram_message += f" (إجمالي: {total_with_discount:,.0f})"
                telegram_message += f"\n  📅 {contract_date}\n"

        # إضافة تفاصيل الدفعات إذا كانت موجودة
        if "دفعة جديدة" in update_types and updated_payments:
            telegram_message += "\n💳 **تفاصيل الدفعات المحدثة:**\n"
            for payment in updated_payments[:2]:  # أول دفعتين فقط
                payment_id, amount, payment_date, is_paid, update_date, contract_code = payment
                status = "✅ مدفوع" if is_paid else "❌ غير مدفوع"
                telegram_message += f"• دفعة {payment_id} | عقد {contract_code}\n"
                telegram_message += f"  💰 {amount:,.0f} جنيه | {status}\n"

        # طباعة ملخص العميل في الترمنال
        print(f"👤 **ملخص العميل:**")
        print(f"   📛 الاسم: {name_ar}")
        if name_en and name_en.strip():
            print(f"   📛 الاسم بالإنجليزية: {name_en}")
        print(f"   📞 الهاتف: {phone_num}")
        print(f"   🏢 الفرع: {branch_name or 'غير محدد'}")
        print(f"   📋 عدد العقود: {contracts_count}")
        print(f"   🔄 أنواع التحديث: {', '.join(update_types)}")

        # إرسال الرسالة للتليجرام
        telegram_sent = send_telegram_notification(telegram_message)

        conn.close()

        if telegram_sent:
            print(f"📤 تم إرسال إشعار مفصل للعميل {name_ar} ({cust_id}) عبر التليجرام")
        else:
            print(f"📤 تم تسجيل إشعار للعميل {name_ar} ({cust_id}) - فشل الإرسال للتليجرام")

        return telegram_sent

    except Exception as e:
        print(f"❌ خطأ في إرسال إشعار العميل {customer_id}: {str(e)}")
        import traceback
        print(f"❌ تفاصيل الخطأ: {traceback.format_exc()}")
        return False

def send_telegram_notification(message):
    """إرسال إشعار عبر التليجرام"""
    try:
        # الحصول على معرفات المحادثات المفعلة
        conn = connect_to_notifications_db()
        if not conn:
            print("❌ خطأ في الاتصال بقاعدة بيانات الإشعارات")
            return False

        cursor = conn.cursor()

        # الحصول على المحادثات النشطة
        cursor.execute("""
            SELECT DISTINCT ChatId
            FROM ChatSettings
            WHERE IsActive = 1
        """)

        active_chats = cursor.fetchall()
        conn.close()

        if not active_chats:
            print("❌ لا توجد محادثات نشطة لإرسال الإشعارات")
            return False

        # إرسال للمحادثات النشطة
        sent_count = 0
        for chat in active_chats:
            chat_id = chat[0]
            try:
                # استخدام البوت العالمي لإرسال الرسالة
                import asyncio

                # الحصول على البوت من المتغير العالمي
                global telegram_app
                if 'telegram_app' in globals() and telegram_app:
                    # إرسال الرسالة بشكل متزامن
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(
                        telegram_app.bot.send_message(
                            chat_id=chat_id,
                            text=message,
                            parse_mode='Markdown'
                        )
                    )
                    loop.close()
                    print(f"📤 تم إرسال إشعار للمحادثة {chat_id}")
                    sent_count += 1
                else:
                    # إذا لم يكن البوت متاح، اطبع فقط
                    print(f"📤 إرسال إشعار للمحادثة {chat_id}:")
                    print(f"📝 {message}")
                    sent_count += 1

            except Exception as e:
                print(f"❌ فشل إرسال إشعار للمحادثة {chat_id}: {str(e)}")

        print(f"📊 تم إرسال الإشعار لـ {sent_count} محادثة")
        return sent_count > 0

    except Exception as e:
        print(f"❌ خطأ في إرسال إشعار التليجرام: {str(e)}")
        return False

async def execute_original_command(app, command):
    """تنفيذ الدوال الأصلية للأوامر مع التقاط النتائج"""
    try:
        # إنشاء كائن وهمي لالتقاط النتائج
        class MessageCapture:
            def __init__(self):
                self.messages = []
                self.bot = app.bot

            async def reply_text(self, text, **kwargs):
                self.messages.append(text)
                print(f"📝 تم التقاط رسالة: {text[:100]}...")

            async def send_message(self, chat_id, text, **kwargs):
                self.messages.append(text)
                print(f"📝 تم التقاط رسالة: {text[:100]}...")

        # إنشاء update وهمي مع message capture
        class FakeUpdate:
            def __init__(self):
                self.message = MessageCapture()
                self.effective_user = type('User', (), {'id': 1})()
                self.effective_chat = type('Chat', (), {'id': -1002308493862})()

        fake_update = FakeUpdate()
        fake_context = None

        # تنفيذ الدالة الأصلية
        if command == 'q':
            await q_command(fake_update, fake_context)
        elif command == 'q1':
            await q1_overdue_command(fake_update, fake_context)
        elif command == 'q2':
            await q2_today_command(fake_update, fake_context)
        elif command == 'q3':
            await q3_upcoming_command(fake_update, fake_context)
        elif command == 'q4':
            await q4_all_due_command(fake_update, fake_context)
        elif command == 'due':
            await due_payments_command(fake_update, fake_context)
        elif command == 'help':
            await help_command(fake_update, fake_context)
        elif command == 'payment_status':
            await payment_status_command(fake_update, fake_context)
        elif command == 'check_notifications':
            await check_notification_table_command(fake_update, fake_context)
        elif command == 'db_review':
            # مراجعة شاملة لقاعدة البيانات
            print("🔍 بدء مراجعة شاملة لقاعدة البيانات...")

            # 1. فحص جدول Customer_Last_Notification
            print("\n📋 فحص جدول Customer_Last_Notification:")
            conn = connect_to_main_db()
            if conn:
                cursor = conn.cursor()
                try:
                    cursor.execute("SELECT COUNT(*) FROM Customer_Last_Notification")
                    count = cursor.fetchone()[0]
                    print(f"   📊 عدد السجلات: {count}")

                    if count > 0:
                        cursor.execute("""
                            SELECT TOP 5
                                CLN.CustomerId, CU.NameAr, CLN.LastNotificationDate,
                                CLN.LastContractUpdate, CLN.LastPaymentUpdate
                            FROM Customer_Last_Notification CLN
                            LEFT JOIN Acc_Customers CU ON CLN.CustomerId = CU.CustomerId
                            ORDER BY CLN.LastNotificationDate DESC
                        """)
                        records = cursor.fetchall()
                        for record in records:
                            customer_id, name, last_notif, last_contract, last_payment = record
                            print(f"   👤 {name} ({customer_id}): آخر إشعار {last_notif}")
                except Exception as e:
                    print(f"   ❌ الجدول غير موجود أو خطأ: {str(e)}")
                conn.close()

            # 2. فحص آخر التحديثات في العقود
            print("\n📋 فحص آخر تحديثات العقود:")
            conn = connect_to_main_db()
            if conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT TOP 5
                        C.ContractId, C.ContractCode, CU.NameAr, C.AddDate, C.UpdateDate
                    FROM Acc_Contracts C
                    INNER JOIN Acc_Customers CU ON C.CustomerId = CU.CustomerId
                    WHERE C.AddDate >= DATEADD(HOUR, -24, GETDATE())
                       OR C.UpdateDate >= DATEADD(HOUR, -24, GETDATE())
                    ORDER BY COALESCE(C.UpdateDate, C.AddDate) DESC
                """)
                contracts = cursor.fetchall()
                if contracts:
                    for contract in contracts:
                        contract_id, code, customer, add_date, update_date = contract
                        print(f"   📋 عقد {code} | {customer} | إضافة: {add_date} | تحديث: {update_date}")
                else:
                    print("   📝 لا توجد تحديثات في آخر 24 ساعة")
                conn.close()

            # 3. فحص آخر تحديثات الدفعات
            print("\n💳 فحص آخر تحديثات الدفعات:")
            conn = connect_to_main_db()
            if conn:
                cursor = conn.cursor()
                cursor.execute("""
                    SELECT TOP 5
                        P.PaymentId, P.Amount, P.IsPaid, CU.NameAr, P.AddDate, P.UpdateDate
                    FROM Sys_Payments P
                    INNER JOIN Acc_Contracts C ON P.ContractId = C.ContractId
                    INNER JOIN Acc_Customers CU ON C.CustomerId = CU.CustomerId
                    WHERE P.AddDate >= DATEADD(HOUR, -24, GETDATE())
                       OR P.UpdateDate >= DATEADD(HOUR, -24, GETDATE())
                    ORDER BY COALESCE(P.UpdateDate, P.AddDate) DESC
                """)
                payments = cursor.fetchall()
                if payments:
                    for payment in payments:
                        payment_id, amount, is_paid, customer, add_date, update_date = payment
                        status = "✅ مدفوع" if is_paid else "❌ غير مدفوع"
                        print(f"   💳 دفعة {payment_id} | {customer} | {amount:,.0f} | {status} | إضافة: {add_date} | تحديث: {update_date}")
                else:
                    print("   📝 لا توجد تحديثات في آخر 24 ساعة")
                conn.close()

            # 4. فحص المحادثات النشطة
            print("\n📱 فحص المحادثات النشطة:")
            conn = connect_to_notifications_db()
            if conn:
                cursor = conn.cursor()
                try:
                    cursor.execute("SELECT ChatId, IsActive FROM ChatSettings ORDER BY ChatId")
                    chats = cursor.fetchall()
                    active_count = 0
                    for chat in chats:
                        chat_id, is_active = chat
                        status = "✅ نشط" if is_active else "❌ غير نشط"
                        print(f"   📱 محادثة {chat_id}: {status}")
                        if is_active:
                            active_count += 1
                    print(f"   📊 إجمالي المحادثات النشطة: {active_count}")
                except Exception as e:
                    print(f"   ❌ خطأ في فحص المحادثات: {str(e)}")
                conn.close()

            # 5. اختبار النظام الذكي
            print("\n🧪 اختبار النظام الذكي:")
            updates_count = check_contracts_and_payments_updates_smart()
            print(f"   📊 نتيجة الاختبار: {updates_count} تحديث")

            print("\n✅ انتهت المراجعة الشاملة")
        elif command == 'test_updates':
            # اختبار النظام الجديد
            print("🧪 اختبار نظام مراقبة التحديثات...")
            updates_count = check_contracts_and_payments_updates_smart()
            print(f"📊 نتيجة الاختبار: {updates_count} تحديث تم إرساله")


        else:
            return False

        # إرسال النتائج المُلتقطة للمستقبلين
        if fake_update.message.messages:
            success_count = 0
            for message in fake_update.message.messages:
                if await send_to_notification_recipients(app, message):
                    success_count += 1

            print(f"✅ تم إرسال {success_count} رسالة من أصل {len(fake_update.message.messages)}")
            return success_count > 0
        else:
            print("⚠️ لم يتم التقاط أي رسائل")
            return False

    except Exception as e:
        print(f"❌ خطأ في تنفيذ الدالة الأصلية {command}: {str(e)}")
        import traceback
        print(f"❌ تفاصيل الخطأ: {traceback.format_exc()}")
        return False

async def execute_bot_command(app, command):
    """تنفيذ الأمر مباشرة وإرسال النتيجة"""
    try:
        print(f"🤖 تنفيذ أمر: /{command}")

        # تنفيذ الأمر حسب نوعه وإرسال النتيجة مباشرة
        if command == 'q':
            # التقرير الشامل - استخدام الدالة الأصلية
            print(f"📊 تنفيذ التقرير الشامل...")
            return await execute_original_command(app, 'q')

        elif command == 'q1':
            # الأقساط المتأخرة - استخدام الدالة الأصلية
            print(f"📊 تنفيذ تقرير الأقساط المتأخرة...")
            return await execute_original_command(app, 'q1')

        elif command == 'q2':
            # المستحقة اليوم - استخدام الدالة الأصلية
            print(f"📊 تنفيذ تقرير الأقساط المستحقة اليوم...")
            return await execute_original_command(app, 'q2')

        elif command == 'q3':
            # المستحقة خلال 3 أيام - استخدام الدالة الأصلية
            print(f"📊 تنفيذ تقرير الأقساط المستحقة خلال 3 أيام...")
            return await execute_original_command(app, 'q3')
        
        elif command == 'q4':
            # جميع الأقساط المستحقة - استخدام الدالة الأصلية
            print(f"📊 تنفيذ تقرير جميع الأقساط المستحقة...")
            return await execute_original_command(app, 'q4')

        elif command == 'due':
            # أمر مواعيد الاستحقاق - استخدام الدالة الأصلية
            print(f"📊 تنفيذ أمر مواعيد الاستحقاق...")
            return await execute_original_command(app, 'due')
        else:
            print(f"❌ أمر غير معروف: {command}")
            return False
        


        print(f"✅ تم تنفيذ أمر /{command} بنجاح")
        return True

    except Exception as e:
        print(f"❌ خطأ في تنفيذ أمر /{command}: {str(e)}")
        import traceback
        print(f"❌ تفاصيل الخطأ: {traceback.format_exc()}")
        return False

async def send_to_notification_recipients(app, message, branch_id=None):
    """إرسال رسالة للمستقبلين من جدول Notification_Recipients"""
    print(f"📤 بدء إرسال رسالة: {message[:50]}...")
    print(f"📤 فرع محدد: {branch_id if branch_id else 'جميع الفروع'}")

    try:
        conn = connect_to_notifications_db()
        if not conn:
            print("❌ خطأ في الاتصال بقاعدة بيانات الإشعارات")
            return False

        cursor = conn.cursor()

        # جلب المستقبلين النشطين
        if branch_id:
            # إرسال لفرع معين
            query = """
                SELECT ChatId, RecipientName, RecipientType
                FROM Notification_Recipients
                WHERE IsActive = 1 AND (BranchId = ? OR BranchId IS NULL)
                ORDER BY RecipientType, RecipientName
            """
            cursor.execute(query, (branch_id,))
        else:
            # إرسال عام لجميع المستقبلين
            query = """
                SELECT ChatId, RecipientName, RecipientType
                FROM Notification_Recipients
                WHERE IsActive = 1
                ORDER BY RecipientType, RecipientName
            """
            cursor.execute(query)

        recipients = cursor.fetchall()
        conn.close()

        if not recipients:
            print("⚠️ لا توجد مستقبلين نشطين")
            print("⚠️ تحقق من جدول Notification_Recipients")
            return False

        print(f"📋 تم العثور على {len(recipients)} مستقبل نشط")

        success_count = 0
        total_count = len(recipients)

        print(f"📤 إرسال الرسالة إلى {total_count} مستقبل...")

        for recipient in recipients:
            chat_id, name, recipient_type = recipient

            try:
                # محاولة الإرسال
                result = await app.bot.send_message(
                    chat_id=chat_id,
                    text=message,
                    parse_mode='Markdown'
                )

                # التحقق من نجاح الإرسال
                if result and result.message_id:
                    success_count += 1
                    icon = "👤" if recipient_type == 'User' else "👥"
                    print(f"✅ تم الإرسال إلى {icon} {name} ({chat_id}) - Message ID: {result.message_id}")
                else:
                    icon = "👤" if recipient_type == 'User' else "👥"
                    print(f"⚠️ إرسال مشكوك فيه إلى {icon} {name} ({chat_id}) - لا يوجد Message ID")

            except Exception as e:
                icon = "👤" if recipient_type == 'User' else "👥"
                error_type = type(e).__name__
                print(f"❌ فشل الإرسال إلى {icon} {name} ({chat_id}): {error_type} - {str(e)}")

                # تسجيل أخطاء شائعة
                if "Forbidden" in str(e):
                    print(f"   🚫 البوت محظور من {name}")
                elif "Chat not found" in str(e):
                    print(f"   🔍 المحادثة غير موجودة: {name}")
                elif "Bad Request" in str(e):
                    print(f"   📝 خطأ في الرسالة أو المعرف: {name}")

        print(f"📊 نتيجة الإرسال: {success_count}/{total_count} نجح")

        # تحديد نجاح العملية بناءً على النتائج
        if success_count == 0:
            print("❌ فشل كامل: لم يتم الإرسال لأي مستقبل")
            return False
        elif success_count == total_count:
            print("✅ نجاح كامل: تم الإرسال لجميع المستقبلين")
            return True
        elif success_count >= (total_count / 2):
            print(f"⚠️ نجاح جزئي: تم الإرسال لـ {success_count} من {total_count} مستقبل")
            return True
        else:
            print(f"❌ فشل أغلبية: تم الإرسال لـ {success_count} فقط من {total_count} مستقبل")
            return False

    except Exception as e:
        print(f"❌ خطأ في إرسال الإشعارات: {str(e)}")
        return False

async def new_schedule_system(app):
    """النظام الجديد للجدولة المحسن"""
    print("🚀 بدء النظام الجديد للجدولة...")

    while True:
        try:
            # عرض الوقت الحالي
            current_time = datetime.now()
            print(f"🕐 الوقت الحالي: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")

            # جلب الأوامر المستحقة للتنفيذ
            scheduled_commands = get_scheduled_commands()

            if scheduled_commands:
                print(f"📋 تم العثور على {len(scheduled_commands)} أمر مستحق للتنفيذ")

                for command_data in scheduled_commands:
                    command_id, command, type_name, next_send_time, interval_minutes, custom_message, last_sent_time = command_data

                    # عرض تفاصيل الأمر مع الأوقات
                    print(f"⏰ أمر مجدول: {command} ({type_name}) - ID: {command_id}")
                    print(f"📅 موعد الإرسال المحدد: {next_send_time}")
                    print(f"🕐 الوقت الحالي: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
                    print(f"📤 آخر إرسال: {last_sent_time if last_sent_time else 'لم يتم الإرسال من قبل'}")

                    # التحقق من أن الوقت حان فعلاً
                    if next_send_time <= current_time:
                        print(f"✅ الوقت حان للتنفيذ!")

                        # تحقق إضافي من آخر إرسال
                        if last_sent_time:
                            time_since_last = (current_time - last_sent_time).total_seconds() / 60
                            print(f"⏱️ مر {time_since_last:.1f} دقيقة منذ آخر إرسال (المطلوب: {interval_minutes} دقيقة)")
                    else:
                        print(f"⏰ لم يحن الوقت بعد - متبقي: {next_send_time - current_time}")
                        continue

                    # تحديث الحالة إلى "قيد التنفيذ"
                    update_command_status(command_id, 'قيد التنفيذ', f'بدء تنفيذ الأمر في {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')

                try:
                    # تنفيذ الأمر حسب نوعه
                    success = False

                    # إذا كان هناك رسالة مخصصة في CustomMessage، استخدمها
                    if custom_message and custom_message.strip():
                        print(f"📝 إرسال رسالة مخصصة من CustomMessage: {custom_message[:100]}...")
                        success = await send_to_notification_recipients(app, custom_message.strip())
                    else:
                        # تنفيذ الأمر الأصلي (بدون رسالة مخصصة)
                        print(f"🤖 تنفيذ الأمر الأصلي: /{command}")
                        success = await execute_bot_command(app, command)

                    # تحديث الحالة حسب النتيجة
                    if success:
                        # تحديث موعد الإرسال التالي
                        update_next_send_time(command_id, interval_minutes)
                        update_command_status(command_id, 'تم الإرسال', f'تم الإرسال بنجاح في {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
                        print(f"✅ تم تنفيذ أمر {command} وتحديث الموعد التالي")
                    else:
                        update_command_status(command_id, 'فشل الإرسال', f'فشل في تنفيذ الأمر في {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
                        print(f"❌ فشل في تنفيذ أمر {command}")
                        print(f"❌ السبب: success = False")

                except Exception as e:
                    error_msg = f'خطأ في التنفيذ: {str(e)} في {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}'
                    update_command_status(command_id, 'فشل الإرسال', error_msg)
                    print(f"❌ خطأ في تنفيذ أمر {command}: {str(e)}")
                    print(f"❌ نوع الخطأ: {type(e).__name__}")
                    import traceback
                    print(f"❌ تفاصيل الخطأ: {traceback.format_exc()}")
            else:
                print("💤 لا توجد أوامر مستحقة للتنفيذ في الوقت الحالي")

            # انتظار دقيقة واحدة قبل الفحص التالي
            await asyncio.sleep(60)

        except Exception as e:
            print(f"❌ خطأ في النظام الجديد للجدولة: {str(e)}")
            await asyncio.sleep(60)



async def branches_reports_command(update, context):
    """أمر تقارير الفروع"""
    if not update.effective_user or not update.message:
        return

    try:
        from telegram import InlineKeyboardButton, InlineKeyboardMarkup

        print("📋 تنفيذ أمر تقارير الفروع")

        # إنشاء أزرار تقارير الفروع
        keyboard = [
            [InlineKeyboardButton("📊 تقرير عميل معين", callback_data="branch_customer_report")],
            [InlineKeyboardButton("👥 تقارير شاملة للفروع", callback_data="branch_full_reports")],
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)

        await update.message.reply_text(
            "🏢 **تقارير الفروع**\n\n"
            "📋 اختر نوع التقرير المطلوب:",
            reply_markup=reply_markup,
            parse_mode='Markdown'
        )

    except Exception as e:
        error_msg = f"❌ خطأ في أمر تقارير الفروع: {str(e)}"
        print(error_msg)
        await update.message.reply_text("❌ حدث خطأ في عرض تقارير الفروع")

async def main():
    """الدالة الرئيسية للبوت"""
    print("🚀 بدء تشغيل Terra Bot المحسن مع الإشعارات")
    print("=" * 50)

    try:
        from telegram.ext import Application, CommandHandler

        # إنشاء التطبيق
        app = Application.builder().token(BOT_TOKEN).build()

        # تعيين البوت العالمي
        global telegram_app
        telegram_app = app

        # تهيئة البوت
        await app.initialize()

        # إضافة المعالجات
        app.add_handler(CommandHandler("start", start))
        app.add_handler(CommandHandler("test", test_db))
        app.add_handler(CommandHandler("testphones", test_phones))
        app.add_handler(CommandHandler("id", get_id))
        app.add_handler(CommandHandler("recipients", manage_recipients))
        app.add_handler(CommandHandler("reports", show_scheduled_reports))
        app.add_handler(CommandHandler("testreport", test_report))
        app.add_handler(CommandHandler("e", statistics_report))
        app.add_handler(CommandHandler("manage_reports", manage_reports))
        app.add_handler(CommandHandler("d", designs_without_files_command))
        app.add_handler(CommandHandler("de", designs_without_files_detailed_command))
        app.add_handler(CommandHandler("customer", customer_report_command))
        app.add_handler(CommandHandler("due", due_payments_command))
        app.add_handler(CommandHandler("q", q_command))
        app.add_handler(CommandHandler("q1", q1_overdue_command))
        app.add_handler(CommandHandler("q2", q2_today_command))
        app.add_handler(CommandHandler("q3", q3_upcoming_command))
        app.add_handler(CommandHandler("q4", q4_all_due_command))
        app.add_handler(CommandHandler("branches", branches_reports_command))
        app.add_handler(CommandHandler("monitor_pay", start_payment_monitoring))
        app.add_handler(CommandHandler("check_pay", check_payment_changes))
        app.add_handler(CommandHandler("payment_status", payment_status_command))
        app.add_handler(CommandHandler("check_columns", check_customer_columns_command))
        app.add_handler(CommandHandler("check_notifications", check_notification_table_command))
        app.add_handler(CommandHandler("add_chat", add_chat_command))
        app.add_handler(CommandHandler("help", help_command))
        app.add_handler(CommandHandler("cleanup", cleanup_duplicate_commands))

        # إضافة معالج الأزرار التفاعلية
        from telegram.ext import CallbackQueryHandler, MessageHandler, filters
        app.add_handler(CallbackQueryHandler(handle_all_callbacks))

        # إضافة معالج الرسائل النصية (الأزرار والبحث الذكي)
        app.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, handle_keyboard_buttons))

        setup_msg = "✅ البوت جاهز!"
        print(setup_msg)
        print("📱 أرسل /start في التليجرام")
        print("🆔 أرسل /id لعرض المعرف")
        print("🧪 أرسل /test لاختبار قاعدة البيانات")
        print("📊 أرسل /reports لعرض التقارير المجدولة")
        print("📈 أرسل /testreport لتقرير تجريبي")
        print("📊 أرسل /e لتقرير إحصائي (مثال: /e 3)")
        print("👤 أرسل /customer [كود] لتقرير عميل (مثال: /customer 1213)")
        print("📅 أرسل /due لمواعيد الاستحقاق والأقساط المتأخرة")
        print("📄 ملف Log: terra_bot.log")
        print("=" * 50)

        if logger:
            logger.info(setup_msg)
            logger.info("🔔 نظام الإشعارات نشط")
            logger.info("📊 مراقبة قاعدة البيانات مستمرة")
            logger.info("📈 نظام التقارير المجدولة نشط")

        # إنشاء الجداول الجديدة للنظام المحسن
        print("🔧 إنشاء الجداول الجديدة...")
        create_new_schedule_tables()

        # تشغيل النظام الجديد للجدولة
        asyncio.create_task(new_schedule_system(app))
        print("📅 تم بدء النظام الجديد للجدولة")

        # تشغيل مراقب قاعدة البيانات في مهمة منفصلة
        asyncio.create_task(monitor_all_data())
        print("🔍 تم بدء مراقب قاعدة البيانات")

        # تشغيل البوت
        await app.start()
        await app.updater.start_polling(drop_pending_updates=True)

        # انتظار إيقاف البوت
        await asyncio.Event().wait()

    except ImportError:
        print("❌ خطأ: مكتبات التليجرام غير مثبتة")
        print("💡 شغل: pip install python-telegram-bot")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
        if logger:
            logger.error(f"خطأ في تشغيل البوت: {e}")


        # تشغيل مراقب قاعدة البيانات في مهمة منفصلة
        monitor_task = asyncio.create_task(monitor_all_data())
        print("🔍 تم بدء مراقب قاعدة البيانات")

        # تشغيل البوت
        await app.start()
        await app.updater.start_polling(drop_pending_updates=True)

        # انتظار إيقاف البوت
        await asyncio.Event().wait()

    except ImportError:
        print("❌ خطأ: مكتبات التليجرام غير مثبتة")
        print("💡 شغل: pip install python-telegram-bot")
    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
        if logger:
            logger.error(f"خطأ في تشغيل البوت: {e}")

if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"❌ خطأ عام: {e}")
