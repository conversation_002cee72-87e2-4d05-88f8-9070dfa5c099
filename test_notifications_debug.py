#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار وتشخيص نظام الإشعارات التلقائية
"""

import asyncio
import pyodbc
from datetime import datetime
from telegram import Bot

# إعدادات
BOT_TOKEN = "**********************************************"
NOTIFICATION_CHAT_IDS = [
    "1107000748",
    "1206533289", 
    "-1002255521584"
]

DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

def connect_to_db():
    """الاتصال بقاعدة البيانات"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['user']};PWD={DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def get_latest_customer():
    """الحصول على آخر عميل"""
    try:
        conn = connect_to_db()
        if not conn:
            return None
        
        cursor = conn.cursor()
        
        cursor.execute("SELECT TOP 1 CustomerCode, NameAr, MainPhoneNo, BranchId, AddDate FROM Acc_Customers WHERE IsDeleted = 0 ORDER BY AddDate DESC")
        result = cursor.fetchone()
        conn.close()
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في الحصول على العميل: {str(e)}")
        return None

async def test_notification_system():
    """اختبار نظام الإشعارات"""
    print("🧪 اختبار نظام الإشعارات التلقائية")
    print("=" * 50)
    
    # 1. اختبار الاتصال بقاعدة البيانات
    print("1️⃣ اختبار الاتصال بقاعدة البيانات...")
    customer = get_latest_customer()
    if customer:
        print(f"✅ تم الاتصال بنجاح - آخر عميل: {customer[0]} - {customer[1] or 'بدون اسم'}")
    else:
        print("❌ فشل الاتصال بقاعدة البيانات")
        return
    
    # 2. اختبار البوت
    print("\n2️⃣ اختبار البوت...")
    try:
        bot = Bot(token=BOT_TOKEN)
        bot_info = await bot.get_me()
        print(f"✅ البوت يعمل: {bot_info.first_name} (@{bot_info.username})")
    except Exception as e:
        print(f"❌ خطأ في البوت: {str(e)}")
        return
    
    # 3. اختبار إرسال الإشعارات
    print("\n3️⃣ اختبار إرسال الإشعارات...")
    
    branch_name = "فرع التجمع" if customer[3] == 3 else "فرع مدينة نصر" if customer[3] == 2 else "غير محدد"
    
    test_message = f"""🧪 **اختبار نظام الإشعارات**

👤 **آخر عميل في قاعدة البيانات:**

🔢 **الكود:** {customer[0]}
👤 **الاسم:** {customer[1] or 'غير محدد'}
📱 **الهاتف:** {customer[2] or 'غير محدد'}
🏢 **الفرع:** {branch_name}
📅 **تاريخ الإضافة:** {customer[4].strftime('%Y-%m-%d %H:%M') if customer[4] else 'غير محدد'}

⏰ **وقت الاختبار:** {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
    
    notification_text = f"🔔 **اختبار الإشعارات - Terra Bot**\n\n{test_message}"
    
    success_count = 0
    for chat_id in NOTIFICATION_CHAT_IDS:
        try:
            await bot.send_message(
                chat_id=chat_id,
                text=notification_text,
                parse_mode='Markdown'
            )
            print(f"✅ تم إرسال اختبار إلى {chat_id}")
            success_count += 1
            
        except Exception as e:
            print(f"❌ فشل إرسال إلى {chat_id}: {str(e)}")
    
    print(f"\n📊 النتيجة: تم إرسال {success_count}/{len(NOTIFICATION_CHAT_IDS)} إشعارات بنجاح")
    
    # 4. محاكاة نظام المراقبة
    print("\n4️⃣ محاكاة نظام المراقبة...")
    
    last_customer_id = customer[0]
    print(f"📊 آخر عميل محفوظ: {last_customer_id}")
    
    for cycle in range(3):
        print(f"\n🔄 دورة مراقبة #{cycle + 1}")
        
        # فحص آخر عميل
        current_customer = get_latest_customer()
        if current_customer:
            current_id = current_customer[0]
            print(f"🔍 آخر عميل حالياً: {current_id}")
            
            if current_id != last_customer_id:
                print(f"🆕 عميل جديد اكتُشف! الكود: {current_id}")
                
                # إرسال إشعار
                branch_name = "فرع التجمع" if current_customer[3] == 3 else "فرع مدينة نصر" if current_customer[3] == 2 else "غير محدد"
                
                new_customer_message = f"""👤 **عميل جديد تم إضافته**

🔢 **الكود:** {current_customer[0]}
👤 **الاسم:** {current_customer[1] or 'غير محدد'}
📱 **الهاتف:** {current_customer[2] or 'غير محدد'}
🏢 **الفرع:** {branch_name}
📅 **تاريخ الإضافة:** {current_customer[4].strftime('%Y-%m-%d %H:%M') if current_customer[4] else 'غير محدد'}"""
                
                notification_text = f"🔔 **إشعار تلقائي من Terra Bot**\n\n{new_customer_message}\n\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                
                for chat_id in NOTIFICATION_CHAT_IDS:
                    try:
                        await bot.send_message(
                            chat_id=chat_id,
                            text=notification_text,
                            parse_mode='Markdown'
                        )
                        print(f"✅ تم إرسال إشعار العميل الجديد إلى {chat_id}")
                        
                    except Exception as e:
                        print(f"❌ فشل إرسال إشعار إلى {chat_id}: {str(e)}")
                
                last_customer_id = current_id
            else:
                print("📊 لا توجد عملاء جدد")
        
        # انتظار 5 ثوان
        await asyncio.sleep(5)
    
    print("\n" + "=" * 50)
    print("✅ انتهى اختبار نظام الإشعارات")

async def start_simple_monitor():
    """بدء مراقب بسيط للاختبار"""
    print("🔍 بدء مراقب بسيط للعملاء الجدد...")
    
    bot = Bot(token=BOT_TOKEN)
    
    # الحصول على آخر عميل حالياً
    last_customer = get_latest_customer()
    last_customer_id = last_customer[0] if last_customer else None
    
    print(f"📊 آخر عميل حالياً: {last_customer_id}")
    
    cycle = 0
    while True:
        try:
            cycle += 1
            current_time = datetime.now().strftime('%H:%M:%S')
            print(f"\n🔄 دورة #{cycle} - {current_time}")
            
            # فحص آخر عميل
            current_customer = get_latest_customer()
            
            if current_customer:
                current_id = current_customer[0]
                
                if current_id != last_customer_id:
                    print(f"🆕 عميل جديد اكتُشف! الكود: {current_id}")
                    
                    # إرسال إشعار
                    branch_name = "فرع التجمع" if current_customer[3] == 3 else "فرع مدينة نصر" if current_customer[3] == 2 else "غير محدد"
                    
                    message = f"""👤 **عميل جديد تم إضافته**

🔢 **الكود:** {current_customer[0]}
👤 **الاسم:** {current_customer[1] or 'غير محدد'}
📱 **الهاتف:** {current_customer[2] or 'غير محدد'}
🏢 **الفرع:** {branch_name}
📅 **تاريخ الإضافة:** {current_customer[4].strftime('%Y-%m-%d %H:%M') if current_customer[4] else 'غير محدد'}"""
                    
                    notification_text = f"🔔 **إشعار تلقائي من Terra Bot**\n\n{message}\n\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                    
                    for chat_id in NOTIFICATION_CHAT_IDS:
                        try:
                            await bot.send_message(
                                chat_id=chat_id,
                                text=notification_text,
                                parse_mode='Markdown'
                            )
                            print(f"✅ تم إرسال إشعار إلى {chat_id}")
                            
                        except Exception as e:
                            print(f"❌ فشل إرسال إلى {chat_id}: {str(e)}")
                    
                    last_customer_id = current_id
                else:
                    print(f"📊 لا توجد عملاء جدد (آخر عميل: {current_id})")
            
            # انتظار 15 ثانية
            await asyncio.sleep(15)
            
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف المراقب")
            break
        except Exception as e:
            print(f"❌ خطأ في المراقب: {str(e)}")
            await asyncio.sleep(30)

async def main():
    """الدالة الرئيسية"""
    print("اختر نوع الاختبار:")
    print("1 - اختبار شامل للنظام")
    print("2 - بدء مراقب بسيط")
    
    choice = input("اختيارك (1 أو 2): ").strip()
    
    if choice == "1":
        await test_notification_system()
    elif choice == "2":
        await start_simple_monitor()
    else:
        print("❌ اختيار غير صحيح")

if __name__ == "__main__":
    asyncio.run(main())
