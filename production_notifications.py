#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نظام إشعارات الإنتاج - للبيانات الحقيقية فقط
لا يتعامل مع أي بيانات تجريبية
"""

import asyncio
import pyodbc
from datetime import datetime
from telegram import Bot

# إعدادات الإنتاج
BOT_TOKEN = "7556046481:AAEMttgLYzrMu1q2ogRlgsV8mgscqZKbbcY"
NOTIFICATION_CHAT_IDS = [
    "1107000748",      # مستخدم 1
    "1206533289"       # مستخدم 2
]

DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

# متغيرات لتتبع آخر البيانات
last_customer_id = None
last_preview_id = None
last_meeting_id = None
last_design_id = None
last_contract_id = None

def connect_to_db():
    """الاتصال بقاعدة البيانات"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['user']};PWD={DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def is_real_customer(customer_name, phone):
    """التحقق من أن العميل حقيقي وليس تجريبي"""
    if not customer_name:
        return True  # قبول العملاء بدون اسم
    
    # استبعاد البيانات التجريبية
    test_keywords = ['test', 'تجريبي', 'اختبار', 'تجربة']
    name_lower = customer_name.lower()
    
    for keyword in test_keywords:
        if keyword in name_lower:
            return False
    
    # استبعاد أرقام الهواتف التجريبية
    if phone and phone.startswith('0101234'):
        return False
    
    return True

def get_latest_real_customer():
    """الحصول على آخر عميل حقيقي"""
    try:
        conn = connect_to_db()
        if not conn:
            return None
        
        cursor = conn.cursor()
        
        # البحث عن آخر عميل حقيقي (استبعاد التجريبيين)
        cursor.execute("""
            SELECT TOP 1 CustomerCode, NameAr, MainPhoneNo, BranchId, AddDate 
            FROM Acc_Customers 
            WHERE IsDeleted = 0 
              AND (NameAr NOT LIKE '%test%' OR NameAr IS NULL)
              AND (NameAr NOT LIKE '%تجريبي%' OR NameAr IS NULL)
              AND (NameAr NOT LIKE '%اختبار%' OR NameAr IS NULL)
              AND (MainPhoneNo NOT LIKE '0101234%' OR MainPhoneNo IS NULL)
            ORDER BY AddDate DESC
        """)
        
        result = cursor.fetchone()
        conn.close()
        
        return result
        
    except Exception as e:
        print(f"❌ خطأ في الحصول على العميل: {str(e)}")
        return None

async def send_notification(message):
    """إرسال إشعار للمستخدمين المخولين"""
    try:
        bot = Bot(token=BOT_TOKEN)
        
        notification_text = f"🔔 **إشعار من Terra Bot**\n\n{message}\n\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        for chat_id in NOTIFICATION_CHAT_IDS:
            try:
                await bot.send_message(
                    chat_id=chat_id,
                    text=notification_text,
                    parse_mode='Markdown'
                )
                print(f"✅ تم إرسال إشعار إلى {chat_id}")
                
            except Exception as e:
                print(f"❌ فشل إرسال إشعار إلى {chat_id}: {str(e)}")
        
    except Exception as e:
        print(f"❌ خطأ في إرسال الإشعارات: {str(e)}")

def check_new_real_customers():
    """فحص العملاء الحقيقيين الجدد فقط"""
    global last_customer_id
    
    try:
        customer = get_latest_real_customer()
        
        if customer:
            current_id = customer[0]
            
            # التحقق المزدوج من أن العميل حقيقي
            if not is_real_customer(customer[1], customer[2]):
                return None
            
            if current_id != last_customer_id:
                last_customer_id = current_id
                
                branch_name = "فرع التجمع" if customer[3] == 3 else "فرع مدينة نصر" if customer[3] == 2 else "غير محدد"
                
                message = f"""👤 **عميل جديد تم إضافته**

🔢 **الكود:** {customer[0]}
👤 **الاسم:** {customer[1] or 'غير محدد'}
📱 **الهاتف:** {customer[2] or 'غير محدد'}
🏢 **الفرع:** {branch_name}
📅 **تاريخ الإضافة:** {customer[4].strftime('%Y-%m-%d %H:%M') if customer[4] else 'غير محدد'}"""
                
                return message
        
        return None
        
    except Exception as e:
        print(f"❌ خطأ في فحص العملاء: {str(e)}")
        return None

def initialize_last_ids():
    """تهيئة آخر المعرفات من البيانات الحقيقية"""
    global last_customer_id
    
    try:
        customer = get_latest_real_customer()
        if customer:
            last_customer_id = customer[0]
            print(f"📊 تم تهيئة آخر عميل حقيقي: {last_customer_id}")
        
    except Exception as e:
        print(f"❌ خطأ في التهيئة: {str(e)}")

async def monitor_production_data():
    """مراقبة البيانات الحقيقية فقط"""
    print("🔍 بدء مراقبة البيانات الحقيقية...")
    print("⚠️ سيتم تجاهل جميع البيانات التجريبية")
    
    # تهيئة المعرفات
    initialize_last_ids()
    
    cycle = 0
    
    while True:
        try:
            cycle += 1
            current_time = datetime.now().strftime('%H:%M:%S')
            print(f"\n🔄 فحص إنتاج #{cycle} - {current_time}")
            
            # فحص العملاء الحقيقيين الجدد
            new_customer = check_new_real_customers()
            if new_customer:
                print("🔔 تم اكتشاف عميل حقيقي جديد!")
                await send_notification(new_customer)
            else:
                print("📊 لا توجد عملاء حقيقيين جدد")
            
            # انتظار 30 ثانية
            await asyncio.sleep(30)
            
        except KeyboardInterrupt:
            print("\n⏹️ تم إيقاف المراقبة")
            break
        except Exception as e:
            print(f"❌ خطأ في المراقبة: {str(e)}")
            await asyncio.sleep(60)

if __name__ == "__main__":
    print("🏢 نظام إشعارات الإنتاج - Terra Company")
    print("=" * 60)
    print("✅ يراقب البيانات الحقيقية فقط")
    print("❌ يتجاهل جميع البيانات التجريبية")
    print("⏹️ اضغط Ctrl+C لإيقاف المراقبة")
    print("=" * 60)
    
    try:
        asyncio.run(monitor_production_data())
    except KeyboardInterrupt:
        print("\n👋 تم إيقاف نظام المراقبة")
