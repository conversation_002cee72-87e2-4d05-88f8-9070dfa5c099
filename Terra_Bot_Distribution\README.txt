========================================
    Terra Bot Enhanced v2.0 - تعليمات الاستخدام
========================================

🤖 **Terra Bot Enhanced**
🏢 **الشركة:** Terra Company
📅 **تاريخ البناء:** 26/06/2025
📊 **حجم الملف:** 12.3 MB

========================================
           🚀 طريقة التشغيل
========================================

1. شغل Terra_Bot_Enhanced.exe
2. البوت سيبدأ العمل تلقائياً في الخلفية
3. سيراقب قاعدة البيانات كل 30 ثانية
4. سيرسل إشعارات تلقائية عند اكتشاف بيانات جديدة

========================================
         📱 الأوامر المتاحة في التليجرام
========================================

/start - بدء البوت وعرض القائمة الرئيسية
/test  - اختبار الاتصال بقاعدة البيانات  
/id    - عرض معرف المستخدم/الجروب للإشعارات

========================================
           🔔 نظام الإشعارات
========================================

يرسل إشعارات تلقائية عند إضافة:
✅ عملاء جدد
✅ معاينات جديدة  
✅ اجتماعات جديدة
✅ تصميمات جديدة
✅ عقود جديدة

يعرض تفاصيل شاملة:
- الفرع (مدينة نصر / التجمع)
- المنطقة/المدينة
- طريقة الدفع
- وسيلة التواصل
- من أنشأ البيانات
- تاريخ الإضافة

========================================
         🔧 إعدادات قاعدة البيانات
========================================

الخادم: . (المحلي)
قاعدة البيانات: Terra
المستخدم: sa
كلمة المرور: Ret_ME@

قاعدة بيانات الإشعارات: Terra_Notifications_DB1

========================================
         📞 معرفات الإشعارات الحالية
========================================

- 1107000748 (مستخدم 1)
- 1206533289 (مستخدم 2)  
- -1002255521584 (جروب TERRABOT)

لإضافة معرفات جديدة: استخدم /id في البوت

========================================
           🛠️ استكشاف الأخطاء
========================================

إذا لم يعمل البوت:
1. تأكد من تشغيل SQL Server
2. تحقق من إعدادات قاعدة البيانات
3. تأكد من صحة توكن البوت
4. للجروبات: أضف البوت للجروب أولاً
5. تحقق من ملف terra_bot.log للأخطاء

========================================
               🔒 الأمان
========================================

✅ البوت يقرأ فقط من قاعدة البيانات
✅ لا يكتب أو يعدل أي بيانات
✅ آمن للاستخدام في بيئة الإنتاج
✅ يتجاهل البيانات التجريبية
✅ يحفظ سجل العمليات في terra_bot.log

========================================
             💡 ملاحظات مهمة
========================================

- يحتاج اتصال بالإنترنت للعمل
- يعمل في الخلفية بعد التشغيل
- يمكن إيقافه من نافذة الكونسول (Ctrl+C)
- يحفظ سجل العمليات في ملف terra_bot.log
- يستخدم المعرفات الفريدة لمنع التكرار
- يراعي الفروع المختلفة

========================================
     للدعم الفني: اتصل بفريق التطوير
     © 2025 Terra Company - جميع الحقوق محفوظة
========================================
