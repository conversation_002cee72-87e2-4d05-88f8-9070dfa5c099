#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار البوت المصحح
"""

import asyncio
from datetime import datetime
from telegram import Bot

# إعدادات
BOT_TOKEN = "**********************************************"
USER_CHAT_IDS = [
    "1107000748",
    "1206533289"
]

async def test_fixed_bot():
    """اختبار البوت المصحح"""
    print("🧪 اختبار البوت المصحح")
    print("=" * 50)
    
    try:
        bot = Bot(token=BOT_TOKEN)
        
        # اختبار البوت
        bot_info = await bot.get_me()
        print(f"✅ البوت: {bot_info.first_name} (@{bot_info.username})")
        
        # اختبار إرسال للمستخدمين
        test_message = f"""🔔 **اختبار البوت المصحح - Terra Bot**

✅ **البوت يعمل بنجاح!**

🤖 **اسم البوت:** {bot_info.first_name}
📅 **التاريخ:** {datetime.now().strftime('%Y-%m-%d %H:%M')}
🔧 **الإصدار:** مصحح ومبسط

💡 **الميزات:**
- ✅ نظام إشعارات تلقائية
- ✅ مراقبة العملاء الجدد
- ✅ أوامر /start و /test و /id

⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"""
        
        success_count = 0
        for chat_id in USER_CHAT_IDS:
            try:
                await bot.send_message(
                    chat_id=chat_id,
                    text=test_message,
                    parse_mode='Markdown'
                )
                print(f"✅ تم إرسال اختبار إلى {chat_id}")
                success_count += 1
                
            except Exception as e:
                print(f"❌ فشل إرسال إلى {chat_id}: {str(e)}")
        
        print(f"\n📊 النتيجة: {success_count}/{len(USER_CHAT_IDS)} إشعارات نجحت")
        
        if success_count == len(USER_CHAT_IDS):
            print("🎉 البوت المصحح يعمل بنجاح!")
        
    except Exception as e:
        print(f"❌ خطأ في الاختبار: {str(e)}")

if __name__ == "__main__":
    asyncio.run(test_fixed_bot())
