#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مراقب قاعدة البيانات Terra
يرسل إشعارات تلقائية عند إضافة بيانات جديدة
"""

import pyodbc
import asyncio
import logging
import time
from datetime import datetime, timedelta
from telegram import Bot

# إعداد التسجيل
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO,
    handlers=[
        logging.FileHandler('database_monitor.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# إعدادات البوت
BOT_TOKEN = "7664606990:AAFbBWGShtg00af-qkbCm9VRpvqq7M--bU0"

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

# قائمة المستخدمين المخولين لتلقي الإشعارات
NOTIFICATION_USERS = []  # سيتم تحديثها من ملف منفصل

# متغيرات لتتبع آخر البيانات المضافة
last_customer_id = None
last_preview_id = None
last_meeting_id = None
last_design_id = None
last_contract_id = None

def connect_to_db():
    """الاتصال بقاعدة البيانات"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['user']};PWD={DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        return conn
    except Exception as e:
        logger.error(f"خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def load_notification_users():
    """تحميل قائمة المستخدمين من ملف"""
    global NOTIFICATION_USERS
    try:
        with open('notification_users.txt', 'r', encoding='utf-8') as f:
            NOTIFICATION_USERS = [int(line.strip()) for line in f if line.strip().isdigit()]
        logger.info(f"تم تحميل {len(NOTIFICATION_USERS)} مستخدم للإشعارات")
    except FileNotFoundError:
        logger.info("ملف المستخدمين غير موجود، سيتم إنشاؤه تلقائياً")
        NOTIFICATION_USERS = []
    except Exception as e:
        logger.error(f"خطأ في تحميل قائمة المستخدمين: {str(e)}")
        NOTIFICATION_USERS = []

def save_notification_users():
    """حفظ قائمة المستخدمين في ملف"""
    try:
        with open('notification_users.txt', 'w', encoding='utf-8') as f:
            for user_id in NOTIFICATION_USERS:
                f.write(f"{user_id}\n")
        logger.info(f"تم حفظ {len(NOTIFICATION_USERS)} مستخدم")
    except Exception as e:
        logger.error(f"خطأ في حفظ قائمة المستخدمين: {str(e)}")

def add_notification_user(user_id):
    """إضافة مستخدم جديد لقائمة الإشعارات"""
    global NOTIFICATION_USERS
    if user_id not in NOTIFICATION_USERS:
        NOTIFICATION_USERS.append(user_id)
        save_notification_users()
        logger.info(f"تم إضافة مستخدم جديد: {user_id}")

async def send_notification(message):
    """إرسال إشعار لجميع المستخدمين المخولين"""
    if not NOTIFICATION_USERS:
        logger.info("لا توجد مستخدمين لإرسال الإشعارات إليهم")
        return
    
    bot = Bot(token=BOT_TOKEN)
    notification_text = f"🔔 **إشعار تلقائي من Terra Bot**\n\n{message}\n\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
    
    for user_id in NOTIFICATION_USERS:
        try:
            await bot.send_message(
                chat_id=user_id,
                text=notification_text,
                parse_mode='Markdown'
            )
            logger.info(f"تم إرسال إشعار للمستخدم: {user_id}")
        except Exception as e:
            logger.error(f"فشل إرسال إشعار للمستخدم {user_id}: {str(e)}")

def check_new_customers():
    """فحص العملاء الجدد"""
    global last_customer_id
    
    try:
        conn = connect_to_db()
        if not conn:
            return None
        
        cursor = conn.cursor()
        
        # الحصول على آخر عميل مضاف
        cursor.execute("SELECT TOP 1 CustomerCode, CustomerName, PrimaryPhone, BranchID, CreatedDate FROM Acc_Customers ORDER BY CreatedDate DESC")
        latest_customer = cursor.fetchone()
        
        if latest_customer and latest_customer[0] != last_customer_id:
            last_customer_id = latest_customer[0]
            
            branch_name = "فرع التجمع" if latest_customer[3] == 1 else "فرع مدينة نصر" if latest_customer[3] == 2 else "غير محدد"
            
            message = f"""👤 **عميل جديد تم إضافته**

🔢 **الكود:** {latest_customer[0]}
👤 **الاسم:** {latest_customer[1] or 'غير محدد'}
📱 **الهاتف:** {latest_customer[2] or 'غير محدد'}
🏢 **الفرع:** {branch_name}
📅 **تاريخ الإضافة:** {latest_customer[4].strftime('%Y-%m-%d %H:%M') if latest_customer[4] else 'غير محدد'}"""
            
            conn.close()
            return message
        
        conn.close()
        return None
        
    except Exception as e:
        logger.error(f"خطأ في فحص العملاء الجدد: {str(e)}")
        return None

def check_new_previews():
    """فحص المعاينات الجديدة"""
    global last_preview_id
    
    try:
        conn = connect_to_db()
        if not conn:
            return None
        
        cursor = conn.cursor()
        
        # الحصول على آخر معاينة مضافة
        cursor.execute("""
            SELECT TOP 1 p.PreviewCode, c.CustomerName, c.PrimaryPhone, c.BranchID, p.PreviewDate
            FROM Sys_Previews p
            INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
            ORDER BY p.PreviewDate DESC
        """)
        latest_preview = cursor.fetchone()
        
        if latest_preview and latest_preview[0] != last_preview_id:
            last_preview_id = latest_preview[0]
            
            branch_name = "فرع التجمع" if latest_preview[3] == 1 else "فرع مدينة نصر" if latest_preview[3] == 2 else "غير محدد"
            
            message = f"""👁️ **معاينة جديدة تم إضافتها**

🔢 **كود المعاينة:** {latest_preview[0]}
👤 **العميل:** {latest_preview[1] or 'غير محدد'}
📱 **الهاتف:** {latest_preview[2] or 'غير محدد'}
🏢 **الفرع:** {branch_name}
📅 **تاريخ المعاينة:** {latest_preview[4].strftime('%Y-%m-%d %H:%M') if latest_preview[4] else 'غير محدد'}"""
            
            conn.close()
            return message
        
        conn.close()
        return None
        
    except Exception as e:
        logger.error(f"خطأ في فحص المعاينات الجديدة: {str(e)}")
        return None

def check_new_meetings():
    """فحص الاجتماعات الجديدة"""
    global last_meeting_id
    
    try:
        conn = connect_to_db()
        if not conn:
            return None
        
        cursor = conn.cursor()
        
        # الحصول على آخر اجتماع مضاف
        cursor.execute("""
            SELECT TOP 1 m.MeetingCode, c.CustomerName, c.PrimaryPhone, c.BranchID, m.MeetingDate
            FROM Sys_Meetings m
            INNER JOIN Sys_Previews p ON m.PreviewCode = p.PreviewCode
            INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
            ORDER BY m.MeetingDate DESC
        """)
        latest_meeting = cursor.fetchone()
        
        if latest_meeting and latest_meeting[0] != last_meeting_id:
            last_meeting_id = latest_meeting[0]
            
            branch_name = "فرع التجمع" if latest_meeting[3] == 1 else "فرع مدينة نصر" if latest_meeting[3] == 2 else "غير محدد"
            
            message = f"""🤝 **اجتماع جديد تم إضافته**

🔢 **كود الاجتماع:** {latest_meeting[0]}
👤 **العميل:** {latest_meeting[1] or 'غير محدد'}
📱 **الهاتف:** {latest_meeting[2] or 'غير محدد'}
🏢 **الفرع:** {branch_name}
📅 **تاريخ الاجتماع:** {latest_meeting[4].strftime('%Y-%m-%d %H:%M') if latest_meeting[4] else 'غير محدد'}"""
            
            conn.close()
            return message
        
        conn.close()
        return None
        
    except Exception as e:
        logger.error(f"خطأ في فحص الاجتماعات الجديدة: {str(e)}")
        return None

def check_new_designs():
    """فحص التصميمات الجديدة"""
    global last_design_id
    
    try:
        conn = connect_to_db()
        if not conn:
            return None
        
        cursor = conn.cursor()
        
        # الحصول على آخر تصميم مضاف
        cursor.execute("""
            SELECT TOP 1 d.DesignCode, c.CustomerName, c.PrimaryPhone, c.BranchID, d.DesignDate
            FROM Sys_Designs d
            INNER JOIN Sys_Meetings m ON d.MeetingCode = m.MeetingCode
            INNER JOIN Sys_Previews p ON m.PreviewCode = p.PreviewCode
            INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
            ORDER BY d.DesignDate DESC
        """)
        latest_design = cursor.fetchone()
        
        if latest_design and latest_design[0] != last_design_id:
            last_design_id = latest_design[0]
            
            branch_name = "فرع التجمع" if latest_design[3] == 1 else "فرع مدينة نصر" if latest_design[3] == 2 else "غير محدد"
            
            message = f"""🎨 **تصميم جديد تم إضافته**

🔢 **كود التصميم:** {latest_design[0]}
👤 **العميل:** {latest_design[1] or 'غير محدد'}
📱 **الهاتف:** {latest_design[2] or 'غير محدد'}
🏢 **الفرع:** {branch_name}
📅 **تاريخ التصميم:** {latest_design[4].strftime('%Y-%m-%d %H:%M') if latest_design[4] else 'غير محدد'}"""
            
            conn.close()
            return message
        
        conn.close()
        return None
        
    except Exception as e:
        logger.error(f"خطأ في فحص التصميمات الجديدة: {str(e)}")
        return None

def check_new_contracts():
    """فحص العقود الجديدة"""
    global last_contract_id
    
    try:
        conn = connect_to_db()
        if not conn:
            return None
        
        cursor = conn.cursor()
        
        # الحصول على آخر عقد مضاف
        cursor.execute("""
            SELECT TOP 1 ct.ContractCode, c.CustomerName, c.PrimaryPhone, c.BranchID, ct.ContractDate
            FROM Acc_Contracts ct
            INNER JOIN Sys_Designs d ON ct.DesignCode = d.DesignCode
            INNER JOIN Sys_Meetings m ON d.MeetingCode = m.MeetingCode
            INNER JOIN Sys_Previews p ON m.PreviewCode = p.PreviewCode
            INNER JOIN Acc_Customers c ON p.CustomerCode = c.CustomerCode
            ORDER BY ct.ContractDate DESC
        """)
        latest_contract = cursor.fetchone()
        
        if latest_contract and latest_contract[0] != last_contract_id:
            last_contract_id = latest_contract[0]
            
            branch_name = "فرع التجمع" if latest_contract[3] == 1 else "فرع مدينة نصر" if latest_contract[3] == 2 else "غير محدد"
            
            message = f"""📄 **عقد جديد تم إضافته**

🔢 **كود العقد:** {latest_contract[0]}
👤 **العميل:** {latest_contract[1] or 'غير محدد'}
📱 **الهاتف:** {latest_contract[2] or 'غير محدد'}
🏢 **الفرع:** {branch_name}
📅 **تاريخ العقد:** {latest_contract[4].strftime('%Y-%m-%d %H:%M') if latest_contract[4] else 'غير محدد'}"""
            
            conn.close()
            return message
        
        conn.close()
        return None
        
    except Exception as e:
        logger.error(f"خطأ في فحص العقود الجديدة: {str(e)}")
        return None

def initialize_last_ids():
    """تهيئة آخر المعرفات من قاعدة البيانات"""
    global last_customer_id, last_preview_id, last_meeting_id, last_design_id, last_contract_id

    try:
        conn = connect_to_db()
        if not conn:
            logger.error("فشل الاتصال بقاعدة البيانات للتهيئة")
            return

        cursor = conn.cursor()

        # آخر عميل
        try:
            cursor.execute("SELECT TOP 1 CustomerCode FROM Acc_Customers ORDER BY CreatedDate DESC")
            result = cursor.fetchone()
            last_customer_id = result[0] if result else None
        except:
            last_customer_id = None

        # آخر معاينة
        try:
            cursor.execute("SELECT TOP 1 PreviewCode FROM Sys_Previews ORDER BY PreviewDate DESC")
            result = cursor.fetchone()
            last_preview_id = result[0] if result else None
        except:
            last_preview_id = None

        # آخر اجتماع
        try:
            cursor.execute("SELECT TOP 1 MeetingCode FROM Sys_Meetings ORDER BY MeetingDate DESC")
            result = cursor.fetchone()
            last_meeting_id = result[0] if result else None
        except:
            last_meeting_id = None

        # آخر تصميم
        try:
            cursor.execute("SELECT TOP 1 DesignCode FROM Sys_Designs ORDER BY DesignDate DESC")
            result = cursor.fetchone()
            last_design_id = result[0] if result else None
        except:
            last_design_id = None

        # آخر عقد
        try:
            cursor.execute("SELECT TOP 1 ContractCode FROM Acc_Contracts ORDER BY ContractDate DESC")
            result = cursor.fetchone()
            last_contract_id = result[0] if result else None
        except:
            last_contract_id = None

        conn.close()

        logger.info(f"تم تهيئة المعرفات: عميل={last_customer_id}, معاينة={last_preview_id}, اجتماع={last_meeting_id}, تصميم={last_design_id}, عقد={last_contract_id}")

    except Exception as e:
        logger.error(f"خطأ في تهيئة المعرفات: {str(e)}")

async def monitor_database():
    """مراقبة قاعدة البيانات للتغييرات الجديدة"""
    logger.info("🔍 بدء مراقبة قاعدة البيانات...")

    while True:
        try:
            # فحص العملاء الجدد
            new_customer = check_new_customers()
            if new_customer:
                await send_notification(new_customer)

            # فحص المعاينات الجديدة
            new_preview = check_new_previews()
            if new_preview:
                await send_notification(new_preview)

            # فحص الاجتماعات الجديدة
            new_meeting = check_new_meetings()
            if new_meeting:
                await send_notification(new_meeting)

            # فحص التصميمات الجديدة
            new_design = check_new_designs()
            if new_design:
                await send_notification(new_design)

            # فحص العقود الجديدة
            new_contract = check_new_contracts()
            if new_contract:
                await send_notification(new_contract)

            # انتظار 30 ثانية قبل الفحص التالي
            await asyncio.sleep(30)

        except Exception as e:
            logger.error(f"خطأ في مراقبة قاعدة البيانات: {str(e)}")
            await asyncio.sleep(60)  # انتظار أطول في حالة الخطأ

async def main():
    """الدالة الرئيسية لمراقب قاعدة البيانات"""
    try:
        logger.info("🚀 بدء تشغيل مراقب قاعدة البيانات Terra...")

        # تحميل قائمة المستخدمين
        load_notification_users()

        # تهيئة آخر المعرفات
        initialize_last_ids()

        # اختبار الاتصال بقاعدة البيانات
        conn = connect_to_db()
        if conn:
            logger.info("✅ تم الاتصال بقاعدة البيانات بنجاح")
            conn.close()
        else:
            logger.error("❌ فشل الاتصال بقاعدة البيانات")
            return

        # بدء المراقبة
        await monitor_database()

    except KeyboardInterrupt:
        logger.info("🛑 تم إيقاف المراقب بواسطة المستخدم")
    except Exception as e:
        logger.error(f"❌ خطأ في تشغيل المراقب: {str(e)}")
        raise

if __name__ == "__main__":
    asyncio.run(main())
