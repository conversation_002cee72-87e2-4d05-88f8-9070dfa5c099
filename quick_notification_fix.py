#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
حل سريع لإرسال إشعارات العملاء الجدد
"""

import asyncio
import pyodbc
from datetime import datetime
from telegram import Bot

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

# إعدادات البوت الصحيحة
BOT_TOKEN = "7556046481:AAEMttgLYzrMu1q2ogRlgsV8mgscqZKbbcY"
NOTIFICATION_CHAT_IDS = [
    "1107000748",
    "1206533289"
]

def connect_to_db():
    """الاتصال بقاعدة البيانات"""
    try:
        conn_str = f"DRIVER={{SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['user']};PWD={DB_CONFIG['password']}"
        conn = pyodbc.connect(conn_str)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة البيانات: {str(e)}")
        return None

def get_latest_customers(count=5):
    """الحصول على آخر العملاء المضافين"""
    try:
        conn = connect_to_db()
        if not conn:
            return []
        
        cursor = conn.cursor()
        
        # الحصول على آخر العملاء
        cursor.execute(f"""
            SELECT TOP {count} CustomerCode, NameAr, MainPhoneNo, BranchId, AddDate 
            FROM Acc_Customers 
            WHERE IsDeleted = 0 
            ORDER BY AddDate DESC
        """)
        
        customers = cursor.fetchall()
        conn.close()
        
        return customers
        
    except Exception as e:
        print(f"❌ خطأ في الحصول على العملاء: {str(e)}")
        return []

async def send_customer_notifications():
    """إرسال إشعارات العملاء الجدد"""
    print("🔍 البحث عن العملاء الجدد...")
    
    customers = get_latest_customers(3)
    
    if not customers:
        print("❌ لم يتم العثور على عملاء")
        return
    
    print(f"✅ تم العثور على {len(customers)} عملاء")
    
    bot = Bot(token=BOT_TOKEN)
    
    for customer in customers:
        try:
            # تحضير رسالة الإشعار
            branch_name = "فرع التجمع" if customer[3] == 3 else "فرع مدينة نصر" if customer[3] == 2 else "غير محدد"
            
            message = f"""👤 **عميل جديد تم إضافته**

🔢 **الكود:** {customer[0]}
👤 **الاسم:** {customer[1] or 'غير محدد'}
📱 **الهاتف:** {customer[2] or 'غير محدد'}
🏢 **الفرع:** {branch_name}
📅 **تاريخ الإضافة:** {customer[4].strftime('%Y-%m-%d %H:%M') if customer[4] else 'غير محدد'}"""
            
            notification_text = f"🔔 **إشعار تلقائي من Terra Bot**\n\n{message}\n\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            # إرسال للمستخدمين
            for chat_id in NOTIFICATION_CHAT_IDS:
                try:
                    await bot.send_message(
                        chat_id=chat_id,
                        text=notification_text,
                        parse_mode='Markdown'
                    )
                    print(f"✅ تم إرسال إشعار العميل {customer[0]} إلى {chat_id}")
                    
                except Exception as e:
                    print(f"❌ فشل إرسال إشعار إلى {chat_id}: {str(e)}")
            
            # انتظار قصير بين الرسائل
            await asyncio.sleep(1)
            
        except Exception as e:
            print(f"❌ خطأ في معالجة العميل {customer[0]}: {str(e)}")

async def start_monitoring():
    """بدء مراقبة مستمرة"""
    print("🔍 بدء المراقبة المستمرة...")
    
    last_customer_id = None
    
    # الحصول على آخر عميل حالياً
    customers = get_latest_customers(1)
    if customers:
        last_customer_id = customers[0][0]
        print(f"📊 آخر عميل حالياً: {last_customer_id}")
    
    cycle = 0
    while True:
        try:
            cycle += 1
            print(f"\n🔄 دورة مراقبة #{cycle} - {datetime.now().strftime('%H:%M:%S')}")
            
            # فحص العملاء الجدد
            current_customers = get_latest_customers(1)
            
            if current_customers:
                current_customer_id = current_customers[0][0]
                
                if current_customer_id != last_customer_id:
                    print(f"🆕 عميل جديد اكتُشف! الكود: {current_customer_id}")
                    
                    # إرسال إشعار للعميل الجديد
                    customer = current_customers[0]
                    branch_name = "فرع التجمع" if customer[3] == 3 else "فرع مدينة نصر" if customer[3] == 2 else "غير محدد"
                    
                    message = f"""👤 **عميل جديد تم إضافته**

🔢 **الكود:** {customer[0]}
👤 **الاسم:** {customer[1] or 'غير محدد'}
📱 **الهاتف:** {customer[2] or 'غير محدد'}
🏢 **الفرع:** {branch_name}
📅 **تاريخ الإضافة:** {customer[4].strftime('%Y-%m-%d %H:%M') if customer[4] else 'غير محدد'}"""
                    
                    notification_text = f"🔔 **إشعار تلقائي من Terra Bot**\n\n{message}\n\n⏰ {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                    
                    bot = Bot(token=BOT_TOKEN)
                    
                    # إرسال للمستخدمين
                    for chat_id in NOTIFICATION_CHAT_IDS:
                        try:
                            await bot.send_message(
                                chat_id=chat_id,
                                text=notification_text,
                                parse_mode='Markdown'
                            )
                            print(f"✅ تم إرسال إشعار العميل {customer[0]} إلى {chat_id}")
                            
                        except Exception as e:
                            print(f"❌ فشل إرسال إشعار إلى {chat_id}: {str(e)}")
                    
                    # تحديث آخر عميل
                    last_customer_id = current_customer_id
                else:
                    print(f"📊 لا توجد عملاء جدد (آخر عميل: {current_customer_id})")
            
            # انتظار 20 ثانية
            await asyncio.sleep(20)
            
        except Exception as e:
            print(f"❌ خطأ في المراقبة: {str(e)}")
            await asyncio.sleep(30)

async def main():
    """الدالة الرئيسية"""
    print("🚀 حل سريع لإشعارات Terra Bot")
    print("=" * 50)
    
    print("1️⃣ إرسال إشعارات للعملاء الحاليين")
    print("2️⃣ بدء المراقبة المستمرة")
    
    choice = input("\nاختر (1 أو 2): ").strip()
    
    if choice == "1":
        await send_customer_notifications()
    elif choice == "2":
        await start_monitoring()
    else:
        print("❌ اختيار غير صحيح")

if __name__ == "__main__":
    asyncio.run(main())
