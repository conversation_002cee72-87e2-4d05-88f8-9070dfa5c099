#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار الاتصال بقاعدة البيانات
"""

import pyodbc

# إعدادات قاعدة البيانات
DB_CONFIG = {
    'server': '.',
    'database': 'Terra',
    'user': 'sa',
    'password': 'Ret_ME@'
}

def test_connection():
    """اختبار الاتصال بقاعدة البيانات"""
    try:
        print("🔄 محاولة الاتصال بقاعدة البيانات...")
        print(f"الخادم: {DB_CONFIG['server']}")
        print(f"قاعدة البيانات: {DB_CONFIG['database']}")
        print(f"المستخدم: {DB_CONFIG['user']}")
        
        conn_str = f"DRIVER={{SQL Server}};SERVER={DB_CONFIG['server']};DATABASE={DB_CONFIG['database']};UID={DB_CONFIG['user']};PWD={DB_CONFIG['password']}"
        print(f"نص الاتصال: {conn_str}")
        
        conn = pyodbc.connect(conn_str)
        print("✅ تم الاتصال بقاعدة البيانات بنجاح!")
        
        cursor = conn.cursor()
        cursor.execute("SELECT @@VERSION")
        version = cursor.fetchone()
        print(f"إصدار SQL Server: {version[0]}")
        
        cursor.execute("SELECT DB_NAME()")
        db_name = cursor.fetchone()
        print(f"اسم قاعدة البيانات الحالية: {db_name[0]}")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في الاتصال: {e}")
        return False

def test_drivers():
    """اختبار تعريفات ODBC المتاحة"""
    try:
        print("\n🔍 تعريفات ODBC المتاحة:")
        drivers = pyodbc.drivers()
        for driver in drivers:
            print(f"  • {driver}")
        return drivers
    except Exception as e:
        print(f"❌ خطأ في قراءة التعريفات: {e}")
        return []

if __name__ == "__main__":
    print("=" * 50)
    print("🧪 اختبار الاتصال بقاعدة البيانات Terra")
    print("=" * 50)
    
    # اختبار التعريفات المتاحة
    drivers = test_drivers()
    
    # اختبار الاتصال
    success = test_connection()
    
    print("=" * 50)
    if success:
        print("🎉 الاختبار نجح! يمكن تشغيل البوت الآن.")
    else:
        print("❌ الاختبار فشل. تحقق من إعدادات SQL Server.")
        print("\n💡 نصائح لحل المشكلة:")
        print("1. تأكد من تشغيل SQL Server")
        print("2. تأكد من تشغيل SQL Server Browser")
        print("3. تأكد من صحة كلمة مرور المستخدم 'sa'")
        print("4. تأكد من وجود قاعدة البيانات 'Terra'")
    print("=" * 50)
