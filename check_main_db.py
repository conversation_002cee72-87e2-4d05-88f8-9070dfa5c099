#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pyodbc
from datetime import datetime

def check_main_db():
    """فحص البيانات في قاعدة البيانات الأصلية"""
    try:
        # الاتصال بقاعدة البيانات الأصلية
        conn_str = "DRIVER={SQL Server};SERVER=.;DATABASE=Terra;UID=sa;PWD=*******"
        conn = pyodbc.connect(conn_str)
        
        cursor = conn.cursor()
        
        print("🔍 فحص معاينة 273 في قاعدة البيانات الأصلية...")
        
        # فحص معاينة 273
        cursor.execute("""
            SELECT
                p.PreviewId,
                p.PreviewCode,
                p.PreviewName,
                p.BranchId,
                b.<PERSON><PERSON>,
                p.AddDate,
                p.AddedBy
            FROM Preview p
            LEFT JOIN Branch b ON p.BranchId = b.BranchId
            WHERE p.PreviewCode = 273
            ORDER BY p.AddDate DESC
        """)
        
        preview_results = cursor.fetchall()
        if preview_results:
            print(f"📊 معاينة 273:")
            for row in preview_results:
                print(f"   معرف: {row[0]} - كود: {row[1]} - اسم: {row[2]}")
                print(f"   فرع: {row[3]} ({row[4]}) - تاريخ: {row[5]} - أضيف بواسطة: {row[6]}")
        else:
            print("❌ معاينة 273 غير موجودة في قاعدة البيانات الأصلية")
        
        print("\n🔍 فحص اجتماع 65 في قاعدة البيانات الأصلية...")
        
        # فحص اجتماع 65
        cursor.execute("""
            SELECT
                m.MeetingId,
                m.MeetingCode,
                m.MeetingName,
                m.BranchId,
                b.BranchName,
                m.AddDate,
                m.AddedBy
            FROM Meeting m
            LEFT JOIN Branch b ON m.BranchId = b.BranchId
            WHERE m.MeetingCode = 65
            ORDER BY m.AddDate DESC
        """)
        
        meeting_results = cursor.fetchall()
        if meeting_results:
            print(f"📊 اجتماع 65:")
            for row in meeting_results:
                print(f"   معرف: {row[0]} - كود: {row[1]} - اسم: {row[2]}")
                print(f"   فرع: {row[3]} ({row[4]}) - تاريخ: {row[5]} - أضيف بواسطة: {row[6]}")
        else:
            print("❌ اجتماع 65 غير موجود في قاعدة البيانات الأصلية")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ: {e}")

if __name__ == "__main__":
    check_main_db()
