@echo off
title Terra Bot - Build EXE

echo Installing PyInstaller...
pip install pyinstaller

echo Installing required libraries...
pip install python-telegram-bot pyodbc

echo Cleaning old files...
if exist "dist" rmdir /s /q "dist"
if exist "build" rmdir /s /q "build"

echo Building EXE file...
pyinstaller --onefile --noconsole --name "TerraBot_Enhanced" enhanced_notifications.py

echo Done!
if exist "dist\TerraBot_Enhanced.exe" (
    echo SUCCESS: EXE file created in dist folder
    explorer "dist"
) else (
    echo ERROR: Failed to create EXE file
)

pause
