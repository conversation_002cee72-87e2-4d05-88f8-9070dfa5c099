#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء جدول التقارير المجدولة في قاعدة بيانات Terra_Notifications_DB1
"""

import pyodbc
from datetime import datetime

# إعدادات قاعدة البيانات
NOTIFICATIONS_DB_CONFIG = {
    'server': 'DESKTOP-KCJG4NG\\SQLEXPRESS',
    'database': 'Terra_Notifications_DB1',
    'trusted_connection': 'yes',
    'driver': '{ODBC Driver 17 for SQL Server}'
}

def connect_to_notifications_db():
    """الاتصال بقاعدة بيانات الإشعارات"""
    try:
        connection_string = (
            f"DRIVER={NOTIFICATIONS_DB_CONFIG['driver']};"
            f"SERVER={NOTIFICATIONS_DB_CONFIG['server']};"
            f"DATABASE={NOTIFICATIONS_DB_CONFIG['database']};"
            f"Trusted_Connection={NOTIFICATIONS_DB_CONFIG['trusted_connection']};"
        )
        conn = pyodbc.connect(connection_string)
        return conn
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة بيانات الإشعارات: {str(e)}")
        return None

def create_scheduled_reports_table():
    """إنشاء جدول التقارير المجدولة في قاعدة بيانات الإشعارات"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            print("❌ فشل الاتصال بقاعدة البيانات")
            return False
        
        cursor = conn.cursor()
        
        # إنشاء جدول التقارير المجدولة
        create_table_query = """
        IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Scheduled_Reports' AND xtype='U')
        CREATE TABLE Scheduled_Reports (
            ReportId INT IDENTITY(1,1) PRIMARY KEY,
            ReportName NVARCHAR(100) NOT NULL,
            IsEnabled BIT DEFAULT 1,
            DaysCount INT DEFAULT 3,
            ScheduleType NVARCHAR(20) DEFAULT 'daily', -- daily, weekly, monthly, custom
            ScheduleHour INT DEFAULT 9,
            ScheduleMinute INT DEFAULT 0,
            ScheduleWeekday INT DEFAULT NULL, -- 0=Monday, 6=Sunday (for weekly)
            ScheduleDay INT DEFAULT NULL, -- day of month (for monthly)
            BranchIds NVARCHAR(500) DEFAULT NULL, -- comma separated branch IDs, NULL = all branches
            LastSentDate DATETIME DEFAULT NULL,
            CreatedDate DATETIME DEFAULT GETDATE(),
            UpdatedDate DATETIME DEFAULT GETDATE()
        )
        """
        
        cursor.execute(create_table_query)
        conn.commit()
        print("✅ تم إنشاء جدول Scheduled_Reports بنجاح")
        
        # التحقق من وجود التقارير الافتراضية
        check_query = "SELECT COUNT(*) FROM Scheduled_Reports"
        cursor.execute(check_query)
        count = cursor.fetchone()[0]
        
        if count == 0:
            # إدراج التقارير الافتراضية
            default_reports = [
                ("التقرير اليومي", 1, 1, "daily", 9, 0, None, None, None),
                ("التقرير الأسبوعي", 1, 7, "weekly", 10, 0, 6, None, None),  # 6 = Sunday
                ("التقرير الشهري", 1, 30, "monthly", 11, 0, None, 1, None)   # 1st day of month
            ]
            
            insert_query = """
            INSERT INTO Scheduled_Reports 
            (ReportName, IsEnabled, DaysCount, ScheduleType, ScheduleHour, ScheduleMinute, ScheduleWeekday, ScheduleDay, BranchIds)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            
            for report in default_reports:
                cursor.execute(insert_query, report)
            
            conn.commit()
            print("✅ تم إدراج التقارير الافتراضية بنجاح")
        else:
            print(f"ℹ️ يوجد {count} تقرير مجدول في الجدول")
        
        # عرض التقارير الموجودة
        cursor.execute("SELECT ReportId, ReportName, ScheduleType, ScheduleHour, ScheduleMinute, IsEnabled FROM Scheduled_Reports")
        reports = cursor.fetchall()
        
        print("\n📊 التقارير المجدولة:")
        print("-" * 50)
        for report in reports:
            status = "مفعل" if report[5] else "معطل"
            print(f"ID: {report[0]} | {report[1]} | {report[2]} | {report[3]:02d}:{report[4]:02d} | {status}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ خطأ في إنشاء جدول التقارير المجدولة: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 بدء إنشاء جدول التقارير المجدولة")
    print("=" * 50)
    
    success = create_scheduled_reports_table()
    
    if success:
        print("\n✅ تم إنشاء النظام بنجاح!")
        print("📍 الجدول: Scheduled_Reports")
        print("📍 قاعدة البيانات: Terra_Notifications_DB1")
    else:
        print("\n❌ فشل في إنشاء النظام")
    
    print("\n" + "=" * 50)
    input("اضغط Enter للخروج...")
