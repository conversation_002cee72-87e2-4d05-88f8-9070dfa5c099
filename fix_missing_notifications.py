#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pyodbc
from datetime import datetime

# إعدادات قاعدة بيانات الإشعارات
NOTIFICATIONS_DB_CONFIG = {
    'server': '.',
    'database': 'Terra_Notifications_DB1',
    'user': 'sa',
    'password': 'Ret_ME@'
}

def connect_to_notifications_db():
    """الاتصال بقاعدة بيانات الإشعارات"""
    try:
        conn_str = (
            f"DRIVER={{ODBC Driver 17 for SQL Server}};"
            f"SERVER={NOTIFICATIONS_DB_CONFIG['server']};"
            f"DATABASE={NOTIFICATIONS_DB_CONFIG['database']};"
            f"UID={NOTIFICATIONS_DB_CONFIG['user']};"
            f"PWD={NOTIFICATIONS_DB_CONFIG['password']};"
        )
        return pyodbc.connect(conn_str)
    except Exception as e:
        print(f"❌ خطأ في الاتصال بقاعدة بيانات الإشعارات: {str(e)}")
        return None

def fix_notifications():
    """إصلاح الإشعارات المفقودة"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return
        
        cursor = conn.cursor()
        
        print("🔧 إصلاح الإشعارات المفقودة...")
        
        # 1. حفظ معاينة 273 (PreviewId = 1405)
        print("\n💾 حفظ معاينة 273...")
        try:
            cursor.execute("""
                INSERT INTO Notifications_Log
                (DataType, RecordId, RecordName, BranchId, BranchName, RecordUniqueId, AddDate, AddedBy, NotificationDate, NotificationStatus, IsActive)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, GETDATE(), N'تم الإرسال', 1)
            """, (
                'معاينة', 
                '273', 
                'معاينة للعميل مادونا نبيل', 
                2, 
                'فرع مدينة نصر', 
                1405,  # PreviewId الصحيح
                datetime(2025, 6, 30, 7, 50, 35),
                'admin'
            ))
            print("✅ تم حفظ معاينة 273 بنجاح")
        except Exception as e:
            if "UNIQUE KEY constraint" in str(e) or "2627" in str(e):
                print("⚠️ معاينة 273 موجودة بالفعل")
            else:
                print(f"❌ خطأ في حفظ معاينة 273: {e}")
        
        # 2. حفظ اجتماع 65 (MeetingId = 150)
        print("\n💾 حفظ اجتماع 65...")
        try:
            cursor.execute("""
                INSERT INTO Notifications_Log
                (DataType, RecordId, RecordName, BranchId, BranchName, RecordUniqueId, AddDate, AddedBy, NotificationDate, NotificationStatus, IsActive)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, GETDATE(), N'تم الإرسال', 1)
            """, (
                'اجتماع', 
                '65', 
                'اجتماع مع العميل مادونا نبيل', 
                2, 
                'فرع مدينة نصر', 
                150,  # MeetingId الصحيح
                datetime(2025, 6, 30, 7, 50, 35),
                'admin'
            ))
            print("✅ تم حفظ اجتماع 65 بنجاح")
        except Exception as e:
            if "UNIQUE KEY constraint" in str(e) or "2627" in str(e):
                print("⚠️ اجتماع 65 موجود بالفعل")
            else:
                print(f"❌ خطأ في حفظ اجتماع 65: {e}")
        
        # 3. التحقق من وجود تضارب في المعرف 150
        print("\n🔍 فحص التضارب في المعرف 150...")
        cursor.execute("""
            SELECT LogId, DataType, RecordId, RecordName, RecordUniqueId
            FROM Notifications_Log 
            WHERE RecordUniqueId = 150
        """)
        
        results = cursor.fetchall()
        if results:
            print(f"📊 وُجد {len(results)} سجل بالمعرف 150:")
            for row in results:
                print(f"   LogId={row[0]}, {row[1]}, كود={row[2]}, اسم={row[3]}")
        
        conn.commit()
        conn.close()
        
        print("\n✅ تم الانتهاء من الإصلاح")
        
    except Exception as e:
        print(f"❌ خطأ في الإصلاح: {str(e)}")

def verify_fix():
    """التحقق من الإصلاح"""
    try:
        conn = connect_to_notifications_db()
        if not conn:
            return
        
        cursor = conn.cursor()
        
        print("\n🔍 التحقق من آخر المعرفات بعد الإصلاح...")
        
        # آخر معاينة
        cursor.execute("""
            SELECT TOP 1 RecordUniqueId, RecordId, NotificationDate
            FROM Notifications_Log 
            WHERE DataType = 'معاينة' AND IsActive = 1 AND RecordUniqueId IS NOT NULL
            ORDER BY RecordUniqueId DESC
        """)
        
        result = cursor.fetchone()
        if result:
            print(f"آخر معاينة: معرف فريد = {result[0]} (كود: {result[1]})")
        
        # آخر اجتماع
        cursor.execute("""
            SELECT TOP 1 RecordUniqueId, RecordId, NotificationDate
            FROM Notifications_Log 
            WHERE DataType = 'اجتماع' AND IsActive = 1 AND RecordUniqueId IS NOT NULL
            ORDER BY RecordUniqueId DESC
        """)
        
        result = cursor.fetchone()
        if result:
            print(f"آخر اجتماع: معرف فريد = {result[0]} (كود: {result[1]})")
        
        conn.close()
        
    except Exception as e:
        print(f"❌ خطأ في التحقق: {str(e)}")

def main():
    print("🚀 إصلاح الإشعارات المفقودة...")
    print("=" * 50)
    fix_notifications()
    verify_fix()
    print("\n🎯 انتهى الإصلاح")

if __name__ == "__main__":
    main()
